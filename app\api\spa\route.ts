import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const treatments = await prisma.spaTreatment.findMany({
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(treatments);
  } catch (error) {
    console.error("Error fetching spa treatments:", error);
    return NextResponse.json(
      { error: "Failed to fetch spa treatments" },
      { status: 500 }
    );
  }
}
