"use client";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { Testimonial } from "@/app/types/review";
import Image from "next/image";

interface DynamicTestimonialsProps {
  maxItems?: number;
  showTitle?: boolean;
  className?: string;
}

export default function DynamicTestimonials({ 
  maxItems = 6, 
  showTitle = true,
  className = "" 
}: DynamicTestimonialsProps) {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await fetch(`/api/testimonials?isFeatured=true&isPublished=true&limit=${maxItems}&sortBy=featured`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch testimonials');
        }
        
        const data = await response.json();
        setTestimonials(data.testimonials || []);
      } catch (err) {
        console.error('Error fetching testimonials:', err);
        setError('Failed to load testimonials');
        
        // Fallback to static testimonials
        setTestimonials([
          {
            id: "1",
            name: "Alem T.",
            content: "Amazing resort experience! Great hospitality and stunning views.",
            isFeatured: true,
            isApproved: true,
            isPublished: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            id: "2", 
            name: "Samuel M.",
            content: "The spa treatments were exceptional. Highly recommend Kuriftu!",
            isFeatured: true,
            isApproved: true,
            isPublished: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, [maxItems]);

  if (loading) {
    return (
      <section className={`py-16 bg-white ${className}`}>
        <div className="max-w-4xl mx-auto px-4 text-center">
          {showTitle && (
            <h2 className="text-3xl font-bold mb-10">What Our Guests Say</h2>
          )}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Array.from({ length: Math.min(maxItems, 3) }).map((_, i) => (
              <div key={i} className="p-6 bg-gray-50 rounded-xl shadow animate-pulse">
                <div className="h-4 bg-gray-300 rounded mb-3"></div>
                <div className="h-4 bg-gray-300 rounded mb-3"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error && testimonials.length === 0) {
    return (
      <section className={`py-16 bg-white ${className}`}>
        <div className="max-w-4xl mx-auto px-4 text-center">
          {showTitle && (
            <h2 className="text-3xl font-bold mb-10">What Our Guests Say</h2>
          )}
          <p className="text-gray-600">Unable to load testimonials at this time.</p>
        </div>
      </section>
    );
  }

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-6xl mx-auto px-4 text-center">
        {showTitle && (
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold mb-10"
          >
            What Our Guests Say
          </motion.h2>
        )}
        
        <div className={`grid gap-6 ${
          testimonials.length === 1 ? 'md:grid-cols-1 max-w-2xl mx-auto' :
          testimonials.length === 2 ? 'md:grid-cols-2 max-w-4xl mx-auto' :
          'md:grid-cols-2 lg:grid-cols-3'
        }`}>
          {testimonials.map((testimonial, i) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
              whileHover={{ scale: 1.02 }}
              className="p-6 bg-gray-50 rounded-xl shadow hover:shadow-lg transition-shadow"
            >
              {testimonial.title && (
                <h4 className="font-semibold text-gray-900 mb-3">
                  {testimonial.title}
                </h4>
              )}
              
              <p className="text-gray-700 italic mb-4 leading-relaxed">
                "{testimonial.content}"
              </p>
              
              <div className="flex items-center justify-center space-x-3">
                {testimonial.image ? (
                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    width={40}
                    height={40}
                    className="rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-medium">
                      {testimonial.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
                
                <div className="text-center">
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  {testimonial.location && (
                    <p className="text-sm text-gray-500">{testimonial.location}</p>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        
        {testimonials.length === 0 && !loading && (
          <p className="text-gray-600">No testimonials available at this time.</p>
        )}
      </div>
    </section>
  );
}
