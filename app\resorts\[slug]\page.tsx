import { prisma } from "@/lib/prisma";
import Image from "next/image";
import { notFound } from "next/navigation";
import ReviewsList from "@/app/components/ReviewsList";
import ReviewForm from "@/app/components/ReviewForm";
import BookingForm from "@/app/components/BookingForm";
import { Suspense } from "react";
import Link from "next/link";

// TypeScript interfaces
interface Resort {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
  createdAt: Date;
}

interface Room {
  id: string;
  name: string;
  type: string;
  description: string;
  price: number;
  image: string;
  images: string[];
  amenities: string[];
  capacity: number;
  size?: number;
  isActive: boolean;
}

// Room card component
function RoomCard({ room }: { room: Room }) {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
      <div className="relative h-48">
        <Image
          src={room.image}
          alt={room.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        <div className="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-sm font-semibold">
          ${room.price}/night
        </div>
      </div>
      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold text-gray-900">{room.name}</h3>
          <span className="text-sm text-gray-500">{room.type}</span>
        </div>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {room.description}
        </p>

        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <span>👥 {room.capacity} guests</span>
          {room.size && <span>📐 {room.size}m²</span>}
        </div>

        {room.amenities.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {room.amenities.slice(0, 3).map((amenity, index) => (
                <span
                  key={index}
                  className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                >
                  {amenity}
                </span>
              ))}
              {room.amenities.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{room.amenities.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        <Link
          href={`/booking?roomId=${room.id}`}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-center block"
        >
          Book Now
        </Link>
      </div>
    </div>
  );
}

// Rooms section component
async function RoomsSection({ resortId }: { resortId: string }) {
  try {
    const rooms = await prisma.room.findMany({
      where: {
        resortId,
        isActive: true,
      },
      orderBy: { price: "asc" },
    });

    if (rooms.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-4">🏨</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No Rooms Available
          </h3>
          <p className="text-gray-600">
            Please check back later or contact us for availability.
          </p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {rooms.map((room) => (
          <RoomCard key={room.id} room={room} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching rooms:", error);
    return (
      <div className="text-center py-8">
        <div className="text-red-400 text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Unable to Load Rooms
        </h3>
        <p className="text-gray-600">Please try refreshing the page.</p>
      </div>
    );
  }
}

export default async function ResortDetail({
  params,
}: {
  params: { slug: string };
}) {
  const resort = await prisma.resort.findUnique({
    where: { slug: params.slug },
  });

  if (!resort) return notFound();

  return (
    <div className="max-w-7xl mx-auto px-4 py-10">
      {/* Hero Section */}
      <div className="relative mb-8">
        <Image
          src={resort.image}
          alt={resort.name}
          className="w-full h-[400px] object-cover rounded-xl"
          width={1200}
          height={400}
          priority
        />
        <div className="absolute inset-0 bg-black bg-opacity-30 rounded-xl flex items-end">
          <div className="p-8 text-white">
            <h1 className="text-4xl font-bold mb-2">{resort.name}</h1>
            <p className="text-lg flex items-center">📍 {resort.location}</p>
          </div>
        </div>
      </div>

      {/* Resort Information */}
      <div className="mb-12">
        <div className="bg-white rounded-xl shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            About This Resort
          </h2>
          <p className="text-lg text-gray-700 leading-relaxed">
            {resort.description}
          </p>

          {/* Quick Booking CTA */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-blue-900">Ready to Book?</h3>
                <p className="text-blue-700 text-sm">
                  Choose from our available rooms below
                </p>
              </div>
              <Link
                href={`/booking?resortId=${resort.id}`}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Book Resort
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Available Rooms Section */}
      <div className="mb-12">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Available Rooms</h2>
          <span className="text-sm text-gray-500">Prices per night</span>
        </div>

        <Suspense
          fallback={
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-white rounded-xl shadow-md animate-pulse"
                >
                  <div className="h-48 bg-gray-200 rounded-t-xl"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-full"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          }
        >
          <RoomsSection resortId={resort.id} />
        </Suspense>
      </div>

      {/* Reviews Section */}
      <div className="mt-12">
        <div className="bg-white rounded-xl shadow-sm p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Guest Reviews
          </h2>

          <div className="space-y-8">
            <Suspense
              fallback={
                <div className="animate-pulse">
                  <div className="h-32 bg-gray-100 rounded-lg mb-4"></div>
                  <div className="h-4 bg-gray-100 rounded w-1/4"></div>
                </div>
              }
            >
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Share Your Experience
                </h3>
                <ReviewForm
                  contentType="resort"
                  contentId={resort.id}
                  contentName={resort.name}
                  onSubmit={async (data) => {
                    try {
                      const response = await fetch("/api/reviews", {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify(data),
                      });
                      if (!response.ok) {
                        throw new Error("Failed to submit review");
                      }
                    } catch (error) {
                      console.error("Review submission error:", error);
                      throw error;
                    }
                  }}
                />
              </div>
            </Suspense>

            <div className="border-t pt-8">
              <Suspense
                fallback={
                  <div className="animate-pulse space-y-4">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div
                        key={i}
                        className="h-32 bg-gray-100 rounded-lg"
                      ></div>
                    ))}
                  </div>
                }
              >
                <ReviewsList
                  contentType="resort"
                  contentId={resort.id}
                  contentName={resort.name}
                  showFilters={true}
                  showStats={true}
                  maxItems={10}
                />
              </Suspense>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
