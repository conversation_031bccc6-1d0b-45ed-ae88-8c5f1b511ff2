import { Suspense } from "react";
import ResortAnalyticsDashboard from "@/app/components/admin/ResortAnalyticsDashboard";
import Link from "next/link";

export default function ResortAnalyticsPage() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Resort Analytics</h1>
            <p className="text-gray-600 mt-1">
              Comprehensive insights into resort performance and booking trends
            </p>
          </div>
          <div className="flex space-x-3">
            <Link
              href="/admin/resorts"
              className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            >
              Manage Resorts
            </Link>
            <Link
              href="/admin/dashboard"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Main Dashboard
            </Link>
          </div>
        </div>
      </div>

      {/* Analytics Dashboard */}
      <Suspense
        fallback={
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="space-y-3">
                  {Array.from({ length: 7 }).map((_, i) => (
                    <div key={i} className="h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
              <div className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="h-12 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        }
      >
        <ResortAnalyticsDashboard />
      </Suspense>

      {/* Additional Information */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">Analytics Information</h3>
        <div className="text-blue-800 text-sm space-y-1">
          <p>• Data is updated every 5 minutes automatically</p>
          <p>• Occupancy rate is calculated based on confirmed bookings for the last 30 days</p>
          <p>• Revenue calculations include only confirmed bookings with room assignments</p>
          <p>• Top performing resorts are ranked by booking count in the last 30 days</p>
          <p>• Booking trends show daily booking creation for the past 7 days</p>
        </div>
      </div>
    </div>
  );
}
