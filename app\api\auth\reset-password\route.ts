import { NextResponse } from "next/server";
import { createPasswordResetToken, verifyPasswordResetToken } from "@/lib/email-verification";
import { logAuditEvent } from "@/lib/security";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import bcrypt from "bcryptjs";

// Rate limiting for password reset requests
const resetRateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RESET_RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RESET_RATE_LIMIT_MAX_ATTEMPTS = 3; // Max 3 reset requests per 15 minutes

// Validation schemas
const RequestResetSchema = z.object({
  email: z.string().email("Invalid email address"),
});

const ResetPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
  token: z.string().min(1, "Token is required"),
  password: z.string().min(8, "Password must be at least 8 characters long"),
});

function checkResetRateLimit(email: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const key = `reset_${email}`;
  const record = resetRateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    resetRateLimitStore.set(key, {
      count: 1,
      resetTime: now + RESET_RATE_LIMIT_WINDOW,
    });
    return { allowed: true };
  }

  if (record.count >= RESET_RATE_LIMIT_MAX_ATTEMPTS) {
    return { allowed: false, resetTime: record.resetTime };
  }

  record.count++;
  resetRateLimitStore.set(key, record);
  return { allowed: true };
}

// POST - Request password reset
export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { action } = body;

    if (action === "request") {
      return await handlePasswordResetRequest(req, body);
    } else if (action === "reset") {
      return await handlePasswordReset(req, body);
    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Password reset error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function handlePasswordResetRequest(req: Request, body: any) {
  // Validate input
  const validation = RequestResetSchema.safeParse(body);
  if (!validation.success) {
    await logAuditEvent({
      action: "PASSWORD_RESET_REQUEST_INVALID_INPUT",
      resource: "/api/auth/reset-password",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: validation.error.errors.map(e => e.message).join(", "),
    });

    return NextResponse.json(
      { 
        error: "Invalid input", 
        details: validation.error.errors 
      },
      { status: 400 }
    );
  }

  const { email } = validation.data;

  // Check rate limiting
  const rateLimitCheck = checkResetRateLimit(email);
  if (!rateLimitCheck.allowed) {
    await logAuditEvent({
      action: "PASSWORD_RESET_REQUEST_RATE_LIMITED",
      resource: "/api/auth/reset-password",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: "Rate limit exceeded",
    });

    const resetTime = rateLimitCheck.resetTime || Date.now();
    const waitMinutes = Math.ceil((resetTime - Date.now()) / (60 * 1000));

    return NextResponse.json(
      { 
        error: "Too many reset requests", 
        message: `Please wait ${waitMinutes} minutes before requesting another password reset.`,
        retryAfter: resetTime
      },
      { status: 429 }
    );
  }

  // Create and send password reset token
  const result = await createPasswordResetToken(email);

  if (!result.success) {
    await logAuditEvent({
      action: "PASSWORD_RESET_REQUEST_FAILED",
      resource: "/api/auth/reset-password",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: result.error || "Unknown error",
    });

    return NextResponse.json(
      { error: result.error || "Failed to send reset email" },
      { status: 500 }
    );
  }

  await logAuditEvent({
    action: "PASSWORD_RESET_REQUEST_SUCCESS",
    resource: "/api/auth/reset-password",
    ip: req.headers.get("x-forwarded-for") || "unknown",
    userAgent: req.headers.get("user-agent") || "unknown",
    success: true,
  });

  return NextResponse.json({
    success: true,
    message: "If an account with that email exists, a password reset link has been sent.",
  });
}

async function handlePasswordReset(req: Request, body: any) {
  // Validate input
  const validation = ResetPasswordSchema.safeParse(body);
  if (!validation.success) {
    await logAuditEvent({
      action: "PASSWORD_RESET_INVALID_INPUT",
      resource: "/api/auth/reset-password",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: validation.error.errors.map(e => e.message).join(", "),
    });

    return NextResponse.json(
      { 
        error: "Invalid input", 
        details: validation.error.errors 
      },
      { status: 400 }
    );
  }

  const { email, token, password } = validation.data;

  // Verify the reset token
  const tokenResult = await verifyPasswordResetToken(email, token);
  if (!tokenResult.success) {
    await logAuditEvent({
      action: "PASSWORD_RESET_INVALID_TOKEN",
      resource: "/api/auth/reset-password",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: tokenResult.error || "Invalid token",
    });

    return NextResponse.json(
      { error: tokenResult.error || "Invalid or expired reset token" },
      { status: 400 }
    );
  }

  // Hash the new password
  const hashedPassword = await bcrypt.hash(password, 12);

  // Update user's password
  await prisma.user.updateMany({
    where: { email },
    data: { password: hashedPassword },
  });

  // Mark the reset token as used
  await prisma.verificationToken.updateMany({
    where: {
      identifier: email,
      token,
      type: "password_reset",
    },
    data: { used: true },
  });

  await logAuditEvent({
    action: "PASSWORD_RESET_SUCCESS",
    resource: "/api/auth/reset-password",
    ip: req.headers.get("x-forwarded-for") || "unknown",
    userAgent: req.headers.get("user-agent") || "unknown",
    success: true,
  });

  return NextResponse.json({
    success: true,
    message: "Password reset successfully",
  });
}

// GET - Verify reset token (for form validation)
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const email = searchParams.get("email");
    const token = searchParams.get("token");

    if (!email || !token) {
      return NextResponse.redirect(
        new URL("/auth/reset-password?error=missing-params", req.url)
      );
    }

    // Verify the reset token
    const result = await verifyPasswordResetToken(email, token);

    if (!result.success) {
      return NextResponse.redirect(
        new URL(`/auth/reset-password?error=${encodeURIComponent(result.error || "invalid-token")}`, req.url)
      );
    }

    // Redirect to reset form with valid token
    return NextResponse.redirect(
      new URL(`/auth/reset-password?email=${encodeURIComponent(email)}&token=${token}`, req.url)
    );

  } catch (error) {
    console.error("Password reset GET error:", error);
    return NextResponse.redirect(
      new URL("/auth/reset-password?error=server-error", req.url)
    );
  }
}
