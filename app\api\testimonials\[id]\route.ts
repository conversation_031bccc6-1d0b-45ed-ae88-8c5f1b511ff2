import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { logAuditEvent } from "@/lib/security";

// GET /api/testimonials/[id] - Get a specific testimonial
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const testimonial = await prisma.testimonial.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    if (!testimonial) {
      return NextResponse.json({ error: "Testimonial not found" }, { status: 404 });
    }

    // Only show published testimonials to non-admin users
    const session = await getServerSession(authOptions);
    const isAdmin = session?.user?.role === 'admin';
    const isOwner = session?.user?.email && testimonial.user?.id === session.user.id;

    if (!testimonial.isPublished && !isAdmin && !isOwner) {
      return NextResponse.json({ error: "Testimonial not found" }, { status: 404 });
    }

    return NextResponse.json(testimonial);

  } catch (error) {
    console.error("Error fetching testimonial:", error);
    return NextResponse.json(
      { error: "Failed to fetch testimonial" },
      { status: 500 }
    );
  }
}

// PUT /api/testimonials/[id] - Update a testimonial
export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await req.json();

    // Get the existing testimonial
    const existingTestimonial = await prisma.testimonial.findUnique({
      where: { id: params.id },
      include: {
        user: true,
      },
    });

    if (!existingTestimonial) {
      return NextResponse.json({ error: "Testimonial not found" }, { status: 404 });
    }

    // Check if user owns the testimonial or is admin
    const isAdmin = session.user.role === 'admin';
    const isOwner = existingTestimonial.user?.email === session.user.email;

    if (!isAdmin && !isOwner) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Prepare update data
    const updateData: any = {};
    
    if (data.name !== undefined) updateData.name = data.name;
    if (data.title !== undefined) updateData.title = data.title;
    if (data.content !== undefined) updateData.content = data.content;
    if (data.image !== undefined) updateData.image = data.image;
    if (data.location !== undefined) updateData.location = data.location;

    // Admin-only fields
    if (isAdmin) {
      if (data.isFeatured !== undefined) updateData.isFeatured = data.isFeatured;
      if (data.isApproved !== undefined) updateData.isApproved = data.isApproved;
      if (data.isPublished !== undefined) updateData.isPublished = data.isPublished;
      if (data.displayOrder !== undefined) updateData.displayOrder = data.displayOrder;
    }

    // If user updates their testimonial, reset approval status
    if (!isAdmin && (data.name || data.content || data.title)) {
      updateData.isApproved = false;
      updateData.isPublished = false;
    }

    const updatedTestimonial = await prisma.testimonial.update({
      where: { id: params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    await logAuditEvent({
      action: "TESTIMONIAL_UPDATED",
      resource: `testimonial_${params.id}`,
      userId: existingTestimonial.user?.id || "anonymous",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json(updatedTestimonial);

  } catch (error) {
    console.error("Error updating testimonial:", error);
    return NextResponse.json(
      { error: "Failed to update testimonial" },
      { status: 500 }
    );
  }
}

// DELETE /api/testimonials/[id] - Delete a testimonial
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the existing testimonial
    const existingTestimonial = await prisma.testimonial.findUnique({
      where: { id: params.id },
      include: {
        user: true,
      },
    });

    if (!existingTestimonial) {
      return NextResponse.json({ error: "Testimonial not found" }, { status: 404 });
    }

    // Check if user owns the testimonial or is admin
    const isAdmin = session.user.role === 'admin';
    const isOwner = existingTestimonial.user?.email === session.user.email;

    if (!isAdmin && !isOwner) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    await prisma.testimonial.delete({
      where: { id: params.id },
    });

    await logAuditEvent({
      action: "TESTIMONIAL_DELETED",
      resource: `testimonial_${params.id}`,
      userId: existingTestimonial.user?.id || "anonymous",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json({ message: "Testimonial deleted successfully" });

  } catch (error) {
    console.error("Error deleting testimonial:", error);
    return NextResponse.json(
      { error: "Failed to delete testimonial" },
      { status: 500 }
    );
  }
}
