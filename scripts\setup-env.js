#!/usr/bin/env node

/**
 * Environment Setup Script
 * Helps set up secure environment variables for the application
 */

import fs from "fs";
import crypto from "crypto";
import path from "path";

function generateSecureSecret(length = 64) {
  return crypto.randomBytes(length).toString("hex");
}

function createEnvFile() {
  const envPath = path.join(process.cwd(), ".env");
  const examplePath = path.join(process.cwd(), ".env.example");

  // Check if .env already exists
  if (fs.existsSync(envPath)) {
    console.log(
      "⚠️  .env file already exists. Please backup and remove it first."
    );
    return;
  }

  // Read the example file
  if (!fs.existsSync(examplePath)) {
    console.log("❌ .env.example file not found. Please create it first.");
    return;
  }

  let envContent = fs.readFileSync(examplePath, "utf8");

  // Generate secure secrets
  const nextAuthSecret = generateSecureSecret(64);
  const jwtSecret = generateSecureSecret(32);

  // Replace placeholder values
  envContent = envContent.replace("your-secret-key-here", nextAuthSecret);
  envContent += `\n# Generated secrets\nJWT_SECRET=${jwtSecret}\n`;

  // Write the new .env file
  fs.writeFileSync(envPath, envContent);

  console.log("✅ .env file created successfully!");
  console.log("🔐 Secure secrets generated automatically");
  console.log("⚠️  Please update the following values manually:");
  console.log("   - MONGODB_URI (your database connection string)");
  console.log("   - GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET");
  console.log("   - CLOUDINARY_* values");
  console.log("   - EMAIL_* values");
  console.log("");
  console.log("🚨 IMPORTANT: Never commit the .env file to version control!");
}

function validateEnvironment() {
  const envPath = path.join(process.cwd(), ".env");

  if (!fs.existsSync(envPath)) {
    console.log(
      "❌ .env file not found. Run with --create flag to create one."
    );
    return false;
  }

  const envContent = fs.readFileSync(envPath, "utf8");
  const lines = envContent
    .split("\n")
    .filter((line) => line.trim() && !line.startsWith("#"));

  const requiredVars = [
    "MONGODB_URI",
    "NEXTAUTH_SECRET",
    "NEXTAUTH_URL",
    "GOOGLE_CLIENT_ID",
    "GOOGLE_CLIENT_SECRET",
  ];

  const missingVars = [];
  const weakSecrets = [];

  for (const varName of requiredVars) {
    const line = lines.find((l) => l.startsWith(`${varName}=`));
    if (!line) {
      missingVars.push(varName);
    } else {
      const value = line.split("=")[1];
      if (varName.includes("SECRET") && value && value.length < 32) {
        weakSecrets.push(varName);
      }
    }
  }

  if (missingVars.length > 0) {
    console.log("❌ Missing required environment variables:");
    missingVars.forEach((v) => console.log(`   - ${v}`));
  }

  if (weakSecrets.length > 0) {
    console.log(
      "⚠️  Weak secrets detected (should be at least 32 characters):"
    );
    weakSecrets.forEach((v) => console.log(`   - ${v}`));
  }

  if (missingVars.length === 0 && weakSecrets.length === 0) {
    console.log("✅ Environment configuration looks good!");
    return true;
  }

  return false;
}

function addToGitignore() {
  const gitignorePath = path.join(process.cwd(), ".gitignore");

  if (!fs.existsSync(gitignorePath)) {
    fs.writeFileSync(gitignorePath, "");
  }

  const gitignoreContent = fs.readFileSync(gitignorePath, "utf8");

  const entriesToAdd = [
    ".env",
    ".env.local",
    ".env.production",
    ".env.staging",
    "*.log",
    "logs/",
    "temp/",
    ".DS_Store",
  ];

  let newContent = gitignoreContent;
  let added = false;

  for (const entry of entriesToAdd) {
    if (!gitignoreContent.includes(entry)) {
      newContent += `\n${entry}`;
      added = true;
    }
  }

  if (added) {
    fs.writeFileSync(gitignorePath, newContent);
    console.log("✅ Updated .gitignore with security entries");
  }
}

// Main execution
const args = process.argv.slice(2);

if (args.includes("--create")) {
  createEnvFile();
  addToGitignore();
} else if (args.includes("--validate")) {
  validateEnvironment();
} else {
  console.log("Environment Setup Script");
  console.log("");
  console.log("Usage:");
  console.log(
    "  node scripts/setup-env.js --create    Create .env from template"
  );
  console.log("  node scripts/setup-env.js --validate  Validate current .env");
  console.log("");
  console.log("Examples:");
  console.log("  npm run setup:env");
  console.log("  npm run validate:env");
}
