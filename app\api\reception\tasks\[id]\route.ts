import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { logAuditEvent } from "@/lib/security";
import { requireRole } from "@/lib/checkRole";
import { z } from "zod";

// Validation schema for updating tasks
const UpdateTaskSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().min(1).max(1000).optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).optional(),
  status: z.enum(["pending", "in_progress", "completed", "cancelled"]).optional(),
  assignedTo: z.string().optional(),
  dueDate: z.string().refine((date) => !isNaN(Date.parse(date)), "Invalid date").optional(),
  category: z.enum(["housekeeping", "maintenance", "guest_service", "spa", "food_service", "other"]).optional(),
  location: z.string().optional(),
  estimatedDuration: z.number().min(1).max(480).optional(),
  notes: z.string().max(500).optional(),
});

export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and role
    const roleCheck = await requireRole(["admin", "manager", "receptionist"]);
    if (roleCheck instanceof NextResponse) {
      return roleCheck;
    }

    const rawData = await req.json();

    // Validate input data
    const validation = UpdateTaskSchema.safeParse(rawData);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 }
      );
    }

    // Check if task exists
    const existingTask = await prisma.staffTask.findUnique({
      where: { id: params.id },
    });

    if (!existingTask) {
      return NextResponse.json(
        { error: "Task not found" },
        { status: 404 }
      );
    }

    const updateData = validation.data;

    // Convert dueDate string to Date if provided
    if (updateData.dueDate) {
      (updateData as any).dueDate = new Date(updateData.dueDate);
    }

    // Update task
    const updatedTask = await prisma.staffTask.update({
      where: { id: params.id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
    });

    await logAuditEvent({
      userId: roleCheck.user.id,
      action: "STAFF_TASK_UPDATED",
      resource: `/api/reception/tasks/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    // TODO: Send notification if status changed
    // TODO: Send notification if assigned to different person

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error("Error updating staff task:", error);
    await logAuditEvent({
      action: "STAFF_TASK_UPDATE_ERROR",
      resource: `/api/reception/tasks/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Failed to update staff task" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and role
    const roleCheck = await requireRole(["admin", "manager", "receptionist"]);
    if (roleCheck instanceof NextResponse) {
      return roleCheck;
    }

    // Check if task exists
    const existingTask = await prisma.staffTask.findUnique({
      where: { id: params.id },
    });

    if (!existingTask) {
      return NextResponse.json(
        { error: "Task not found" },
        { status: 404 }
      );
    }

    // Delete task
    await prisma.staffTask.delete({
      where: { id: params.id },
    });

    await logAuditEvent({
      userId: roleCheck.user.id,
      action: "STAFF_TASK_DELETED",
      resource: `/api/reception/tasks/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json({ message: "Task deleted successfully" });
  } catch (error) {
    console.error("Error deleting staff task:", error);
    await logAuditEvent({
      action: "STAFF_TASK_DELETE_ERROR",
      resource: `/api/reception/tasks/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Failed to delete staff task" },
      { status: 500 }
    );
  }
}
