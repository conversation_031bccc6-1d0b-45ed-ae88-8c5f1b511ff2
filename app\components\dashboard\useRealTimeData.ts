"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { RealTimeUpdate, RealTimeConfig, DashboardError } from './types';

/**
 * Custom hook for real-time data updates
 * Supports polling and WebSocket connections
 */
export function useRealTimeData<T>(
  fetchData: () => Promise<T>,
  config: Partial<RealTimeConfig> = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<DashboardError | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const mountedRef = useRef(true);

  const defaultConfig: RealTimeConfig = {
    enabled: true,
    updateInterval: 30000, // 30 seconds
    reconnectAttempts: 5,
    ...config,
  };

  const handleError = useCallback((error: Error | DashboardError) => {
    if (!mountedRef.current) return;

    const dashboardError: DashboardError = 'code' in error ? error : {
      code: 'REAL_TIME_ERROR',
      message: error.message || 'Failed to fetch real-time data',
      details: error.stack,
      timestamp: new Date().toISOString(),
      recoverable: true,
    };

    setError(dashboardError);
    setLoading(false);
    
    if (defaultConfig.onError) {
      defaultConfig.onError(dashboardError);
    }

    console.error('Real-time data error:', dashboardError);
  }, [defaultConfig]);

  const fetchDataWithErrorHandling = useCallback(async () => {
    if (!mountedRef.current) return;

    try {
      setError(null);
      const result = await fetchData();
      
      if (!mountedRef.current) return;

      setData(result);
      setLastUpdated(new Date());
      setLoading(false);
      setIsConnected(true);
      reconnectAttemptsRef.current = 0;

      // Trigger update callback if provided
      if (defaultConfig.onUpdate) {
        const update: RealTimeUpdate = {
          type: 'system_metric_update',
          data: result,
          timestamp: new Date().toISOString(),
        };
        defaultConfig.onUpdate(update);
      }
    } catch (err) {
      handleError(err as Error);
      setIsConnected(false);
      
      // Attempt reconnection if configured
      if (reconnectAttemptsRef.current < defaultConfig.reconnectAttempts) {
        reconnectAttemptsRef.current++;
        const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
        
        reconnectTimeoutRef.current = setTimeout(() => {
          if (mountedRef.current) {
            fetchDataWithErrorHandling();
          }
        }, delay);
      }
    }
  }, [fetchData, handleError, defaultConfig]);

  const startPolling = useCallback(() => {
    if (!defaultConfig.enabled || intervalRef.current) return;

    // Initial fetch
    fetchDataWithErrorHandling();

    // Set up polling interval
    intervalRef.current = setInterval(() => {
      fetchDataWithErrorHandling();
    }, defaultConfig.updateInterval);
  }, [defaultConfig.enabled, defaultConfig.updateInterval, fetchDataWithErrorHandling]);

  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setIsConnected(false);
  }, []);

  const refresh = useCallback(() => {
    setLoading(true);
    fetchDataWithErrorHandling();
  }, [fetchDataWithErrorHandling]);

  const retry = useCallback(() => {
    setError(null);
    setLoading(true);
    reconnectAttemptsRef.current = 0;
    fetchDataWithErrorHandling();
  }, [fetchDataWithErrorHandling]);

  // Start polling on mount
  useEffect(() => {
    mountedRef.current = true;
    startPolling();

    return () => {
      mountedRef.current = false;
      stopPolling();
    };
  }, [startPolling, stopPolling]);

  // Handle visibility change to pause/resume polling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else if (defaultConfig.enabled) {
        startPolling();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [defaultConfig.enabled, startPolling, stopPolling]);

  return {
    data,
    loading,
    error,
    lastUpdated,
    isConnected,
    refresh,
    retry,
    startPolling,
    stopPolling,
  };
}

/**
 * Hook for WebSocket-based real-time updates
 */
export function useWebSocketData<T>(
  url: string,
  config: Partial<RealTimeConfig> = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<DashboardError | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const mountedRef = useRef(true);

  const defaultConfig: RealTimeConfig = {
    enabled: true,
    updateInterval: 1000, // Not used for WebSocket, but kept for consistency
    reconnectAttempts: 5,
    ...config,
  };

  const handleError = useCallback((error: Error | string) => {
    if (!mountedRef.current) return;

    const dashboardError: DashboardError = {
      code: 'WEBSOCKET_ERROR',
      message: typeof error === 'string' ? error : error.message,
      details: typeof error === 'string' ? undefined : error.stack,
      timestamp: new Date().toISOString(),
      recoverable: true,
    };

    setError(dashboardError);
    setLoading(false);
    setIsConnected(false);
    
    if (defaultConfig.onError) {
      defaultConfig.onError(dashboardError);
    }

    console.error('WebSocket error:', dashboardError);
  }, [defaultConfig]);

  const connect = useCallback(() => {
    if (!defaultConfig.enabled || wsRef.current?.readyState === WebSocket.OPEN) return;

    try {
      wsRef.current = new WebSocket(url);

      wsRef.current.onopen = () => {
        if (!mountedRef.current) return;
        
        setIsConnected(true);
        setError(null);
        setLoading(false);
        reconnectAttemptsRef.current = 0;
        console.log('WebSocket connected');
      };

      wsRef.current.onmessage = (event) => {
        if (!mountedRef.current) return;

        try {
          const update: RealTimeUpdate = JSON.parse(event.data);
          setData(update.data);
          setLastUpdated(new Date());
          
          if (defaultConfig.onUpdate) {
            defaultConfig.onUpdate(update);
          }
        } catch (err) {
          handleError(new Error('Failed to parse WebSocket message'));
        }
      };

      wsRef.current.onclose = (event) => {
        if (!mountedRef.current) return;

        setIsConnected(false);
        
        if (!event.wasClean && reconnectAttemptsRef.current < defaultConfig.reconnectAttempts) {
          reconnectAttemptsRef.current++;
          const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              connect();
            }
          }, delay);
        }
      };

      wsRef.current.onerror = () => {
        handleError('WebSocket connection error');
      };
    } catch (err) {
      handleError(err as Error);
    }
  }, [url, defaultConfig, handleError]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setIsConnected(false);
  }, []);

  const retry = useCallback(() => {
    setError(null);
    setLoading(true);
    reconnectAttemptsRef.current = 0;
    disconnect();
    connect();
  }, [connect, disconnect]);

  // Connect on mount
  useEffect(() => {
    mountedRef.current = true;
    connect();

    return () => {
      mountedRef.current = false;
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    data,
    loading,
    error,
    isConnected,
    lastUpdated,
    retry,
    connect,
    disconnect,
  };
}
