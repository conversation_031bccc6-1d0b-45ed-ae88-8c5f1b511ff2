import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const offers = await prisma.specialOffer.findMany({
      where: {
        OR: [
          { validUntil: null }, // No expiry date
          { validUntil: { gte: new Date() } }, // Not expired
        ],
      },
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(offers);
  } catch (error) {
    console.error("Error fetching special offers:", error);
    return NextResponse.json(
      { error: "Failed to fetch special offers" },
      { status: 500 }
    );
  }
}
