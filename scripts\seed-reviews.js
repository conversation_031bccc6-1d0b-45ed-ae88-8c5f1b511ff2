#!/usr/bin/env node

/**
 * Seed script to add sample testimonials and reviews
 * Run this after the database migration
 */

import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

const sampleTestimonials = [
  {
    name: "<PERSON><PERSON>.",
    content:
      "Amazing resort experience! Great hospitality and stunning views. The staff went above and beyond to make our stay memorable.",
    location: "Addis Ababa, Ethiopia",
    isFeatured: true,
    isApproved: true,
    isPublished: true,
    displayOrder: 1,
  },
  {
    name: "<PERSON>",
    content:
      "The spa treatments were exceptional. Highly recommend Kuriftu! The massage therapy was exactly what I needed after a long week.",
    location: "Dire Dawa, Ethiopia",
    isFeatured: true,
    isApproved: true,
    isPublished: true,
    displayOrder: 2,
  },
  {
    name: "<PERSON><PERSON>",
    title: "Perfect Family Getaway",
    content:
      "We had an incredible family vacation at Kuriftu. The kids loved the water activities and we enjoyed the peaceful atmosphere. Will definitely return!",
    location: "Bahir Dar, Ethiopia",
    isFeatured: true,
    isApproved: true,
    isPublished: true,
    displayOrder: 3,
  },
  {
    name: "<PERSON>",
    title: "Outstanding Service",
    content:
      "From check-in to check-out, every aspect of our stay was perfect. The food was delicious, rooms were clean, and the views were breathtaking.",
    location: "Hawassa, Ethiopia",
    isFeatured: true,
    isApproved: true,
    isPublished: true,
    displayOrder: 4,
  },
  {
    name: "Hanan A.",
    content:
      "The wellness programs here are top-notch. I felt completely rejuvenated after my stay. The yoga sessions by the lake were particularly amazing.",
    location: "Mekelle, Ethiopia",
    isFeatured: true,
    isApproved: true,
    isPublished: true,
    displayOrder: 5,
  },
  {
    name: "Robel T.",
    title: "Business Retreat Success",
    content:
      "Hosted our company retreat here and it was a huge success. The conference facilities are modern and the team building activities were excellent.",
    location: "Jimma, Ethiopia",
    isFeatured: true,
    isApproved: true,
    isPublished: true,
    displayOrder: 6,
  },
];

async function seedTestimonials() {
  console.log("🌱 Seeding sample testimonials...");

  try {
    // Clear existing testimonials (optional)
    await prisma.testimonial.deleteMany({});
    console.log("🗑️  Cleared existing testimonials");

    // Create sample testimonials
    for (const testimonial of sampleTestimonials) {
      await prisma.testimonial.create({
        data: testimonial,
      });
      console.log(`✅ Created testimonial by ${testimonial.name}`);
    }

    console.log(
      `🎉 Successfully seeded ${sampleTestimonials.length} testimonials!`
    );
  } catch (error) {
    console.error("❌ Error seeding testimonials:", error);
    throw error;
  }
}

async function seedSampleReviews() {
  console.log("🌱 Seeding sample reviews...");

  try {
    // Get some resorts and spa treatments to review
    const resorts = await prisma.resort.findMany({ take: 2 });
    const spaTreatments = await prisma.spaTreatment.findMany({ take: 2 });

    if (resorts.length === 0 || spaTreatments.length === 0) {
      console.log(
        "⚠️  No resorts or spa treatments found. Skipping review seeding."
      );
      return;
    }

    // Get or create a sample user
    let sampleUser = await prisma.user.findFirst({
      where: { email: "<EMAIL>" },
    });

    if (!sampleUser) {
      sampleUser = await prisma.user.create({
        data: {
          name: "Sample Guest",
          email: "<EMAIL>",
          role: "user",
        },
      });
      console.log("👤 Created sample user");
    }

    const sampleReviews = [
      {
        userId: sampleUser.id,
        resortId: resorts[0]?.id,
        rating: 5,
        title: "Absolutely Perfect!",
        comment:
          "This resort exceeded all our expectations. The location is stunning, the staff is incredibly friendly, and the amenities are top-notch. We'll definitely be back!",
        isVerified: true,
        isApproved: true,
        isPublished: true,
      },
      {
        userId: sampleUser.id,
        spaId: spaTreatments[0]?.id,
        rating: 4,
        title: "Very Relaxing",
        comment:
          "The massage was exactly what I needed. The therapist was professional and the atmosphere was very calming. Only minor issue was the wait time.",
        isVerified: true,
        isApproved: true,
        isPublished: true,
      },
    ];

    // Clear existing reviews (optional)
    await prisma.review.deleteMany({});
    console.log("🗑️  Cleared existing reviews");

    // Create sample reviews
    for (const review of sampleReviews) {
      if (review.resortId || review.spaId) {
        await prisma.review.create({
          data: review,
        });
        console.log(
          `✅ Created review for ${
            review.resortId ? "resort" : "spa treatment"
          }`
        );
      }
    }

    console.log(`🎉 Successfully seeded ${sampleReviews.length} reviews!`);
  } catch (error) {
    console.error("❌ Error seeding reviews:", error);
    throw error;
  }
}

async function main() {
  console.log("🚀 Starting review system seeding...");

  try {
    await seedTestimonials();
    await seedSampleReviews();

    console.log("");
    console.log("✅ All seeding completed successfully!");
    console.log("");
    console.log("📝 What was created:");
    console.log(`- ${sampleTestimonials.length} featured testimonials`);
    console.log("- Sample reviews for resorts and spa treatments");
    console.log("- Sample user for reviews");
    console.log("");
    console.log("🎯 Next steps:");
    console.log("1. Visit your homepage to see the testimonials");
    console.log("2. Visit resort or spa pages to see the reviews");
    console.log("3. Login as admin to manage reviews and testimonials");
    console.log("4. Test submitting new reviews as a logged-in user");
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
