import { NextResponse } from "next/server";
import { createEmailVerificationToken } from "@/lib/email-verification";
import { logAuditEvent } from "@/lib/security";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Rate limiting - simple in-memory store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_ATTEMPTS = 3; // Max 3 verification emails per 15 minutes

// Validation schema
const SendVerificationSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().optional(),
});

function checkRateLimit(email: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const key = `verify_${email}`;
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // Reset or create new record
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    });
    return { allowed: true };
  }

  if (record.count >= RATE_LIMIT_MAX_ATTEMPTS) {
    return { allowed: false, resetTime: record.resetTime };
  }

  // Increment count
  record.count++;
  rateLimitStore.set(key, record);
  return { allowed: true };
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    
    // Validate input
    const validation = SendVerificationSchema.safeParse(body);
    if (!validation.success) {
      await logAuditEvent({
        action: "SEND_VERIFICATION_INVALID_INPUT",
        resource: "/api/auth/send-verification",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: validation.error.errors.map(e => e.message).join(", "),
      });

      return NextResponse.json(
        { 
          error: "Invalid input", 
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { email, name } = validation.data;

    // Check rate limiting
    const rateLimitCheck = checkRateLimit(email);
    if (!rateLimitCheck.allowed) {
      await logAuditEvent({
        action: "SEND_VERIFICATION_RATE_LIMITED",
        resource: "/api/auth/send-verification",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Rate limit exceeded",
      });

      const resetTime = rateLimitCheck.resetTime || Date.now();
      const waitMinutes = Math.ceil((resetTime - Date.now()) / (60 * 1000));

      return NextResponse.json(
        { 
          error: "Too many verification requests", 
          message: `Please wait ${waitMinutes} minutes before requesting another verification email.`,
          retryAfter: resetTime
        },
        { status: 429 }
      );
    }

    // Check if user exists and if email is already verified
    const existingUser = await prisma.user.findUnique({
      where: { email },
      select: { emailVerified: true, name: true },
    });

    if (existingUser?.emailVerified) {
      await logAuditEvent({
        action: "SEND_VERIFICATION_ALREADY_VERIFIED",
        resource: "/api/auth/send-verification",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Email already verified",
      });

      return NextResponse.json(
        { error: "Email is already verified" },
        { status: 400 }
      );
    }

    // Create and send verification token
    const result = await createEmailVerificationToken(
      email, 
      name || existingUser?.name || undefined
    );

    if (!result.success) {
      await logAuditEvent({
        action: "SEND_VERIFICATION_FAILED",
        resource: "/api/auth/send-verification",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: result.error || "Unknown error",
      });

      return NextResponse.json(
        { error: result.error || "Failed to send verification email" },
        { status: 500 }
      );
    }

    await logAuditEvent({
      action: "SEND_VERIFICATION_SUCCESS",
      resource: "/api/auth/send-verification",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json({
      success: true,
      message: "Verification email sent successfully",
    });

  } catch (error) {
    console.error("Send verification error:", error);

    await logAuditEvent({
      action: "SEND_VERIFICATION_ERROR",
      resource: "/api/auth/send-verification",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
