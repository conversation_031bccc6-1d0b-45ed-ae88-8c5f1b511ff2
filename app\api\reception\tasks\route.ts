import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { logAuditEvent } from "@/lib/security";
import { requireRole } from "@/lib/checkRole";
import { z } from "zod";

// Validation schema for creating tasks
const CreateTaskSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  description: z.string().min(1, "Description is required").max(1000, "Description too long"),
  priority: z.enum(["low", "medium", "high", "urgent"]),
  assignedTo: z.string().min(1, "Assigned to is required"),
  dueDate: z.string().refine((date) => !isNaN(Date.parse(date)), "Invalid date"),
  category: z.enum(["housekeeping", "maintenance", "guest_service", "spa", "food_service", "other"]),
  location: z.string().optional(),
  estimatedDuration: z.number().min(1).max(480).optional(), // Max 8 hours
  notes: z.string().max(500).optional(),
});

export async function GET(req: Request) {
  try {
    // Check authentication and role
    const roleCheck = await requireRole(["admin", "manager", "receptionist"]);
    if (roleCheck instanceof NextResponse) {
      return roleCheck;
    }

    const url = new URL(req.url);
    const status = url.searchParams.get("status");
    const priority = url.searchParams.get("priority");
    const category = url.searchParams.get("category");
    const assignedTo = url.searchParams.get("assignedTo");

    // Build where clause based on filters
    const where: any = {};
    if (status && status !== "all") {
      where.status = status;
    }
    if (priority && priority !== "all") {
      where.priority = priority;
    }
    if (category && category !== "all") {
      where.category = category;
    }
    if (assignedTo && assignedTo !== "all") {
      where.assignedTo = assignedTo;
    }

    // Fetch tasks
    const tasks = await prisma.staffTask.findMany({
      where,
      orderBy: [
        { priority: "desc" }, // Urgent first
        { dueDate: "asc" },   // Then by due date
        { createdAt: "desc" }, // Then by creation date
      ],
    });

    await logAuditEvent({
      userId: roleCheck.user.id,
      action: "STAFF_TASKS_FETCHED",
      resource: "/api/reception/tasks",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json(tasks);
  } catch (error) {
    console.error("Error fetching staff tasks:", error);
    await logAuditEvent({
      action: "STAFF_TASKS_FETCH_ERROR",
      resource: "/api/reception/tasks",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Failed to fetch staff tasks" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    // Check authentication and role
    const roleCheck = await requireRole(["admin", "manager", "receptionist"]);
    if (roleCheck instanceof NextResponse) {
      return roleCheck;
    }

    const rawData = await req.json();

    // Validate input data
    const validation = CreateTaskSchema.safeParse(rawData);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 }
      );
    }

    const {
      title,
      description,
      priority,
      assignedTo,
      dueDate,
      category,
      location,
      estimatedDuration,
      notes,
    } = validation.data;

    // Create task
    const task = await prisma.staffTask.create({
      data: {
        title,
        description,
        priority,
        assignedTo,
        assignedBy: roleCheck.user.email,
        dueDate: new Date(dueDate),
        category,
        location,
        estimatedDuration,
        notes,
        status: "pending",
      },
    });

    await logAuditEvent({
      userId: roleCheck.user.id,
      action: "STAFF_TASK_CREATED",
      resource: "/api/reception/tasks",
      resourceId: task.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    // TODO: Send notification to assigned staff member
    // TODO: Create calendar event if applicable

    return NextResponse.json(task, { status: 201 });
  } catch (error) {
    console.error("Error creating staff task:", error);
    await logAuditEvent({
      action: "STAFF_TASK_CREATE_ERROR",
      resource: "/api/reception/tasks",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Failed to create staff task" },
      { status: 500 }
    );
  }
}
