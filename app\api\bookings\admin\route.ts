import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function GET() {
  // Check authorization - allow admin, manager, and receptionist to view bookings
  const authResult = await requireRole(["admin", "manager", "receptionist"]);
  if (authResult instanceof NextResponse) {
    return authResult; // Return error response
  }

  try {
    const bookings = await prisma.booking.findMany({
      orderBy: { createdAt: "desc" },
      include: {
        resort: true,
        spaTreatment: true, // Fixed: was 'spa' but should be 'spaTreatment'
      },
    });
    return NextResponse.json(bookings);
  } catch (error) {
    console.error("Error fetching bookings:", error);
    return NextResponse.json(
      { error: "Failed to fetch bookings" },
      { status: 500 }
    );
  }
}
