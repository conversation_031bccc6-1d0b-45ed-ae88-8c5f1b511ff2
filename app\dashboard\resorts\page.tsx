"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  AdminCardSkeleton,
  GridSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface Resort {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
  isActive: boolean;
  createdAt: string;
}

interface ResortListState {
  resorts: Resort[];
  loading: boolean;
  error: string | null;
}

// Resort card component
function ResortCard({ resort }: { resort: Resort }) {
  return (
    <div className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="relative mb-3">
        <Image
          src={resort.image}
          alt={resort.name}
          width={400}
          height={160}
          className="h-40 w-full object-cover rounded"
          loading="lazy"
        />
        {!resort.isActive && (
          <div className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
            Inactive
          </div>
        )}
      </div>

      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-gray-900">{resort.name}</h2>
        <p className="text-sm text-gray-600">{resort.location}</p>
        <p className="text-sm text-gray-700 line-clamp-2">
          {resort.description}
        </p>
      </div>

      <div className="mt-4 flex justify-between items-center">
        <Link
          href={`/dashboard/resorts/${resort.id}/edit`}
          className="text-blue-600 hover:text-blue-800 font-medium text-sm"
          aria-label={`Edit ${resort.name} resort`}
        >
          Edit Resort
        </Link>
        <Link
          href={`/resorts/${resort.slug}`}
          target="_blank"
          className="text-green-600 hover:text-green-800 font-medium text-sm"
          aria-label={`View ${resort.name} resort details`}
        >
          View Details
        </Link>
      </div>
    </div>
  );
}

export default function ResortList() {
  const [state, setState] = useState<ResortListState>({
    resorts: [],
    loading: true,
    error: null,
  });

  const fetchResorts = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const response = await fetch("/api/resorts");
      if (!response.ok) {
        throw new Error(`Failed to fetch resorts: ${response.status}`);
      }

      const data = await response.json();
      setState({
        resorts: data,
        loading: false,
        error: null,
      });
    } catch (error) {
      console.error("Error fetching resorts:", error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      }));
    }
  };

  useEffect(() => {
    fetchResorts();
  }, []);

  const handleRetry = () => {
    fetchResorts();
  };

  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Resort Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage your resort properties and accommodations
            </p>
          </div>
          <Link
            href="/admin/resorts/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
            aria-label="Create new resort"
          >
            <span className="mr-2">+</span>
            Add Resort
          </Link>
        </div>

        {/* Loading State */}
        {state.loading && (
          <GridSkeleton
            count={6}
            columns={2}
            SkeletonComponent={AdminCardSkeleton}
          />
        )}

        {/* Error State */}
        {state.error && !state.loading && (
          <LoadingError resource="resort data" onRetry={handleRetry} />
        )}

        {/* Empty State */}
        {!state.loading && !state.error && state.resorts.length === 0 && (
          <EmptyState
            title="No Resorts Found"
            message="Start by creating your first resort property."
            actionLabel="+ Create First Resort"
            actionHref="/admin/resorts/create"
            icon={<div className="text-gray-400 text-6xl mb-4">🏨</div>}
          />
        )}

        {/* Resorts Grid */}
        {!state.loading && !state.error && state.resorts.length > 0 && (
          <div
            className="grid grid-cols-1 md:grid-cols-2 gap-6"
            role="list"
            aria-label="Resorts list"
          >
            {state.resorts.map((resort) => (
              <div key={resort.id} role="listitem">
                <ResortCard resort={resort} />
              </div>
            ))}
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
}
