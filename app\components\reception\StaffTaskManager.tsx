"use client";

import { useState, useEffect } from "react";
import {
  CheckCircle,
  Clock,
  User,
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  Filter,
} from "lucide-react";
import { useToast } from "@/app/hooks/useNotifications";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";
import { LoadingSkeleton } from "@/app/components/ui/SkeletonComponents";

interface StaffTask {
  id: string;
  title: string;
  description: string;
  priority: "low" | "medium" | "high" | "urgent";
  status: "pending" | "in_progress" | "completed" | "cancelled";
  assignedTo: string;
  assignedBy: string;
  dueDate: string;
  category:
    | "housekeeping"
    | "maintenance"
    | "guest_service"
    | "spa"
    | "food_service"
    | "other";
  location?: string;
  estimatedDuration?: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface StaffTaskManagerProps {
  className?: string;
}

export default function StaffTaskManager({
  className = "",
}: StaffTaskManagerProps) {
  const toast = useToast();
  const [tasks, setTasks] = useState<StaffTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<{
    status: string;
    priority: string;
    category: string;
    assignedTo: string;
  }>({
    status: "all",
    priority: "all",
    category: "all",
    assignedTo: "all",
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingTask, setEditingTask] = useState<StaffTask | null>(null);

  // Fetch tasks
  const fetchTasks = async () => {
    try {
      setError(null);
      setLoading(true);

      const queryParams = new URLSearchParams();
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== "all") {
          queryParams.append(key, value);
        }
      });

      const response = await fetch(
        `/api/reception/tasks?${queryParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch tasks: ${response.status}`);
      }

      const tasksData = await response.json();
      setTasks(tasksData);
    } catch (err) {
      console.error("Error fetching tasks:", err);
      setError(err instanceof Error ? err.message : "Failed to load tasks");
    } finally {
      setLoading(false);
    }
  };

  // Update task status
  const updateTaskStatus = async (
    taskId: string,
    status: StaffTask["status"]
  ) => {
    try {
      const response = await fetch(`/api/reception/tasks/${taskId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Failed to update task status");
      }

      setTasks((prev) =>
        prev.map((task) =>
          task.id === taskId
            ? { ...task, status, updatedAt: new Date().toISOString() }
            : task
        )
      );

      toast.success(
        "Task Updated",
        `Task status changed to ${status.replace("_", " ")}.`
      );
    } catch (err) {
      console.error("Error updating task status:", err);
      toast.error(
        "Update Failed",
        err instanceof Error ? err.message : "Failed to update task"
      );
    }
  };

  // Delete task
  const deleteTask = async (taskId: string) => {
    if (!confirm("Are you sure you want to delete this task?")) return;

    try {
      const response = await fetch(`/api/reception/tasks/${taskId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to delete task");
      }

      setTasks((prev) => prev.filter((task) => task.id !== taskId));
      toast.success("Task Deleted", "Task has been successfully deleted.");
    } catch (err) {
      console.error("Error deleting task:", err);
      toast.error(
        "Delete Failed",
        err instanceof Error ? err.message : "Failed to delete task"
      );
    }
  };

  // Get priority badge
  const getPriorityBadge = (priority: StaffTask["priority"]) => {
    switch (priority) {
      case "low":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Low
          </span>
        );
      case "medium":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Medium
          </span>
        );
      case "high":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            High
          </span>
        );
      case "urgent":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Urgent
          </span>
        );
      default:
        return null;
    }
  };

  // Get status badge
  const getStatusBadge = (status: StaffTask["status"]) => {
    switch (status) {
      case "pending":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </span>
        );
      case "in_progress":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Clock className="w-3 h-3 mr-1" />
            In Progress
          </span>
        );
      case "completed":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Completed
          </span>
        );
      case "cancelled":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            Cancelled
          </span>
        );
      default:
        return null;
    }
  };

  // Get category badge
  const getCategoryBadge = (category: StaffTask["category"]) => {
    const categoryLabels = {
      housekeeping: "Housekeeping",
      maintenance: "Maintenance",
      guest_service: "Guest Service",
      spa: "Spa",
      food_service: "Food Service",
      other: "Other",
    };

    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
        {categoryLabels[category]}
      </span>
    );
  };

  // Check if task is overdue
  const isOverdue = (task: StaffTask) => {
    return new Date(task.dueDate) < new Date() && task.status !== "completed";
  };

  useEffect(() => {
    fetchTasks();
  }, [filter]);

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <LoadingSkeleton count={6} />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Staff Task Manager
              </h2>
              <p className="text-gray-600 mt-1">
                Assign and track staff tasks and workflows
              </p>
            </div>
            <button
              type="button"
              onClick={() => setShowAddModal(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-4">
            <Filter className="w-5 h-5 text-gray-400" />
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 flex-1">
              <select
                value={filter.status}
                onChange={(e) =>
                  setFilter((prev) => ({ ...prev, status: e.target.value }))
                }
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Filter by task status"
                title="Filter tasks by status"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>

              <select
                value={filter.priority}
                onChange={(e) =>
                  setFilter((prev) => ({ ...prev, priority: e.target.value }))
                }
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Filter by task priority"
                title="Filter tasks by priority"
              >
                <option value="all">All Priority</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>

              <select
                value={filter.category}
                onChange={(e) =>
                  setFilter((prev) => ({ ...prev, category: e.target.value }))
                }
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Filter by task category"
                title="Filter tasks by category"
              >
                <option value="all">All Categories</option>
                <option value="housekeeping">Housekeeping</option>
                <option value="maintenance">Maintenance</option>
                <option value="guest_service">Guest Service</option>
                <option value="spa">Spa</option>
                <option value="food_service">Food Service</option>
                <option value="other">Other</option>
              </select>

              <select
                value={filter.assignedTo}
                onChange={(e) =>
                  setFilter((prev) => ({ ...prev, assignedTo: e.target.value }))
                }
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label="Filter by assigned staff"
                title="Filter tasks by assigned staff"
              >
                <option value="all">All Staff</option>
                <option value="john_doe">John Doe</option>
                <option value="jane_smith">Jane Smith</option>
                <option value="mike_johnson">Mike Johnson</option>
                <option value="sarah_wilson">Sarah Wilson</option>
              </select>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {error ? (
            <div className="text-center py-8">
              <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Error Loading Tasks
              </h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                type="button"
                onClick={fetchTasks}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : tasks.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No Tasks Found
              </h3>
              <p className="text-gray-600">
                No tasks match the current filters.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {tasks.map((task) => (
                <div
                  key={task.id}
                  className={`border rounded-lg p-4 hover:shadow-md transition-shadow ${
                    isOverdue(task)
                      ? "border-red-200 bg-red-50"
                      : "border-gray-200"
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-medium text-gray-900">
                          {task.title}
                        </h3>
                        {getPriorityBadge(task.priority)}
                        {getStatusBadge(task.status)}
                        {getCategoryBadge(task.category)}
                        {isOverdue(task) && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            Overdue
                          </span>
                        )}
                      </div>

                      <p className="text-gray-600 mb-3">{task.description}</p>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-2" />
                          <span>Assigned to: {task.assignedTo}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-2" />
                          <span>
                            Due: {new Date(task.dueDate).toLocaleDateString()}
                          </span>
                        </div>
                        {task.estimatedDuration && (
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-2" />
                            <span>Est. {task.estimatedDuration} min</span>
                          </div>
                        )}
                      </div>

                      {task.location && (
                        <div className="text-sm text-gray-600 mt-2">
                          <strong>Location:</strong> {task.location}
                        </div>
                      )}

                      {task.notes && (
                        <div className="text-sm text-gray-600 mt-2">
                          <strong>Notes:</strong> {task.notes}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      {task.status === "pending" && (
                        <button
                          type="button"
                          onClick={() =>
                            updateTaskStatus(task.id, "in_progress")
                          }
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                        >
                          Start
                        </button>
                      )}
                      {task.status === "in_progress" && (
                        <button
                          type="button"
                          onClick={() => updateTaskStatus(task.id, "completed")}
                          className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                        >
                          Complete
                        </button>
                      )}
                      <button
                        type="button"
                        onClick={() => setEditingTask(task)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                        aria-label="Edit task"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        type="button"
                        onClick={() => deleteTask(task.id)}
                        className="p-2 text-gray-400 hover:text-red-600"
                        aria-label="Delete task"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
}
