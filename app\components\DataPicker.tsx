"use client";

import { useState, useEffect } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

export default function AvailabilityCalendar({
  id,
  type,
}: {
  id: string;
  type: "resort" | "spa";
}) {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);

  useEffect(() => {
    if (!selectedDate) return;

    const fetchAvailability = async () => {
      const res = await fetch("/api/availability", {
        method: "POST",
        body: JSON.stringify({
          date: selectedDate.toISOString().split("T")[0],
          id,
          type,
        }),
      });
      const data = await res.json();
      setIsAvailable(data.isAvailable);
    };

    fetchAvailability();
  }, [selectedDate]);

  return (
    <div className="space-y-2">
      <DatePicker
        selected={selectedDate}
        onChange={(date) => setSelectedDate(date)}
        minDate={new Date()}
        className="p-2 border rounded"
        placeholderText="Select a date"
      />
      {isAvailable === true && <p className="text-green-600">Available</p>}
      {isAvailable === false && <p className="text-red-600">Fully Booked</p>}
    </div>
  );
}
