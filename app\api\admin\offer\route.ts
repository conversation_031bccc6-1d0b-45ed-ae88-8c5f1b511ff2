import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function POST(req: Request) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult; // Return error response
  }

  try {
    const data = await req.json();

    // Basic input validation
    if (!data.name || !data.description) {
      return NextResponse.json(
        { error: "Missing required fields: name, description" },
        { status: 400 }
      );
    }

    // Validate pricing logic
    if (data.originalPrice && data.originalPrice <= 0) {
      return NextResponse.json(
        { error: "Original price must be greater than 0" },
        { status: 400 }
      );
    }

    if (data.discountPrice && data.discountPrice <= 0) {
      return NextResponse.json(
        { error: "Discount price must be greater than 0" },
        { status: 400 }
      );
    }

    if (data.originalPrice && data.discountPrice && data.discountPrice >= data.originalPrice) {
      return NextResponse.json(
        { error: "Discount price must be less than original price" },
        { status: 400 }
      );
    }

    const offer = await prisma.specialOffer.create({ data });
    return NextResponse.json(offer);
  } catch (error) {
    console.error("Error creating special offer:", error);
    return NextResponse.json(
      { error: "Failed to create special offer" },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Check authorization
  const authResult = await requireRole(["admin", "manager"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const offers = await prisma.specialOffer.findMany({
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(offers);
  } catch (error) {
    console.error("Error fetching special offers:", error);
    return NextResponse.json(
      { error: "Failed to fetch special offers" },
      { status: 500 }
    );
  }
}
