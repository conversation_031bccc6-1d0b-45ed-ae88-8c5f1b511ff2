"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import ImageUpload from "@/app/components/ui/ImageUpload";

interface SpecialOffer {
  id: string;
  name: string;
  description: string;
  image: string;
  images: string[];
  category?: string;
  originalPrice?: number;
  discountPrice?: number;
  discountPercent?: number;
  features: string[];
  validFrom?: Date;
  validUntil?: Date;
  terms?: string;
  isActive: boolean;
}

interface OfferFormProps {
  initialData?: SpecialOffer;
}

export default function OfferForm({ initialData }: OfferFormProps) {
  const router = useRouter();
  const isEdit = !!initialData;

  const [name, setName] = useState(initialData?.name || "");
  const [description, setDescription] = useState(
    initialData?.description || ""
  );
  const [image, setImage] = useState(initialData?.image || "");
  const [category, setCategory] = useState(initialData?.category || "");
  const [originalPrice, setOriginalPrice] = useState(
    initialData?.originalPrice?.toString() || ""
  );
  const [discountPrice, setDiscountPrice] = useState(
    initialData?.discountPrice?.toString() || ""
  );
  const [discountPercent, setDiscountPercent] = useState(
    initialData?.discountPercent?.toString() || ""
  );
  const [features, setFeatures] = useState<string[]>(
    initialData?.features || []
  );
  const [validFrom, setValidFrom] = useState(
    initialData?.validFrom
      ? new Date(initialData.validFrom).toISOString().split("T")[0]
      : ""
  );
  const [validUntil, setValidUntil] = useState(
    initialData?.validUntil
      ? new Date(initialData.validUntil).toISOString().split("T")[0]
      : ""
  );
  const [terms, setTerms] = useState(initialData?.terms || "");
  const [isActive, setIsActive] = useState(initialData?.isActive ?? true);
  const [newFeature, setNewFeature] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const addFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setFeatures([...features, newFeature.trim()]);
      setNewFeature("");
    }
  };

  const removeFeature = (feature: string) => {
    setFeatures(features.filter((f) => f !== feature));
  };

  // Calculate discount percentage when prices change
  const calculateDiscountPercent = () => {
    if (originalPrice && discountPrice) {
      const original = parseFloat(originalPrice);
      const discount = parseFloat(discountPrice);
      if (original > 0 && discount > 0 && discount < original) {
        const percent = Math.round(((original - discount) / original) * 100);
        setDiscountPercent(percent.toString());
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim() || !description.trim() || !image) {
      alert("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        name,
        description,
        image,

        category: category || null,
        discountPrice: discountPrice ? parseFloat(discountPrice) : null,
        discountPercent: discountPercent ? parseInt(discountPercent) : null,
        features,
        validFrom: validFrom ? new Date(validFrom) : null,
        validUntil: validUntil ? new Date(validUntil) : null,
        terms: terms || null,
        isActive,
      };

      const res = await fetch(
        `/api/admin/offer${isEdit ? `/${initialData.id}` : ""}`,
        {
          method: isEdit ? "PUT" : "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        }
      );

      if (res.ok) {
        router.push("/admin/offers");
        router.refresh();
      } else {
        const errorData = await res.json();
        alert(`Error: ${errorData.error || "Failed to save special offer"}`);
      }
    } catch (error) {
      console.error("Submit error:", error);
      alert("An error occurred while saving the special offer.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-2xl mx-auto">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Offer Name *
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Offer name"
          placeholder="Enter offer name"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description *
        </label>
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Offer description"
          placeholder="Enter detailed description"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Main Image *
        </label>
        <ImageUpload
          value={image}
          onChange={setImage}
          className="w-full"
          placeholder="Upload main image"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Category
        </label>
        <select
          value={category}
          onChange={(e) => setCategory(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Offer category"
          title="Select offer category"
        >
          <option value="">Select Category</option>
          <option value="Package">Package</option>
          <option value="Discount">Discount</option>
          <option value="Seasonal">Seasonal</option>
          <option value="Early Bird">Early Bird</option>
          <option value="Last Minute">Last Minute</option>
          <option value="Group">Group</option>
          <option value="Loyalty">Loyalty</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Original Price (ETB)
          </label>
          <input
            type="number"
            step="0.01"
            value={originalPrice}
            onChange={(e) => {
              setOriginalPrice(e.target.value);
              setTimeout(calculateDiscountPercent, 100);
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Original price in ETB"
            placeholder="Enter original price"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Discount Price (ETB)
          </label>
          <input
            type="number"
            step="0.01"
            value={discountPrice}
            onChange={(e) => {
              setDiscountPrice(e.target.value);
              setTimeout(calculateDiscountPercent, 100);
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Discount price in ETB"
            placeholder="Enter discount price"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Discount %
          </label>
          <input
            type="number"
            value={discountPercent}
            onChange={(e) => setDiscountPercent(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Discount percentage (calculated automatically)"
            title="Discount percentage calculated from original and discount prices"
            readOnly
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Valid From
          </label>
          <input
            type="date"
            value={validFrom}
            onChange={(e) => setValidFrom(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Valid from date"
            title="Select start date for offer"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Valid Until
          </label>
          <input
            type="date"
            value={validUntil}
            onChange={(e) => setValidUntil(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Valid until date"
            title="Select end date for offer"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Features & What&apos;s Included
        </label>
        <div className="flex gap-2 mb-2">
          <input
            type="text"
            value={newFeature}
            onChange={(e) => setNewFeature(e.target.value)}
            placeholder="Add a feature"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Add new feature"
            title="Enter a feature to add to the offer"
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                addFeature();
              }
            }}
          />
          <button
            type="button"
            onClick={addFeature}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Add
          </button>
        </div>
        <div className="flex flex-wrap gap-2">
          {features.map((feature, index) => (
            <span
              key={index}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
            >
              {feature}
              <button
                type="button"
                onClick={() => removeFeature(feature)}
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                ×
              </button>
            </span>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Terms & Conditions
        </label>
        <textarea
          value={terms}
          onChange={(e) => setTerms(e.target.value)}
          rows={3}
          placeholder="Enter terms and conditions for this offer"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Terms and conditions"
          title="Enter terms and conditions for this offer"
        />
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="isActive"
          checked={isActive}
          onChange={(e) => setIsActive(e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
          Active (visible to customers)
        </label>
      </div>

      <div className="flex gap-4">
        <button
          type="button"
          onClick={() => router.back()}
          className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isSubmitting ? "Saving..." : isEdit ? "Update" : "Create"}
        </button>
      </div>
    </form>
  );
}
