import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function POST(req: Request) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult; // Return error response
  }

  try {
    const data = await req.json();

    // Basic input validation
    if (!data.name || !data.description || typeof data.price !== "number") {
      return NextResponse.json(
        { error: "Missing required fields: name, description, price" },
        { status: 400 }
      );
    }

    if (data.price <= 0) {
      return NextResponse.json(
        { error: "Price must be greater than 0" },
        { status: 400 }
      );
    }

    const wellnessService = await prisma.wellnessService.create({ data });
    return NextResponse.json(wellnessService);
  } catch (error) {
    console.error("Error creating wellness service:", error);
    return NextResponse.json(
      { error: "Failed to create wellness service" },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Check authorization
  const authResult = await requireRole(["admin", "manager"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const wellnessServices = await prisma.wellnessService.findMany({
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(wellnessServices);
  } catch (error) {
    console.error("Error fetching wellness services:", error);
    return NextResponse.json(
      { error: "Failed to fetch wellness services" },
      { status: 500 }
    );
  }
}
