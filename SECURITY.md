# Security Implementation Guide

## Overview

This document outlines the security measures implemented in the Kuriftu Water Park RBAC system and provides guidelines for maintaining security best practices.

## Role-Based Access Control (RBAC)

### Role Hierarchy

- **Admin**: Full system access, can manage all resources
- **Manager**: Can manage resorts, spa treatments, and view bookings
- **Receptionist**: Can view and update bookings, limited resource access
- **User**: Basic user access, can create bookings and view own data

### Implementation

- Roles are stored in the database and validated on every request
- JWT tokens contain role information for client-side checks
- Server-side validation is performed for all protected routes
- Middleware enforces route-level access control

## Security Features Implemented

### 1. Authentication

- **Provider**: Google OAuth 2.0 via NextAuth.js
- **Session Strategy**: JWT with secure token handling
- **Token Validation**: Server-side validation on every protected request

### 2. Authorization

- **Route Protection**: Middleware-based route protection
- **API Security**: Role-based API endpoint protection
- **Component Guards**: Client-side role-based component rendering

### 3. Input Validation

- **Schema Validation**: Zod schemas for all API inputs
- **Sanitization**: XSS prevention through input sanitization
- **Type Safety**: TypeScript for compile-time type checking

### 4. Security Headers

- **CSP**: Content Security Policy to prevent XSS
- **HSTS**: HTTP Strict Transport Security
- **X-Frame-Options**: Clickjacking protection
- **X-Content-Type-Options**: MIME type sniffing prevention

### 5. Rate Limiting

- **API Protection**: 100 requests per 15-minute window
- **IP-based**: Rate limiting by client IP address
- **Audit Logging**: Failed attempts are logged

### 6. CSRF Protection

- **Origin Validation**: Request origin verification
- **Custom Headers**: X-Requested-With header validation
- **SameSite Cookies**: Cookie security configuration

## Security Best Practices

### Environment Variables

- Never commit `.env` files to version control
- Use `.env.example` as a template
- Rotate secrets regularly
- Use strong, randomly generated secrets

### Database Security

- Use connection string with authentication
- Enable SSL/TLS for database connections
- Implement proper indexing for performance
- Regular backups with encryption

### API Security

- Always validate input data
- Use parameterized queries (Prisma handles this)
- Implement proper error handling
- Log security events for monitoring

### Client-Side Security

- Never store sensitive data in localStorage
- Use secure HTTP-only cookies for sessions
- Implement proper logout functionality
- Validate user permissions on every action

## Monitoring and Logging

### Audit Events

The system logs the following security events:

- Authentication attempts
- Authorization failures
- Rate limit violations
- CSRF validation failures
- Privilege escalation attempts

### Log Format

```json
{
  "userId": "user-id",
  "action": "ACTION_TYPE",
  "resource": "/api/endpoint",
  "resourceId": "resource-id",
  "ip": "client-ip",
  "userAgent": "user-agent",
  "timestamp": "2024-01-01T00:00:00Z",
  "success": false,
  "error": "error-message"
}
```

## Vulnerability Prevention

### Common Attacks Mitigated

1. **SQL Injection**: Prevented by Prisma ORM
2. **XSS**: Input sanitization and CSP headers
3. **CSRF**: Origin validation and custom headers
4. **Clickjacking**: X-Frame-Options header
5. **Session Hijacking**: Secure JWT implementation
6. **Privilege Escalation**: Strict role validation

### Security Testing

- Regular dependency updates
- Static code analysis
- Penetration testing recommendations
- Security audit checklist

## Incident Response

### Security Incident Checklist

1. **Immediate Response**

   - Identify the scope of the incident
   - Contain the threat
   - Preserve evidence

2. **Investigation**

   - Review audit logs
   - Identify affected users/data
   - Determine root cause

3. **Recovery**

   - Patch vulnerabilities
   - Reset compromised credentials
   - Notify affected users if required

4. **Post-Incident**
   - Update security measures
   - Document lessons learned
   - Update incident response procedures

## Compliance Considerations

### Data Protection

- Implement data minimization principles
- Provide user data export/deletion capabilities
- Maintain audit trails for compliance
- Regular security assessments

### Privacy

- Clear privacy policy
- User consent management
- Data retention policies
- Secure data transmission

## Security Checklist

### Development

- [ ] All API endpoints have proper authorization
- [ ] Input validation is implemented
- [ ] Error messages don't leak sensitive information
- [ ] Security headers are configured
- [ ] Dependencies are up to date

### Deployment

- [ ] Environment variables are properly configured
- [ ] HTTPS is enforced
- [ ] Database connections are secure
- [ ] Monitoring and logging are enabled
- [ ] Backup procedures are in place

### Maintenance

- [ ] Regular security updates
- [ ] Audit log review
- [ ] Access control review
- [ ] Incident response testing
- [ ] Security training for team members

## Contact Information

For security-related issues or questions:

- Create a security issue in the repository
- Follow responsible disclosure practices
- Include detailed reproduction steps
- Provide impact assessment

## Booking System Security

### Booking API Endpoints Security

#### Authentication & Authorization

- **POST /api/bookings**: Requires authentication, users can only book for themselves
- **PATCH /api/bookings/[id]**: Role-based access (users can only cancel own bookings, staff can update any)
- **DELETE /api/bookings/[id]**: Admin-only access for complete booking removal
- **GET /api/bookings/admin**: Staff-only access (admin, manager, receptionist)
- **GET /api/bookings/user**: User can only see their own bookings

#### Input Validation

- **Zod Schema Validation**: All booking inputs validated with comprehensive schemas
- **Date Validation**: Check-in dates cannot be in the past, check-out must be after check-in
- **Resource Validation**: Verify resort/spa exists before creating booking
- **Availability Checking**: Prevent overbooking by checking existing bookings
- **Notes Sanitization**: XSS prevention through input sanitization

#### Business Logic Security

- **Ownership Verification**: Users can only access/modify their own bookings
- **Status Transition Rules**: Prevent invalid status changes (e.g., modifying cancelled bookings)
- **Time-based Restrictions**: Cannot cancel bookings after check-in date
- **Role-based Permissions**: Different capabilities for different user roles

#### Data Integrity

- **Database Constraints**: Proper foreign key relationships and cascading deletes
- **Enum Status Values**: Type-safe booking status using Prisma enums
- **Audit Logging**: All booking operations logged for security monitoring
- **Transaction Safety**: Atomic operations to prevent data corruption

### Security Improvements Implemented

#### Critical Fixes Applied

1. **Fixed Role Case Sensitivity**: Resolved authentication bypass vulnerability
2. **Added API Authorization**: Protected all booking endpoints with proper role checks
3. **Input Validation**: Comprehensive validation using Zod schemas
4. **CSRF Protection**: Added origin validation for state-changing operations
5. **Rate Limiting**: Implemented to prevent abuse of booking endpoints
6. **Audit Logging**: Security events logged for monitoring and compliance

#### Booking-Specific Security Measures

1. **Availability Verification**: Real-time availability checking prevents overbooking
2. **User Isolation**: Users can only see and modify their own bookings
3. **Staff Permissions**: Granular permissions for different staff roles
4. **Status Validation**: Only valid status transitions allowed
5. **Time-based Controls**: Prevent modifications to past bookings
6. **Resource Validation**: Verify resort/spa exists before booking creation

### Testing & Monitoring

#### Security Test Coverage

- Authentication requirement tests
- Authorization boundary tests
- Input validation tests
- Business logic security tests
- Rate limiting tests
- CSRF protection tests

#### Monitoring & Alerting

- Failed authentication attempts
- Unauthorized access attempts
- Invalid input submissions
- Rate limit violations
- Suspicious booking patterns

## Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [NextAuth.js Security](https://next-auth.js.org/configuration/options#security)
- [Next.js Security Headers](https://nextjs.org/docs/advanced-features/security-headers)
- [Prisma Security](https://www.prisma.io/docs/concepts/components/prisma-client/working-with-prismaclient/connection-management#security)
- [OWASP API Security Top 10](https://owasp.org/www-project-api-security/)
- [Zod Validation Library](https://zod.dev/)
