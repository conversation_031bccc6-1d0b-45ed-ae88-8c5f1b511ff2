# Email Verification System Documentation

## Overview

The Kuriftu Resorts email verification system provides secure, reliable email verification and password reset functionality using <PERSON><PERSON>mail<PERSON> with Gmail SMTP. This system includes comprehensive security features, rate limiting, and audit logging.

## Features

### ✅ **Implemented Features**

1. **Email Verification**
   - Secure token generation and validation
   - Beautiful HTML email templates
   - 24-hour token expiration
   - Automatic cleanup of expired tokens

2. **Password Reset**
   - Secure password reset workflow
   - 1-hour token expiration for security
   - Rate limiting to prevent abuse

3. **User Registration**
   - Email/password registration with verification
   - Strong password requirements
   - Rate limiting for registration attempts

4. **Security Features**
   - CSRF protection
   - SQL injection prevention
   - XSS protection
   - Rate limiting on all endpoints
   - Audit logging for all actions

5. **Email Configuration**
   - Gmail SMTP integration
   - TLS encryption
   - Connection verification

## Architecture

### **Database Schema**

```prisma
model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  password      String?   // For email/password authentication
  isActive      Boolean   @default(true)
  // ... other fields
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String   // Usually email address
  token      String   @unique
  expires    DateTime
  type       String   @default("email_verification")
  used       Boolean  @default(false)
  createdAt  DateTime @default(now())
}
```

### **API Endpoints**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/auth/register` | POST | User registration with email verification |
| `/api/auth/verify-email` | POST/GET | Email verification |
| `/api/auth/send-verification` | POST | Send/resend verification email |
| `/api/auth/reset-password` | POST/GET | Password reset functionality |

### **Frontend Pages**

| Page | Description |
|------|-------------|
| `/auth/register` | User registration form |
| `/auth/verify-email` | Email verification page |
| `/auth/reset-password` | Password reset form |

## Configuration

### **Environment Variables**

```env
# Email Configuration (Gmail SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### **Gmail Setup**

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
   - Use this password as `EMAIL_PASS`

## Usage

### **User Registration Flow**

1. User submits registration form
2. System creates user account (inactive)
3. Verification email sent automatically
4. User clicks verification link
5. Account activated and email verified

### **Email Verification Flow**

1. Generate secure verification token
2. Store token in database with expiration
3. Send HTML email with verification link
4. User clicks link → token validated
5. User account activated
6. Token marked as used

### **Password Reset Flow**

1. User requests password reset
2. Generate secure reset token (1-hour expiry)
3. Send reset email with secure link
4. User clicks link → validates token
5. User sets new password
6. Token marked as used

## Security Features

### **Rate Limiting**

- **Email Verification**: 3 requests per 15 minutes per email
- **Password Reset**: 3 requests per 15 minutes per email  
- **Registration**: 5 attempts per hour per IP

### **Token Security**

- **Cryptographically secure** token generation
- **Time-based expiration** (24h for verification, 1h for reset)
- **Single-use tokens** (marked as used after verification)
- **Automatic cleanup** of expired tokens

### **Input Validation**

- **Email format validation**
- **Password strength requirements**
- **SQL injection prevention** via Prisma
- **XSS protection** via input sanitization

## Testing

### **Run Email System Tests**

```bash
npm run test:email
```

### **Cleanup Expired Tokens**

```bash
npm run cleanup:tokens
```

### **Validate Environment**

```bash
npm run validate:env
```

## Monitoring & Maintenance

### **Audit Logging**

All email verification actions are logged with:
- Action type
- Timestamp
- IP address
- User agent
- Success/failure status
- Error details (if any)

### **Token Cleanup**

Run periodic cleanup of expired tokens:

```bash
# Add to cron job for production
0 2 * * * cd /path/to/app && npm run cleanup:tokens
```

### **Email Delivery Monitoring**

Monitor email delivery through:
- Gmail SMTP logs
- Application audit logs
- User feedback on email receipt

## Troubleshooting

### **Common Issues**

1. **Email not sending**
   - Check Gmail app password
   - Verify SMTP settings
   - Check firewall/network restrictions

2. **Verification links not working**
   - Check `NEXTAUTH_URL` configuration
   - Verify token expiration
   - Check database connectivity

3. **Rate limiting issues**
   - Adjust rate limits in code
   - Implement Redis for production
   - Monitor abuse patterns

### **Error Codes**

| Error | Description | Solution |
|-------|-------------|----------|
| `EAUTH` | Authentication failed | Check Gmail credentials |
| `ECONNECTION` | Connection failed | Check network/firewall |
| `ESOCKET` | Socket error | Check port/SSL settings |

## Production Deployment

### **Recommendations**

1. **Use Redis** for rate limiting storage
2. **Implement email queue** for high volume
3. **Set up monitoring** for email delivery
4. **Configure backup SMTP** provider
5. **Enable database SSL** connections
6. **Set up log aggregation** for audit trails

### **Performance Optimization**

- Use connection pooling for SMTP
- Implement email templates caching
- Optimize database queries
- Set up CDN for static assets

## Security Checklist

- ✅ Strong password requirements
- ✅ Rate limiting implemented
- ✅ Secure token generation
- ✅ Input validation and sanitization
- ✅ HTTPS enforcement
- ✅ Audit logging
- ✅ Token expiration
- ✅ Single-use tokens
- ✅ SQL injection prevention
- ✅ XSS protection

## Support

For issues or questions regarding the email verification system:

1. Check the troubleshooting section
2. Review audit logs for error details
3. Test email configuration with `npm run test:email`
4. Verify environment variables with `npm run validate:env`

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Status**: Production Ready ✅
