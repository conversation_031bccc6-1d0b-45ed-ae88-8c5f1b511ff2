"use client";

import { useState, useEffect, useCallback } from "react";
import SpaServiceCard from "@/app/components/SpaServiceCard";
import { SpaTreatment } from "@/app/types/spa";

interface DynamicSpaServiceCardsProps {
  className?: string;
  showTitle?: boolean;
  maxServices?: number;
  enableRealTimeUpdates?: boolean;
  updateInterval?: number;
  showUserPreferences?: boolean;
}

export default function DynamicSpaServiceCards({
  className = "",
  showTitle = true,
  maxServices,
  enableRealTimeUpdates = false,
  updateInterval = 30000, // 30 seconds
}: DynamicSpaServiceCardsProps) {
  const [treatments, setTreatments] = useState<SpaTreatment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch spa treatments from the API
  const fetchTreatments = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch("/api/spa", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch spa treatments: ${response.status}`);
      }

      const data = await response.json();

      // Filter only active treatments and apply maxServices limit
      let activeTreatments = data.filter(
        (treatment: SpaTreatment) => treatment.isActive
      );

      if (maxServices && maxServices > 0) {
        activeTreatments = activeTreatments.slice(0, maxServices);
      }

      setTreatments(activeTreatments);
      setLastUpdated(new Date());
    } catch (err) {
      console.error("Error fetching spa treatments:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load spa treatments"
      );
    } finally {
      setLoading(false);
    }
  }, [maxServices]);

  // Initial fetch
  useEffect(() => {
    fetchTreatments();
  }, [fetchTreatments]);

  // Set up real-time updates if enabled
  useEffect(() => {
    if (!enableRealTimeUpdates) return;

    const interval = setInterval(() => {
      fetchTreatments();
    }, updateInterval);

    return () => clearInterval(interval);
  }, [enableRealTimeUpdates, updateInterval, fetchTreatments]);

  // Handle booking action
  const handleBooking = async (treatmentId: string) => {
    try {
      // Find the treatment to get its details
      const treatment = treatments.find((t) => t.id === treatmentId);
      if (!treatment) {
        alert("Treatment not found");
        return;
      }

      // Redirect to booking page with spa treatment ID
      window.location.href = `/book?spaId=${treatmentId}`;
    } catch (error) {
      console.error("Error initiating booking:", error);
      alert("Failed to start booking process. Please try again.");
    }
  };

  // Handle view details action
  const handleViewDetails = (treatmentId: string) => {
    // Redirect to spa treatment details page
    window.location.href = `/spa/treatments/${treatmentId}`;
  };

  // Convert SpaTreatment to SpaService format for the card component
  const convertToSpaService = (treatment: SpaTreatment) => ({
    ...treatment,
    category: (treatment.category || "Wellness") as
      | "Grooming"
      | "Wellness"
      | "Beauty"
      | "Skincare"
      | "Therapy"
      | "Massage"
      | "Facial"
      | "Body Treatment",
    features: treatment.features || [],
    isActive: treatment.isActive,
    createdAt: treatment.createdAt,
    updatedAt: treatment.updatedAt,
  });

  // Loading state
  if (loading) {
    return (
      <section className={className}>
        {showTitle && (
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold mb-4">Premium Spa Services</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Indulge in our world-class spa treatments designed to rejuvenate
              your body and mind.
            </p>
          </div>
        )}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse"
            >
              <div className="w-full h-48 bg-gray-200"></div>
              <div className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  // Error state
  if (error) {
    return (
      <section className={className}>
        {showTitle && (
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold mb-4">Premium Spa Services</h2>
          </div>
        )}
        <div className="text-center py-12">
          <div className="text-red-600 mb-4">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="text-lg font-semibold mb-2">
              Unable to Load Spa Services
            </h3>
            <p className="text-gray-600 mb-4">{error}</p>
          </div>
          <button
            type="button"
            onClick={fetchTreatments}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            Try Again
          </button>
        </div>
      </section>
    );
  }

  // No treatments available
  if (treatments.length === 0) {
    return (
      <section className={className}>
        {showTitle && (
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold mb-4">Premium Spa Services</h2>
          </div>
        )}
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
            <h3 className="text-lg font-semibold mb-2">
              No Spa Services Available
            </h3>
            <p className="text-gray-600">
              Check back later for our amazing spa treatments.
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={className}>
      {showTitle && (
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold mb-4">Premium Spa Services</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Indulge in our world-class spa treatments designed to rejuvenate
            your body and mind. Experience luxury and relaxation with our
            professional spa services.
          </p>
          {lastUpdated && enableRealTimeUpdates && (
            <p className="text-sm text-gray-500 mt-2">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {treatments.map((treatment) => (
          <SpaServiceCard
            key={treatment.id}
            service={convertToSpaService(treatment)}
            onBook={handleBooking}
            onViewDetails={handleViewDetails}
            showBookButton={true}
            showDetailsButton={true}
          />
        ))}
      </div>
    </section>
  );
}
