"use client";

import Link from "next/link";
import { useState } from "react";
import { useSession, signIn, signOut } from "next-auth/react";
import { useRole } from "./auth/RoleGuard";

export default function Navbar() {
  const { data: session } = useSession();
  const { isAd<PERSON>, is<PERSON><PERSON><PERSON>, isReceptionist, isStaff } = useRole();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link
            href="/"
            className="text-xl font-bold text-blue-600 hover:text-blue-700 transition-colors"
            onClick={closeMobileMenu}
          >
            Kuriftu Resorts
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {/* Main Navigation Links */}
            <div className="flex items-center space-x-6">
              <Link
                href="/resorts"
                className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
              >
                Resorts
              </Link>

              {/* Dropdown for Services */}
              <div className="relative group">
                <button
                  type="button"
                  className="text-gray-700 hover:text-blue-600 font-medium transition-colors flex items-center"
                >
                  Services
                  <svg
                    className="ml-1 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="py-1">
                    <Link
                      href="/spa"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                    >
                      Spa & Wellness
                    </Link>
                    <Link
                      href="/resorts#experiences"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                    >
                      Experiences
                    </Link>
                    <Link
                      href="/resorts#dining"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                    >
                      Dining
                    </Link>
                    <Link
                      href="/resorts#events"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                    >
                      Events
                    </Link>
                  </div>
                </div>
              </div>

              <Link
                href="/resorts#offers"
                className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
              >
                Special Offers
              </Link>

              {session && (
                <Link
                  href="/bookings"
                  className="text-gray-700 hover:text-blue-600 font-medium transition-colors"
                >
                  My Bookings
                </Link>
              )}
            </div>

            {/* Role-based navigation */}
            <div className="flex items-center space-x-4">
              {isAdmin() && (
                <Link
                  href="/admin"
                  className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium hover:bg-purple-200 transition-colors"
                >
                  Admin Panel
                </Link>
              )}

              {isManager() && (
                <Link
                  href="/manager"
                  className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium hover:bg-blue-200 transition-colors"
                >
                  Manager
                </Link>
              )}

              {isReceptionist() && (
                <Link
                  href="/reception"
                  className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium hover:bg-green-200 transition-colors"
                >
                  Reception
                </Link>
              )}
            </div>

            {/* Authentication */}
            {session ? (
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-700">
                    Hi, {session.user?.name?.split(" ")[0]}
                  </p>
                  {isStaff() && (
                    <p className="text-xs text-gray-500 capitalize">
                      {session.user.role}
                    </p>
                  )}
                </div>
                <button
                  type="button"
                  onClick={() => signOut()}
                  className="bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-medium hover:bg-red-200 transition-colors"
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <button
                type="button"
                onClick={() => signIn("google")}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Sign In
              </button>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              onClick={toggleMobileMenu}
              className="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600"
              aria-label="Toggle mobile menu"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isMobileMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
              <Link
                href="/resorts"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md font-medium"
                onClick={closeMobileMenu}
              >
                Resorts
              </Link>
              <Link
                href="/spa"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md font-medium"
                onClick={closeMobileMenu}
              >
                Spa & Wellness
              </Link>
              <Link
                href="/resorts#experiences"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md font-medium"
                onClick={closeMobileMenu}
              >
                Experiences
              </Link>
              <Link
                href="/resorts#dining"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md font-medium"
                onClick={closeMobileMenu}
              >
                Dining
              </Link>
              <Link
                href="/resorts#events"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md font-medium"
                onClick={closeMobileMenu}
              >
                Events
              </Link>
              <Link
                href="/resorts#offers"
                className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md font-medium"
                onClick={closeMobileMenu}
              >
                Special Offers
              </Link>

              {session && (
                <Link
                  href="/bookings"
                  className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md font-medium"
                  onClick={closeMobileMenu}
                >
                  My Bookings
                </Link>
              )}

              {/* Role-based mobile navigation */}
              {isAdmin() && (
                <Link
                  href="/admin"
                  className="block px-3 py-2 text-purple-700 hover:text-purple-800 hover:bg-purple-50 rounded-md font-medium"
                  onClick={closeMobileMenu}
                >
                  Admin Panel
                </Link>
              )}

              {isManager() && (
                <Link
                  href="/manager"
                  className="block px-3 py-2 text-blue-700 hover:text-blue-800 hover:bg-blue-50 rounded-md font-medium"
                  onClick={closeMobileMenu}
                >
                  Manager Dashboard
                </Link>
              )}

              {isReceptionist() && (
                <Link
                  href="/reception"
                  className="block px-3 py-2 text-green-700 hover:text-green-800 hover:bg-green-50 rounded-md font-medium"
                  onClick={closeMobileMenu}
                >
                  Reception
                </Link>
              )}

              {/* Mobile Authentication */}
              <div className="border-t border-gray-200 pt-3 mt-3">
                {session ? (
                  <div className="space-y-2">
                    <div className="px-3 py-2">
                      <p className="text-sm font-medium text-gray-700">
                        Hi, {session.user?.name?.split(" ")[0]}
                      </p>
                      {isStaff() && (
                        <p className="text-xs text-gray-500 capitalize">
                          {session.user.role}
                        </p>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        signOut();
                        closeMobileMenu();
                      }}
                      className="block w-full text-left px-3 py-2 text-red-700 hover:text-red-800 hover:bg-red-50 rounded-md font-medium"
                    >
                      Sign Out
                    </button>
                  </div>
                ) : (
                  <button
                    type="button"
                    onClick={() => {
                      signIn("google");
                      closeMobileMenu();
                    }}
                    className="block w-full text-left px-3 py-2 text-blue-700 hover:text-blue-800 hover:bg-blue-50 rounded-md font-medium"
                  >
                    Sign In
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
