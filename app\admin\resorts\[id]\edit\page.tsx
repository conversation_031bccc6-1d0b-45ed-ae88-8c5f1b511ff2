import { prisma } from "@/lib/prisma";
import ResortForm from "@/app/components/admin/ResortForm";
import { notFound } from "next/navigation";

export default async function EditResortPage({
  params,
}: {
  params: { id: string };
}) {
  const resort = await prisma.resort.findUnique({ where: { id: params.id } });
  if (!resort) return notFound();

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Edit Resort</h1>
      <ResortForm initialData={resort} />
    </div>
  );
}
