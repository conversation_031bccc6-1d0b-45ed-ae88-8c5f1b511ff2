import { prisma } from "../lib/prisma";

async function main() {
  await prisma.resort.deleteMany();
  await prisma.spaTreatment.deleteMany();

  await prisma.resort.createMany({
    data: [
      {
        name: "Kurift<PERSON> Entoto",
        slug: "entoto",
        description: "Located on the breathtaking Entoto hills...",
        image: "/images/entoto.jpg",
      },
      {
        name: "Kuriftu Water Park",
        slug: "water-park",
        description: "Splash and relax at Africa’s largest water park...",
        image: "/images/water-park.jpg",
      },
      {
        name: "Kuriftu Awash Park",
        slug: "awash",
        description: "Experience wildlife, adventure and culture...",
        image: "/images/awash.jpg",
      },
      {
        name: "Kuriftu African Village",
        slug: "african-village",
        description: "A cultural getaway inspired by African heritage...",
        image: "/images/african-village.jpg",
      },
    ],
  });

  await prisma.spaTreatment.createMany({
    data: [
      {
        name: "Swedish Massage",
        description:
          "Relaxing full-body massage using light pressure to improve circulation and reduce stress.",
        price: 1200,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=300&fit=crop",
        category: "Massage",
        features: ["Full Body", "Relaxation", "Stress Relief"],
        isActive: true,
      },
      {
        name: "Aromatherapy Massage",
        description:
          "Therapeutic massage with essential oils for healing and stress relief.",
        price: 1500,
        duration: 75,
        image:
          "https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=300&fit=crop",
        category: "Therapy",
        features: ["Essential Oils", "Aromatherapy", "Deep Relaxation"],
        isActive: true,
      },
      {
        name: "Rejuvenating Facial",
        description:
          "Deep cleansing and moisturizing facial treatment to refresh and revitalize your skin.",
        price: 1000,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop",
        category: "Skincare",
        features: ["Deep Cleansing", "Moisturizing", "Anti-Aging"],
        isActive: true,
      },
      {
        name: "Professional Barber Service",
        description:
          "Expert haircuts, beard trimming, and traditional shaving services with premium grooming products.",
        price: 150,
        duration: 45,
        image:
          "https://images.unsplash.com/photo-1503951914875-452162b0f3f1?w=400&h=300&fit=crop",
        category: "Grooming",
        features: ["Haircut", "Beard Trim", "Hot Towel Shave"],
        isActive: true,
      },
      {
        name: "Steam Sauna Experience",
        description:
          "Relax and detoxify in our traditional steam sauna with eucalyptus aromatherapy.",
        price: 200,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
        category: "Wellness",
        features: ["Steam Therapy", "Aromatherapy", "Detoxification"],
        isActive: true,
      },
      {
        name: "Luxury Pedicure",
        description:
          "Complete foot care treatment including nail shaping, cuticle care, and relaxing foot massage.",
        price: 180,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1519415943484-9fa1873496d4?w=400&h=300&fit=crop",
        category: "Beauty",
        features: ["Nail Care", "Foot Massage", "Moisturizing"],
        isActive: true,
      },
      {
        name: "Professional Manicure",
        description:
          "Complete hand and nail care with nail shaping, cuticle treatment, and hand massage.",
        price: 120,
        duration: 45,
        image:
          "https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400&h=300&fit=crop",
        category: "Beauty",
        features: ["Nail Shaping", "Cuticle Care", "Hand Massage"],
        isActive: true,
      },
      {
        name: "Body Scrub & Wrap",
        description:
          "Exfoliating body scrub followed by nourishing body wrap for silky smooth skin.",
        price: 280,
        duration: 80,
        image:
          "https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=400&h=300&fit=crop",
        category: "Skincare",
        features: ["Exfoliation", "Body Wrap", "Moisturizing"],
        isActive: true,
      },
    ],
  });

  // Seed Experiences
  console.log("Seeding experiences...");
  await prisma.experience.createMany({
    data: [
      {
        name: "Lake Tana Boat Tour",
        description:
          "Explore the largest lake in Ethiopia with visits to ancient monasteries on secluded islands.",
        price: 800,
        duration: 240,
        image:
          "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop",
        category: "Cultural",
        features: ["Boat Tour", "Monastery Visits", "Local Guide"],
        difficulty: "Easy",
        minAge: 8,
        maxCapacity: 12,
        location: "Lake Tana",
        isActive: true,
      },
      {
        name: "Blue Nile Falls Adventure",
        description:
          "Witness the spectacular Blue Nile Falls, known locally as 'Tis Issat' or 'Smoking Water'.",
        price: 600,
        duration: 180,
        image:
          "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
        category: "Adventure",
        features: ["Hiking", "Photography", "Natural Wonder"],
        difficulty: "Moderate",
        minAge: 12,
        maxCapacity: 15,
        location: "Blue Nile Falls",
        isActive: true,
      },
      {
        name: "Traditional Coffee Ceremony",
        description:
          "Experience the authentic Ethiopian coffee ceremony with local families.",
        price: 300,
        duration: 90,
        image:
          "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop",
        category: "Cultural",
        features: ["Coffee Tasting", "Cultural Exchange", "Traditional Music"],
        difficulty: "Easy",
        minAge: 5,
        maxCapacity: 8,
        location: "Local Village",
        isActive: true,
      },
      {
        name: "Water Sports Package",
        description:
          "Enjoy kayaking, jet skiing, and water skiing on the pristine waters.",
        price: 1200,
        duration: 180,
        image:
          "https://images.unsplash.com/photo-1530549387789-4c1017266635?w=400&h=300&fit=crop",
        category: "Water Sports",
        features: ["Kayaking", "Jet Skiing", "Water Skiing"],
        difficulty: "Moderate",
        minAge: 16,
        maxCapacity: 6,
        location: "Resort Marina",
        isActive: true,
      },
    ],
  });

  // Seed Wellness Services
  console.log("Seeding wellness services...");
  await prisma.wellnessService.createMany({
    data: [
      {
        name: "Morning Yoga Class",
        description:
          "Start your day with energizing yoga sessions overlooking the lake.",
        price: 300,
        duration: 60,
        image:
          "https://images.unsplash.com/photo-1506126613408-eca07ce68773?w=400&h=300&fit=crop",
        category: "Yoga",
        features: ["Outdoor Setting", "All Levels", "Equipment Provided"],
        instructor: "Sarah Johnson",
        equipment: ["Yoga Mats", "Blocks", "Straps"],
        maxCapacity: 15,
        isActive: true,
      },
      {
        name: "Fitness Training Session",
        description:
          "Personal training sessions with certified fitness instructors.",
        price: 500,
        duration: 45,
        image:
          "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
        category: "Fitness",
        features: ["Personal Training", "Custom Workout", "Nutrition Advice"],
        instructor: "Mike Thompson",
        equipment: ["Weights", "Cardio Equipment", "Resistance Bands"],
        maxCapacity: 1,
        isActive: true,
      },
      {
        name: "Meditation & Mindfulness",
        description:
          "Guided meditation sessions for inner peace and relaxation.",
        price: 250,
        duration: 30,
        image:
          "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
        category: "Meditation",
        features: ["Guided Sessions", "Breathing Techniques", "Stress Relief"],
        instructor: "Dr. Amara Bekele",
        equipment: ["Meditation Cushions", "Sound Bowls"],
        maxCapacity: 20,
        isActive: true,
      },
    ],
  });

  // Seed Dining Options
  console.log("Seeding dining options...");
  await prisma.diningOption.createMany({
    data: [
      {
        name: "Lakeside Restaurant",
        description:
          "Fine dining with panoramic lake views featuring Ethiopian and international cuisine.",
        image:
          "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=300&fit=crop",
        category: "Restaurant",
        cuisine: "Ethiopian & International",
        priceRange: "$$$",
        features: ["Lake View", "Fine Dining", "Wine Selection"],
        openingHours: "6:00 AM - 11:00 PM",
        location: "Main Building",
        capacity: 80,
        isActive: true,
      },
      {
        name: "Poolside Bar & Grill",
        description:
          "Casual dining and refreshing drinks by the pool with grilled specialties.",
        image:
          "https://images.unsplash.com/photo-1544148103-0773bf10d330?w=400&h=300&fit=crop",
        category: "Bar",
        cuisine: "Grilled & Light Meals",
        priceRange: "$$",
        features: ["Pool View", "Outdoor Seating", "Live Music"],
        openingHours: "11:00 AM - 1:00 AM",
        location: "Pool Area",
        capacity: 50,
        isActive: true,
      },
      {
        name: "Coffee House",
        description:
          "Traditional Ethiopian coffee and light snacks in a cozy atmosphere.",
        image:
          "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop",
        category: "Cafe",
        cuisine: "Coffee & Pastries",
        priceRange: "$",
        features: ["Traditional Coffee", "Local Pastries", "Cozy Atmosphere"],
        openingHours: "5:00 AM - 10:00 PM",
        location: "Lobby Level",
        capacity: 30,
        isActive: true,
      },
    ],
  });

  // Seed Events
  console.log("Seeding events...");
  await prisma.event.createMany({
    data: [
      {
        name: "Lakeside Wedding Package",
        description:
          "Create your perfect wedding day with stunning lake views and professional service.",
        price: 15000,
        image:
          "https://images.unsplash.com/photo-1519225421980-715cb0215aed?w=400&h=300&fit=crop",
        category: "Wedding",
        features: [
          "Ceremony Setup",
          "Reception Venue",
          "Catering",
          "Photography",
        ],
        venue: "Lakeside Pavilion",
        maxCapacity: 150,
        duration: 8,
        isActive: true,
      },
      {
        name: "Corporate Conference",
        description:
          "Professional meeting facilities with modern technology and catering services.",
        price: 5000,
        image:
          "https://images.unsplash.com/photo-1511578314322-379afb476865?w=400&h=300&fit=crop",
        category: "Corporate",
        features: ["AV Equipment", "Catering", "WiFi", "Parking"],
        venue: "Conference Center",
        maxCapacity: 100,
        duration: 8,
        isActive: true,
      },
      {
        name: "Birthday Celebration",
        description:
          "Memorable birthday parties with customized decorations and entertainment.",
        price: 2500,
        image:
          "https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=400&h=300&fit=crop",
        category: "Birthday",
        features: ["Decorations", "Entertainment", "Cake", "Photography"],
        venue: "Party Hall",
        maxCapacity: 50,
        duration: 4,
        isActive: true,
      },
    ],
  });

  // Seed Special Offers
  console.log("Seeding special offers...");
  await prisma.specialOffer.createMany({
    data: [
      {
        name: "Weekend Getaway Package",
        description:
          "Two nights accommodation with breakfast, spa treatment, and lake activities.",
        image:
          "https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=300&fit=crop",
        category: "Package",
        originalPrice: 8000,
        discountPrice: 6000,
        discountPercent: 25,
        features: [
          "2 Nights Stay",
          "Breakfast",
          "Spa Treatment",
          "Lake Activities",
        ],
        validFrom: new Date("2024-01-01"),
        validUntil: new Date("2024-12-31"),
        terms: "Valid for weekends only. Subject to availability.",
        isActive: true,
      },
      {
        name: "Early Bird Special",
        description: "Book 30 days in advance and save 20% on accommodation.",
        image:
          "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop",
        category: "Discount",
        discountPercent: 20,
        features: ["20% Discount", "Free WiFi", "Late Checkout"],
        validFrom: new Date("2024-01-01"),
        validUntil: new Date("2024-12-31"),
        terms: "Must book 30 days in advance. Non-refundable.",
        isActive: true,
      },
      {
        name: "Honeymoon Package",
        description:
          "Romantic getaway with champagne, couples spa treatment, and private dinner.",
        image:
          "https://images.unsplash.com/photo-1522673607200-164d1b6ce486?w=400&h=300&fit=crop",
        category: "Package",
        originalPrice: 12000,
        discountPrice: 9000,
        discountPercent: 25,
        features: [
          "Champagne",
          "Couples Spa",
          "Private Dinner",
          "Room Upgrade",
        ],
        validFrom: new Date("2024-01-01"),
        validUntil: new Date("2024-12-31"),
        terms: "Valid for newlyweds with marriage certificate.",
        isActive: true,
      },
    ],
  });

  await prisma.user.update({
    where: { email: "<EMAIL>" },
    data: { role: "admin" },
  });

  console.log("🌱 Seeded successfully!");
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
