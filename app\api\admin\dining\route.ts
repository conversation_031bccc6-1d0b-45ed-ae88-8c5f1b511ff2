import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function POST(req: Request) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult; // Return error response
  }

  try {
    const data = await req.json();

    // Basic input validation
    if (!data.name || !data.description) {
      return NextResponse.json(
        { error: "Missing required fields: name, description" },
        { status: 400 }
      );
    }

    const diningOption = await prisma.diningOption.create({ data });
    return NextResponse.json(diningOption);
  } catch (error) {
    console.error("Error creating dining option:", error);
    return NextResponse.json(
      { error: "Failed to create dining option" },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Check authorization
  const authResult = await requireRole(["admin", "manager"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const diningOptions = await prisma.diningOption.findMany({
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(diningOptions);
  } catch (error) {
    console.error("Error fetching dining options:", error);
    return NextResponse.json(
      { error: "Failed to fetch dining options" },
      { status: 500 }
    );
  }
}
