import { Suspense } from "react";
import { prisma } from "@/lib/prisma";
import { analytics } from "@/lib/analytics";
import DashboardLayout from "@/app/components/dashboard/DashboardLayout";
import StatCard, { StatIcons } from "@/app/components/dashboard/StatCard";
import SimpleChart from "@/app/components/dashboard/SimpleChart";
import QuickActions, {
  ActionIcons,
} from "@/app/components/dashboard/QuickActions";
import DataTable from "@/app/components/dashboard/DataTable";

async function getManagerDashboardData() {
  const [
    bookings,
    resorts,
    spaTreatments,
    bookingAnalytics,
    bookingPatterns,
    recentBookings,
  ] = await Promise.all([
    prisma.booking.findMany({
      where: {
        status: { in: ["PENDING", "CONFIRMED"] },
      },
    }),
    prisma.resort.findMany(),
    prisma.spaTreatment.findMany(),
    analytics.getBookingAnalytics(),
    analytics.getBookingPatterns(),
    prisma.booking.findMany({
      include: { resort: true, spaTreatment: true },
      orderBy: { createdAt: "desc" },
      take: 8,
    }),
  ]);

  return {
    bookings,
    resorts,
    spaTreatments,
    bookingAnalytics,
    bookingPatterns,
    recentBookings,
  };
}

function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border p-6">
            <div className="animate-pulse">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                <div className="ml-4 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

async function DashboardContent() {
  const data = await getManagerDashboardData();

  const quickActions = [
    {
      id: "view-bookings",
      title: "Manage Bookings",
      description: "View and manage all bookings",
      icon: <ActionIcons.Calendar />,
      href: "/dashboard/bookings",
      color: "blue" as const,
      badge:
        data.bookings.filter((b) => b.status === "PENDING").length || undefined,
    },
    {
      id: "view-resorts",
      title: "Resort Management",
      description: "Manage resort listings",
      icon: <ActionIcons.View />,
      href: "/dashboard/resorts",
      color: "green" as const,
    },
    {
      id: "view-spa",
      title: "Spa Management",
      description: "Manage spa treatments",
      icon: <ActionIcons.View />,
      href: "/dashboard/spa",
      color: "purple" as const,
    },
    {
      id: "reports",
      title: "Generate Reports",
      description: "View analytics and reports",
      icon: <ActionIcons.Report />,
      href: "/dashboard/reports",
      color: "indigo" as const,
    },
  ];

  const bookingStatusData = Object.entries(
    data.bookingAnalytics.bookingsByStatus
  ).map(([status, count]) => ({
    label: status.charAt(0) + status.slice(1).toLowerCase(),
    value: count,
  }));

  const popularResortsData = data.bookingAnalytics.popularResorts.map(
    (resort) => ({
      label: resort.name,
      value: resort.bookings,
    })
  );

  const recentBookingsColumns = [
    {
      key: "userEmail" as keyof (typeof data.recentBookings)[0],
      title: "Customer",
      sortable: true,
    },
    {
      key: "type" as keyof (typeof data.recentBookings)[0],
      title: "Type",
      render: (value: any, row: (typeof data.recentBookings)[0]) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            row.resort
              ? "bg-blue-100 text-blue-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {row.resort ? "Resort" : "Spa"}
        </span>
      ),
    },
    {
      key: "service" as keyof (typeof data.recentBookings)[0],
      title: "Service",
      render: (value: any, row: (typeof data.recentBookings)[0]) =>
        row.resort?.name || row.spaTreatment?.name || "N/A",
    },
    {
      key: "status" as keyof (typeof data.recentBookings)[0],
      title: "Status",
      render: (value: string) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            value === "CONFIRMED"
              ? "bg-green-100 text-green-800"
              : value === "PENDING"
              ? "bg-yellow-100 text-yellow-800"
              : value === "CANCELLED"
              ? "bg-red-100 text-red-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {value}
        </span>
      ),
    },
    {
      key: "checkIn" as keyof (typeof data.recentBookings)[0],
      title: "Check-in",
      render: (value: Date | null) =>
        value ? new Date(value).toLocaleDateString() : "N/A",
      sortable: true,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Active Bookings"
          value={data.bookings.length}
          icon={<StatIcons.Bookings />}
          color="blue"
          change={{
            value: 15,
            type: "increase",
            period: "last week",
          }}
        />
        <StatCard
          title="Pending Approvals"
          value={data.bookings.filter((b) => b.status === "PENDING").length}
          icon={<StatIcons.Activity />}
          color="yellow"
        />
        <StatCard
          title="Available Resorts"
          value={data.resorts.length}
          icon={<StatIcons.Resorts />}
          color="green"
        />
        <StatCard
          title="Spa Treatments"
          value={data.spaTreatments.length}
          icon={<StatIcons.Spa />}
          color="purple"
        />
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Occupancy Rate"
          value={`${data.bookingAnalytics.occupancyRate.toFixed(1)}%`}
          icon={<StatIcons.Occupancy />}
          color="indigo"
          change={{
            value: 5,
            type: "increase",
            period: "last month",
          }}
        />
        <StatCard
          title="Cancellation Rate"
          value={`${data.bookingAnalytics.cancellationRate.toFixed(1)}%`}
          icon={<StatIcons.Performance />}
          color={data.bookingAnalytics.cancellationRate > 10 ? "red" : "green"}
        />
        <StatCard
          title="Avg Lead Time"
          value={`${data.bookingPatterns.averageLeadTime} days`}
          icon={<StatIcons.Activity />}
          color="blue"
        />
        <StatCard
          title="Total Revenue"
          value="ETB 125,000"
          icon={<StatIcons.Revenue />}
          color="green"
          change={{
            value: 12,
            type: "increase",
            period: "last month",
          }}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SimpleChart
          data={bookingStatusData}
          type="pie"
          title="Booking Status Distribution"
          height={300}
          width={400}
        />
        <SimpleChart
          data={popularResortsData}
          type="bar"
          title="Most Popular Resorts"
          height={300}
          width={400}
        />
      </div>

      {/* Quick Actions */}
      <QuickActions
        title="Management Actions"
        actions={quickActions}
        columns={4}
      />

      {/* Recent Bookings */}
      <DataTable
        data={data.recentBookings}
        columns={recentBookingsColumns}
        searchable
        searchPlaceholder="Search bookings..."
        pagination={{ pageSize: 8 }}
        className="mt-6"
      />
    </div>
  );
}

export default function ManagerDashboard() {
  return (
    <DashboardLayout
      title="Manager Dashboard"
      subtitle="Operational overview and booking management"
      breadcrumbs={[
        { label: "Manager", href: "/manager" },
        { label: "Dashboard" },
      ]}
    >
      <Suspense fallback={<DashboardSkeleton />}>
        <DashboardContent />
      </Suspense>
    </DashboardLayout>
  );
}
