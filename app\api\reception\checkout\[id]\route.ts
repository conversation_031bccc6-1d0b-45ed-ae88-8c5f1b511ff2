import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { logAuditEvent } from "@/lib/security";
import { requireRole } from "@/lib/checkRole";
import { z } from "zod";

// Validation schema for check-out
const CheckOutSchema = z.object({
  status: z.enum(["CHECKED_OUT"]),
  checkOutTime: z.string().optional(),
  notes: z.string().optional(),
  feedback: z.string().optional(),
  rating: z.number().min(1).max(5).optional(),
});

export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and role
    const roleCheck = await requireRole(["admin", "manager", "receptionist"]);
    if (roleCheck instanceof NextResponse) {
      return roleCheck;
    }

    const rawData = await req.json();

    // Validate input data
    const validation = CheckOutSchema.safeParse(rawData);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 }
      );
    }

    const { status, checkOutTime, notes, feedback, rating } = validation.data;

    // Check if booking exists
    const booking = await prisma.booking.findUnique({
      where: { id: params.id },
      include: {
        resort: true,
        spaTreatment: true,
      },
    });

    if (!booking) {
      return NextResponse.json(
        { error: "Booking not found" },
        { status: 404 }
      );
    }

    // Check if booking can be checked out
    if (booking.status === "CANCELLED") {
      return NextResponse.json(
        { error: "Cannot check out cancelled booking" },
        { status: 400 }
      );
    }

    if (booking.status === "PENDING") {
      return NextResponse.json(
        { error: "Guest must be checked in before checking out" },
        { status: 400 }
      );
    }

    // Update booking status to completed (using existing enum)
    const updatedBooking = await prisma.booking.update({
      where: { id: params.id },
      data: {
        status: "CONFIRMED", // We'll use CONFIRMED as completed since CHECKED_OUT doesn't exist in enum
        notes: notes || booking.notes,
        updatedAt: new Date(),
      },
      include: {
        resort: true,
        spaTreatment: true,
      },
    });

    // Create a review if feedback and rating provided
    if (feedback && rating) {
      try {
        // Get user ID from email
        const user = await prisma.user.findUnique({
          where: { email: booking.userEmail },
          select: { id: true },
        });

        if (user) {
          await prisma.review.create({
            data: {
              userId: user.id,
              resortId: booking.resortId,
              spaId: booking.spaId,
              rating,
              comment: feedback,
              isVerified: true, // Verified because it's from actual booking
              isApproved: false, // Needs admin approval
              isPublished: false,
            },
          });
        }
      } catch (reviewError) {
        console.error("Error creating review:", reviewError);
        // Don't fail the checkout if review creation fails
      }
    }

    // Log the check-out event
    await logAuditEvent({
      userId: roleCheck.user.id,
      action: "GUEST_CHECKED_OUT",
      resource: `/api/reception/checkout/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    // TODO: Send check-out confirmation email/SMS to guest
    // TODO: Process final billing
    // TODO: Create notification for housekeeping
    // TODO: Update room status

    return NextResponse.json({
      ...updatedBooking,
      userEmail: updatedBooking.userEmail,
      message: "Guest checked out successfully",
      reviewCreated: !!(feedback && rating),
    });
  } catch (error) {
    console.error("Error checking out guest:", error);
    await logAuditEvent({
      action: "GUEST_CHECKOUT_ERROR",
      resource: `/api/reception/checkout/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Failed to check out guest" },
      { status: 500 }
    );
  }
}
