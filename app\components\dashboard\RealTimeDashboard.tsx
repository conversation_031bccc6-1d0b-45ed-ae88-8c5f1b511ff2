"use client";

import React, { useState } from "react";
import { useRealTimeData } from "./useRealTimeData";
import StatCard, { StatIcons } from "./StatCard";
import InteractiveChart from "./InteractiveChart";
import ErrorBoundary from "./ErrorBoundary";
import { SystemMetrics } from "./types";

interface RealTimeDashboardProps {
  updateInterval?: number;
  className?: string;
}

export default function RealTimeDashboard({
  updateInterval = 30000, // 30 seconds
  className = "",
}: RealTimeDashboardProps) {
  const [isLive, setIsLive] = useState(true);

  // Mock data fetcher for demonstration
  const fetchLiveData = async (): Promise<SystemMetrics> => {
    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Generate realistic mock data with some variation
    const baseResponseTime = 150;
    const variation = Math.random() * 100 - 50; // -50 to +50ms variation

    return {
      responseTime: Math.max(50, Math.round(baseResponseTime + variation)),
      errorRate: Math.max(0, Math.round(Math.random() * 2 * 100) / 100), // 0-2%
      activeUsers: Math.round(50 + Math.random() * 20), // 50-70 users
      databaseConnections: Math.round(10 + Math.random() * 5), // 10-15 connections
      memoryUsage: Math.round(60 + Math.random() * 20), // 60-80%
      cpuUsage: Math.round(30 + Math.random() * 40), // 30-70%
      uptime: 99.9,
      requestsPerMinute: Math.round(100 + Math.random() * 50), // 100-150 RPM
      errorCount: Math.round(Math.random() * 5), // 0-5 errors
      lastUpdated: new Date().toISOString(),
    };
  };

  const {
    data: liveMetrics,
    loading,
    error,
    lastUpdated,
    isConnected,
    refresh,
    retry,
  } = useRealTimeData(fetchLiveData, {
    enabled: isLive,
    updateInterval,
    onUpdate: () => {
      // No-op: removed lastUpdateTime state
      if (error) {
        console.error("Real-time data error:", error);
      }
    },
  });

  // Generate chart data from live metrics
  const generateChartData = () => {
    if (!liveMetrics) return [];

    return [
      { label: "CPU", value: liveMetrics.cpuUsage, color: "#3B82F6" },
      { label: "Memory", value: liveMetrics.memoryUsage, color: "#10B981" },
      {
        label: "Response Time",
        value: Math.min(100, liveMetrics.responseTime / 5),
        color: "#F59E0B",
      },
    ];
  };

  const handleToggleLive = () => {
    setIsLive(!isLive);
  };

  const getStatusColor = (
    value: number,
    thresholds: { good: number; warning: number }
  ) => {
    if (value <= thresholds.good) return "green";
    if (value <= thresholds.warning) return "yellow";
    return "red";
  };

  return (
    <ErrorBoundary>
      <div className={`space-y-6 ${className}`}>
        {/* Real-time Controls */}
        <div className="flex items-center justify-between bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  isConnected && isLive
                    ? "bg-green-500 animate-pulse"
                    : "bg-gray-400"
                }`}
                aria-label={
                  isConnected && isLive
                    ? "Connected and live"
                    : "Disconnected or paused"
                }
              />
              <span className="text-sm font-medium text-gray-700">
                {isConnected && isLive ? "Live" : "Paused"}
              </span>
            </div>

            {lastUpdated && (
              <span className="text-xs text-gray-500">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={handleToggleLive}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                isLive
                  ? "bg-red-100 text-red-700 hover:bg-red-200"
                  : "bg-green-100 text-green-700 hover:bg-green-200"
              }`}
              aria-label={isLive ? "Pause live updates" : "Resume live updates"}
            >
              {isLive ? "Pause" : "Resume"}
            </button>

            <button
              type="button"
              onClick={refresh}
              disabled={loading}
              className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md text-sm font-medium hover:bg-blue-200 disabled:opacity-50 transition-colors"
              aria-label="Refresh data manually"
            >
              {loading ? "Refreshing..." : "Refresh"}
            </button>

            {error && (
              <button
                type="button"
                onClick={retry}
                className="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-md text-sm font-medium hover:bg-yellow-200 transition-colors"
                aria-label="Retry failed connection"
              >
                Retry
              </button>
            )}
          </div>
        </div>

        {/* Live Metrics */}
        {liveMetrics && (
          <div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            role="region"
            aria-label="Live system metrics"
          >
            <StatCard
              title="Response Time"
              value={`${liveMetrics.responseTime}ms`}
              icon={<StatIcons.Performance />}
              color={getStatusColor(liveMetrics.responseTime, {
                good: 200,
                warning: 500,
              })}
              loading={loading}
              error={error?.message}
              ariaLabel={`Response time: ${liveMetrics.responseTime} milliseconds`}
              description="Current API response time"
            />

            <StatCard
              title="Active Users"
              value={liveMetrics.activeUsers}
              icon={<StatIcons.Users />}
              color="blue"
              loading={loading}
              error={error?.message}
              ariaLabel={`Active users: ${liveMetrics.activeUsers}`}
              description="Users currently online"
            />

            <StatCard
              title="Error Rate"
              value={`${liveMetrics.errorRate}%`}
              icon={<StatIcons.Activity />}
              color={getStatusColor(liveMetrics.errorRate, {
                good: 1,
                warning: 5,
              })}
              loading={loading}
              error={error?.message}
              ariaLabel={`Error rate: ${liveMetrics.errorRate} percent`}
              description="Current error rate"
            />

            <StatCard
              title="Requests/Min"
              value={liveMetrics.requestsPerMinute}
              icon={<StatIcons.Activity />}
              color="purple"
              loading={loading}
              error={error?.message}
              ariaLabel={`Requests per minute: ${liveMetrics.requestsPerMinute}`}
              description="Current request volume"
            />
          </div>
        )}

        {/* Live System Usage Chart */}
        {liveMetrics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InteractiveChart
              data={generateChartData()}
              type="bar"
              title="System Resource Usage"
              height={300}
              width={400}
              interactive={true}
              showValues={true}
              showGrid={true}
              loading={loading}
              error={error?.message}
              ariaLabel="Real-time system resource usage chart"
              description="Shows current CPU, memory usage, and normalized response time"
            />

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                System Health
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">CPU Usage</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-500 ${
                          liveMetrics.cpuUsage > 80
                            ? "bg-red-500"
                            : liveMetrics.cpuUsage > 60
                            ? "bg-yellow-500"
                            : "bg-green-500"
                        }`}
                        style={{ width: `${liveMetrics.cpuUsage}%` }}
                        aria-label={`CPU usage: ${liveMetrics.cpuUsage}%`}
                      />
                    </div>
                    <span className="text-sm font-medium">
                      {liveMetrics.cpuUsage}%
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Memory Usage</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-500 ${
                          liveMetrics.memoryUsage > 80
                            ? "bg-red-500"
                            : liveMetrics.memoryUsage > 60
                            ? "bg-yellow-500"
                            : "bg-green-500"
                        }`}
                        style={{ width: `${liveMetrics.memoryUsage}%` }}
                        aria-label={`Memory usage: ${liveMetrics.memoryUsage}%`}
                      />
                    </div>
                    <span className="text-sm font-medium">
                      {liveMetrics.memoryUsage}%
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Database Connections
                  </span>
                  <span className="text-sm font-medium">
                    {liveMetrics.databaseConnections}/20
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Uptime</span>
                  <span className="text-sm font-medium text-green-600">
                    {liveMetrics.uptime}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !liveMetrics && (
          <div
            className="bg-red-50 border border-red-200 rounded-lg p-6 text-center"
            role="alert"
          >
            <h3 className="text-lg font-semibold text-red-800 mb-2">
              Connection Error
            </h3>
            <p className="text-red-600 mb-4">{error.message}</p>
            <button
              type="button"
              onClick={retry}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
}
