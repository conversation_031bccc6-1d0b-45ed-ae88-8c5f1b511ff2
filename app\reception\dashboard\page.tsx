import { prisma } from "@/lib/prisma";
import { Suspense } from "react";
import Link from "next/link";
import { StatCardSkeleton } from "@/app/components/ui/SkeletonComponents";
import { EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";
import CheckInOutManager from "@/app/components/reception/CheckInOutManager";
import ServiceScheduler from "@/app/components/reception/ServiceScheduler";
import StaffTaskManager from "@/app/components/reception/StaffTaskManager";

// TypeScript interfaces
interface ReceptionDashboardProps {
  searchParams?: {
    date?: string;
  };
}

interface Booking {
  id: string;
  userEmail: string;
  checkIn: Date;
  checkOut: Date;
  status: string;
  resort?: { name: string } | null;
  spaTreatment?: { name: string } | null;
}

// Dashboard card component
function DashboardCard({
  title,
  value,
  icon,
  color = "blue",
  href,
}: {
  title: string;
  value: number | string;
  icon: string;
  color?: "blue" | "green" | "yellow" | "red" | "purple";
  href?: string;
}) {
  const colorClasses = {
    blue: "text-blue-600",
    green: "text-green-600",
    yellow: "text-yellow-600",
    red: "text-red-600",
    purple: "text-purple-600",
  }[color];

  const content = (
    <div className="bg-white rounded-lg shadow border p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <div className={`text-2xl font-bold ${colorClasses}`}>{value}</div>
          <div className="text-gray-600">{title}</div>
        </div>
        <div className="text-3xl">{icon}</div>
      </div>
    </div>
  );

  return href ? (
    <Link href={href} aria-label={`View ${title}`}>
      {content}
    </Link>
  ) : (
    content
  );
}

// Booking list component
function BookingsList({ bookings }: { bookings: Booking[] }) {
  if (bookings.length === 0) {
    return (
      <EmptyState
        title="No Bookings Today"
        message="There are no bookings scheduled for today."
        icon={<div className="text-gray-400 text-6xl mb-4">📅</div>}
      />
    );
  }

  return (
    <div className="bg-white rounded-lg shadow border">
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          Today's Bookings
        </h3>
      </div>
      <div className="p-6">
        <div className="space-y-4">
          {bookings.map((booking) => (
            <div
              key={booking.id}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div>
                <div className="font-medium text-gray-900">
                  {booking.userEmail}
                </div>
                <div className="text-sm text-gray-600">
                  {booking.resort?.name || booking.spaTreatment?.name}
                </div>
                <div className="text-sm text-gray-500">
                  Check-in: {new Date(booking.checkIn).toLocaleDateString()}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${
                    booking.status === "CONFIRMED"
                      ? "bg-green-100 text-green-800"
                      : booking.status === "PENDING"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {booking.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Loading component
function DashboardLoading() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="h-8 bg-gray-200 rounded w-64 mb-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {Array.from({ length: 4 }).map((_, i) => (
          <StatCardSkeleton key={i} />
        ))}
      </div>
    </div>
  );
}

async function getDashboardData() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const [allBookings, todayBookings] = await Promise.all([
    prisma.booking.findMany({
      include: { resort: true, spaTreatment: true },
      orderBy: { createdAt: "desc" },
      take: 10,
    }),
    prisma.booking.findMany({
      where: {
        checkIn: {
          gte: today,
          lt: tomorrow,
        },
      },
      include: { resort: true, spaTreatment: true },
      orderBy: { checkIn: "asc" },
    }),
  ]);

  const stats = {
    todayCheckIns: todayBookings.filter((b) => b.status === "CONFIRMED").length,
    todayCheckOuts: 0, // This would need additional logic
    currentGuests: allBookings.filter((b) => b.status === "CONFIRMED").length,
    pendingBookings: allBookings.filter((b) => b.status === "PENDING").length,
  };

  return { bookings: allBookings, todayBookings, stats };
}

async function DashboardContent() {
  const { bookings, todayBookings, stats } = await getDashboardData();

  return (
    <>
      {/* Today's Overview */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Today's Overview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <DashboardCard
            title="Check-ins Today"
            value={stats.todayCheckIns}
            icon="🏨"
            color="green"
          />

          <DashboardCard
            title="Current Guests"
            value={stats.currentGuests}
            icon="👥"
            color="purple"
          />

          <DashboardCard
            title="Pending Bookings"
            value={stats.pendingBookings}
            icon="⏳"
            color="yellow"
          />

          <DashboardCard
            title="Total Bookings"
            value={bookings.length}
            icon="📅"
            color="blue"
          />
        </div>
      </div>

      {/* Today's Bookings */}
      <div className="mb-8">
        <BookingsList bookings={todayBookings} />
      </div>

      {/* Enhanced Reception Features */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8">
        <CheckInOutManager />
        <ServiceScheduler />
      </div>

      <div className="mb-8">
        <StaffTaskManager />
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Link
            href="/admin/bookings"
            className="block p-6 bg-white rounded-lg shadow border hover:shadow-md transition-shadow"
            aria-label="Manage all bookings"
          >
            <div className="text-3xl mb-3">📅</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Manage Bookings
            </h3>
            <p className="text-gray-600">View and update all reservations</p>
          </Link>

          <Link
            href="/admin/resorts"
            className="block p-6 bg-white rounded-lg shadow border hover:shadow-md transition-shadow"
            aria-label="Manage resorts"
          >
            <div className="text-3xl mb-3">🏨</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Resort Management
            </h3>
            <p className="text-gray-600">Manage resort properties</p>
          </Link>

          <Link
            href="/admin/spa"
            className="block p-6 bg-white rounded-lg shadow border hover:shadow-md transition-shadow"
            aria-label="Manage spa services"
          >
            <div className="text-3xl mb-3">🧘‍♀️</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Spa Services
            </h3>
            <p className="text-gray-600">Manage spa treatments</p>
          </Link>

          <Link
            href="/admin/reviews"
            className="block p-6 bg-white rounded-lg shadow border hover:shadow-md transition-shadow"
            aria-label="View customer feedback"
          >
            <div className="text-3xl mb-3">⭐</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Customer Feedback
            </h3>
            <p className="text-gray-600">View reviews and testimonials</p>
          </Link>
        </div>
      </div>
    </>
  );
}

export default async function ReceptionDashboard({
  searchParams,
}: ReceptionDashboardProps) {
  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Reception Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Manage guest services and daily operations
          </p>
        </div>

        <Suspense fallback={<DashboardLoading />}>
          <DashboardContent />
        </Suspense>
      </div>
    </ErrorBoundary>
  );
}
