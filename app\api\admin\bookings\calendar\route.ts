import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/auth-utils";

export async function GET(request: Request) {
  // Check authorization
  const authResult = await requireRole(["admin", "manager", "receptionist"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const roomId = searchParams.get("roomId");
    const resortId = searchParams.get("resortId");

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: "Start date and end date are required" },
        { status: 400 }
      );
    }

    // Build where clause
    const whereClause: any = {
      checkIn: {
        lte: new Date(endDate)
      },
      checkOut: {
        gte: new Date(startDate)
      },
      status: {
        in: ["PENDING", "CONFIRMED"]
      }
    };

    // Add room or resort filter
    if (roomId) {
      whereClause.roomId = roomId;
    } else if (resortId) {
      whereClause.OR = [
        { resortId: resortId },
        { 
          room: {
            resortId: resortId
          }
        }
      ];
    }

    const bookings = await prisma.booking.findMany({
      where: whereClause,
      select: {
        id: true,
        checkIn: true,
        checkOut: true,
        status: true,
        userEmail: true,
        notes: true,
        room: {
          select: {
            id: true,
            name: true,
            type: true,
            resort: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        resort: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        checkIn: "asc"
      }
    });

    return NextResponse.json(bookings);
  } catch (error) {
    console.error("Error fetching calendar bookings:", error);
    return NextResponse.json(
      { error: "Failed to fetch calendar data" },
      { status: 500 }
    );
  }
}
