import { prisma } from "@/lib/prisma";
import Link from "next/link";
import Image from "next/image";
import { deleteResort } from "@/lib/actions";
import { Suspense } from "react";
import type { Resort } from "@/app/types/resort";

// Loading skeleton component
function ResortCardSkeleton() {
  return (
    <div className="border rounded-xl p-4 animate-pulse">
      <div className="w-full h-40 bg-gray-200 rounded mb-3"></div>
      <div className="h-6 bg-gray-200 rounded mb-2"></div>
      <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
      <div className="flex justify-between">
        <div className="h-4 bg-gray-200 rounded w-12"></div>
        <div className="h-4 bg-gray-200 rounded w-16"></div>
      </div>
    </div>
  );
}

// Resort card component
function ResortCard({ resort }: { resort: Resort }) {
  return (
    <div className="border rounded-xl p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="relative mb-3">
        <Image
          src={resort.image}
          alt={resort.name}
          width={400}
          height={160}
          className="w-full h-40 object-cover rounded"
          loading="lazy"
        />
        <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
          {resort.location}
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900">{resort.name}</h3>
        <p className="text-sm text-gray-600 line-clamp-2">
          {resort.description.substring(0, 100)}...
        </p>
        <p className="text-xs text-gray-500">
          Created: {new Date(resort.createdAt).toLocaleDateString()}
        </p>
      </div>

      <div className="mt-4 space-y-2">
        <div className="flex justify-between items-center">
          <Link
            href={`/admin/resorts/${resort.id}/edit`}
            className="text-blue-600 hover:text-blue-800 font-medium text-sm"
          >
            Edit Resort
          </Link>
          <div className="flex gap-2">
            <Link
              href={`/resorts/${resort.slug}`}
              target="_blank"
              className="text-green-600 hover:text-green-800 font-medium text-sm"
            >
              View
            </Link>
            <form action={deleteResort} className="inline">
              <input type="hidden" name="id" value={resort.id} />
              <button
                type="submit"
                className="text-red-600 hover:text-red-800 font-medium text-sm"
                onClick={(e) => {
                  if (
                    !confirm(
                      `Are you sure you want to delete "${resort.name}"? This action cannot be undone.`
                    )
                  ) {
                    e.preventDefault();
                  }
                }}
              >
                Delete
              </button>
            </form>
          </div>
        </div>
        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
          <Link
            href={`/admin/resorts/${resort.id}/rooms`}
            className="text-purple-600 hover:text-purple-800 font-medium text-sm"
          >
            Manage Rooms
          </Link>
          <Link
            href={`/admin/analytics/resorts`}
            className="text-indigo-600 hover:text-indigo-800 font-medium text-sm"
          >
            Analytics
          </Link>
        </div>
      </div>
    </div>
  );
}

// Resorts grid component
async function ResortsGrid() {
  try {
    const resorts = await prisma.resort.findMany({
      orderBy: { createdAt: "desc" },
    });

    if (resorts.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🏨</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No Resorts Created Yet
          </h3>
          <p className="text-gray-600 mb-4">
            Start by creating your first resort listing.
          </p>
          <Link
            href="/admin/resorts/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            + Create First Resort
          </Link>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {resorts.map((resort) => (
          <ResortCard key={resort.id} resort={resort} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching resorts:", error);
    return (
      <div className="text-center py-12">
        <div className="text-red-400 text-6xl mb-4">⚠️</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Error Loading Resorts
        </h3>
        <p className="text-gray-600 mb-4">
          Unable to load resort data. Please try refreshing the page.
        </p>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
        >
          Refresh Page
        </button>
      </div>
    );
  }
}

export default async function AdminResorts() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Resort Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your resort listings and room availability
          </p>
        </div>
        <Link
          href="/admin/resorts/create"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
        >
          <span className="mr-2">+</span>
          Add Resort
        </Link>
      </div>

      <Suspense
        fallback={
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <ResortCardSkeleton key={i} />
            ))}
          </div>
        }
      >
        <ResortsGrid />
      </Suspense>
    </div>
  );
}
