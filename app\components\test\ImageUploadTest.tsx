"use client";

import React, { useState } from 'react';
import ImageUpload from '@/app/components/ui/ImageUpload';
import MultipleImageUpload from '@/app/components/ui/MultipleImageUpload';
import ImageGallery from '@/app/components/ui/ImageGallery';

/**
 * Test component for image upload functionality
 * This component can be used to test all image upload features
 */
export default function ImageUploadTest() {
  const [singleImage, setSingleImage] = useState('');
  const [multipleImages, setMultipleImages] = useState<string[]>([]);
  const [testMode, setTestMode] = useState<'single' | 'multiple' | 'gallery'>('single');

  const handleReset = () => {
    setSingleImage('');
    setMultipleImages([]);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Image Upload System Test
        </h1>
        <p className="text-gray-600">
          Test all image upload components and functionality
        </p>
      </div>

      {/* Mode Selector */}
      <div className="flex justify-center space-x-4">
        <button
          onClick={() => setTestMode('single')}
          className={`px-4 py-2 rounded-md transition-colors ${
            testMode === 'single'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Single Upload
        </button>
        <button
          onClick={() => setTestMode('multiple')}
          className={`px-4 py-2 rounded-md transition-colors ${
            testMode === 'multiple'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Multiple Upload
        </button>
        <button
          onClick={() => setTestMode('gallery')}
          className={`px-4 py-2 rounded-md transition-colors ${
            testMode === 'gallery'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          Gallery View
        </button>
      </div>

      {/* Reset Button */}
      <div className="text-center">
        <button
          onClick={handleReset}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Reset All
        </button>
      </div>

      {/* Test Content */}
      <div className="bg-white rounded-lg shadow-md p-6">
        {testMode === 'single' && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Single Image Upload Test
            </h2>
            <p className="text-gray-600">
              Test the single image upload component with drag & drop, preview, and validation.
            </p>
            
            <ImageUpload
              value={singleImage}
              onChange={setSingleImage}
              uploadType="test"
              placeholder="Test single image upload - drag & drop or click to select"
              className="border-2 border-dashed border-gray-300"
            />

            {singleImage && (
              <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                <h3 className="font-medium text-green-800">Upload Successful!</h3>
                <p className="text-green-700 text-sm mt-1">
                  Image URL: {singleImage}
                </p>
              </div>
            )}
          </div>
        )}

        {testMode === 'multiple' && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Multiple Image Upload Test
            </h2>
            <p className="text-gray-600">
              Test the multiple image upload component with batch upload and management.
            </p>
            
            <MultipleImageUpload
              value={multipleImages}
              onChange={setMultipleImages}
              uploadType="test-gallery"
              maxImages={5}
              className="border-2 border-dashed border-gray-300"
            />

            {multipleImages.length > 0 && (
              <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                <h3 className="font-medium text-green-800">
                  {multipleImages.length} Image(s) Uploaded!
                </h3>
                <div className="text-green-700 text-sm mt-1 space-y-1">
                  {multipleImages.map((url, index) => (
                    <div key={index}>
                      Image {index + 1}: {url.substring(0, 50)}...
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {testMode === 'gallery' && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Image Gallery Test
            </h2>
            <p className="text-gray-600">
              Test the image gallery component with navigation and fullscreen view.
            </p>
            
            {multipleImages.length > 0 ? (
              <ImageGallery
                images={multipleImages}
                allowFullscreen
                showThumbnails
                aspectRatio="landscape"
                className="border border-gray-200 rounded-lg p-4"
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No images to display in gallery.</p>
                <p className="text-sm mt-2">
                  Switch to "Multiple Upload" mode and upload some images first.
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Feature Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">
          Features Being Tested
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Security Features</h4>
            <ul className="space-y-1 text-blue-700">
              <li>• File type validation (JPG, PNG, WebP, GIF)</li>
              <li>• File size limits (10MB max)</li>
              <li>• Image dimension validation</li>
              <li>• Content scanning for malicious files</li>
              <li>• Role-based access control</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">User Experience</h4>
            <ul className="space-y-1 text-blue-700">
              <li>• Drag & drop file selection</li>
              <li>• Real-time upload progress</li>
              <li>• Image preview and management</li>
              <li>• Error handling and feedback</li>
              <li>• Responsive design</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Performance</h4>
            <ul className="space-y-1 text-blue-700">
              <li>• Automatic image optimization</li>
              <li>• Responsive image delivery</li>
              <li>• CDN-based storage</li>
              <li>• Lazy loading support</li>
              <li>• Efficient thumbnail generation</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Accessibility</h4>
            <ul className="space-y-1 text-blue-700">
              <li>• ARIA labels and roles</li>
              <li>• Keyboard navigation support</li>
              <li>• Screen reader compatibility</li>
              <li>• Focus management</li>
              <li>• High contrast support</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          Testing Instructions
        </h3>
        <div className="space-y-3 text-sm text-gray-700">
          <div>
            <strong>Single Upload:</strong> Test drag & drop, file selection, preview, and error handling.
          </div>
          <div>
            <strong>Multiple Upload:</strong> Test batch upload, image management, and queue processing.
          </div>
          <div>
            <strong>Gallery View:</strong> Test image navigation, fullscreen view, and thumbnail navigation.
          </div>
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <strong>Note:</strong> You need admin or manager role to upload images. 
            Make sure you're logged in with appropriate permissions.
          </div>
        </div>
      </div>
    </div>
  );
}
