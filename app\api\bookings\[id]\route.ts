import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import {
  UpdateBookingStatusSchema,
  validateRequestBody,
  isValidObjectId,
} from "@/lib/validation";
import { logAuditEvent } from "@/lib/security";
import { requireRole } from "@/lib/checkRole";

export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Validate booking ID format
    if (!isValidObjectId(params.id)) {
      return NextResponse.json(
        { error: "Invalid booking ID" },
        { status: 400 }
      );
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      await logAuditEvent({
        action: "UNAUTHORIZED_BOOKING_UPDATE_ATTEMPT",
        resource: `/api/bookings/${params.id}`,
        resourceId: params.id,
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "No authentication session",
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get current user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, email: true, role: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get the booking to check ownership and current status
    const booking = await prisma.booking.findUnique({
      where: { id: params.id },
      include: {
        resort: true,
        spaTreatment: true,
      },
    });

    if (!booking) {
      return NextResponse.json({ error: "Booking not found" }, { status: 404 });
    }

    const rawData = await req.json();

    // Validate input data
    const validation = validateRequestBody(UpdateBookingStatusSchema, rawData);
    if (!validation.success) {
      await logAuditEvent({
        userId: user.id,
        action: "INVALID_BOOKING_UPDATE_DATA",
        resource: `/api/bookings/${params.id}`,
        resourceId: params.id,
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: validation.error,
      });
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { status } = validation.data;

    // Check authorization based on role and ownership
    const userRole = user.role.toLowerCase();
    const isOwner = booking.userEmail === user.email;
    const isStaff = ["admin", "manager", "receptionist"].includes(userRole);

    // Authorization rules:
    // - Users can only cancel their own bookings
    // - Staff can update any booking status
    // - Users cannot confirm their own bookings (only staff can)
    if (!isStaff && !isOwner) {
      await logAuditEvent({
        userId: user.id,
        action: "UNAUTHORIZED_BOOKING_ACCESS",
        resource: `/api/bookings/${params.id}`,
        resourceId: params.id,
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "User does not own this booking",
      });
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Users can only cancel their own bookings
    if (!isStaff && status !== "CANCELLED") {
      await logAuditEvent({
        userId: user.id,
        action: "UNAUTHORIZED_BOOKING_STATUS_CHANGE",
        resource: `/api/bookings/${params.id}`,
        resourceId: params.id,
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: `User attempted to set status to ${status}`,
      });
      return NextResponse.json(
        { error: "Users can only cancel their own bookings" },
        { status: 403 }
      );
    }

    // Prevent status changes on already cancelled bookings
    if (booking.status === "CANCELLED" && status !== "CANCELLED") {
      return NextResponse.json(
        { error: "Cannot modify cancelled bookings" },
        { status: 400 }
      );
    }

    // Prevent cancelling past bookings (check-in date has passed)
    if (status === "CANCELLED" && new Date(booking.checkIn) < new Date()) {
      return NextResponse.json(
        { error: "Cannot cancel bookings that have already started" },
        { status: 400 }
      );
    }

    // Update booking status
    const updated = await prisma.booking.update({
      where: { id: params.id },
      data: { status },
      include: {
        resort: true,
        spaTreatment: true,
      },
    });

    await logAuditEvent({
      userId: user.id,
      action: "BOOKING_STATUS_UPDATED",
      resource: `/api/bookings/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json(updated);
  } catch (error) {
    console.error("Error updating booking:", error);
    return NextResponse.json(
      { error: "Failed to update booking" },
      { status: 500 }
    );
  }
}

// Add DELETE endpoint for complete booking removal (admin only)
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Validate booking ID format
    if (!isValidObjectId(params.id)) {
      return NextResponse.json(
        { error: "Invalid booking ID" },
        { status: 400 }
      );
    }

    // Check authorization - only admins can delete bookings
    const authResult = await requireRole(["admin"]);
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const { user } = authResult;

    // Check if booking exists
    const booking = await prisma.booking.findUnique({
      where: { id: params.id },
    });

    if (!booking) {
      return NextResponse.json({ error: "Booking not found" }, { status: 404 });
    }

    // Delete the booking
    await prisma.booking.delete({
      where: { id: params.id },
    });

    await logAuditEvent({
      userId: user.id,
      action: "BOOKING_DELETED",
      resource: `/api/bookings/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting booking:", error);
    return NextResponse.json(
      { error: "Failed to delete booking" },
      { status: 500 }
    );
  }
}
