"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter, usePathname } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Resort, ResortFilters } from "@/app/types/resort";

interface ResortsWithFiltersProps {
  initialResorts: Resort[];
  enableRealTimeUpdates?: boolean;
  updateInterval?: number;
  className?: string;
}

// Resort card component
function ResortCard({ resort }: { resort: Resort }) {
  return (
    <Link
      href={`/resorts/${resort.slug}`}
      className="block bg-white rounded-xl shadow hover:shadow-md transition-shadow duration-200 group"
      aria-label={`View details for ${resort.name}`}
    >
      <div className="relative overflow-hidden rounded-t-xl">
        <Image
          src={resort.image}
          alt={resort.name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
          width={300}
          height={200}
          loading="lazy"
        />
        {resort.location && (
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
            {resort.location}
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="text-center font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
          {resort.name}
        </h3>
        {resort.description && (
          <p className="text-sm text-gray-600 mt-2 line-clamp-2">
            {resort.description.substring(0, 100)}...
          </p>
        )}
      </div>
    </Link>
  );
}

// Filter component
function ResortFilters({
  filters,
  onFiltersChange,
  locations,
}: {
  filters: ResortFilters;
  onFiltersChange: (filters: ResortFilters) => void;
  locations: string[];
}) {
  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter Resorts</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search */}
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
            Search
          </label>
          <input
            id="search"
            type="text"
            placeholder="Search resorts..."
            value={filters.searchTerm || ""}
            onChange={(e) => onFiltersChange({ ...filters, searchTerm: e.target.value })}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Location */}
        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
            Location
          </label>
          <select
            id="location"
            value={filters.location || ""}
            onChange={(e) => onFiltersChange({ ...filters, location: e.target.value || undefined })}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Locations</option>
            {locations.map((location) => (
              <option key={location} value={location}>
                {location}
              </option>
            ))}
          </select>
        </div>

        {/* Clear Filters */}
        <div className="flex items-end">
          <button
            type="button"
            onClick={() => onFiltersChange({})}
            className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors"
          >
            Clear Filters
          </button>
        </div>
      </div>
    </div>
  );
}

export default function ResortsWithFilters({
  initialResorts,
  enableRealTimeUpdates = false,
  updateInterval = 30000,
  className = "",
}: ResortsWithFiltersProps) {
  const [resorts, setResorts] = useState<Resort[]>(initialResorts);
  const [filteredResorts, setFilteredResorts] = useState<Resort[]>(initialResorts);
  const [filters, setFilters] = useState<ResortFilters>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  // Extract unique locations
  const locations = useMemo(() => {
    const uniqueLocations = [...new Set(resorts.map(resort => resort.location))];
    return uniqueLocations.sort();
  }, [resorts]);

  // Initialize filters from URL params
  useEffect(() => {
    const urlFilters: ResortFilters = {};
    
    const searchTerm = searchParams.get("search");
    const location = searchParams.get("location");
    
    if (searchTerm) urlFilters.searchTerm = searchTerm;
    if (location) urlFilters.location = location;
    
    setFilters(urlFilters);
  }, [searchParams]);

  // Update URL when filters change
  const updateURL = useCallback((newFilters: ResortFilters) => {
    const params = new URLSearchParams();
    
    if (newFilters.searchTerm) params.set("search", newFilters.searchTerm);
    if (newFilters.location) params.set("location", newFilters.location);
    
    const queryString = params.toString();
    const newURL = queryString ? `${pathname}?${queryString}` : pathname;
    
    router.replace(newURL, { scroll: false });
  }, [pathname, router]);

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters: ResortFilters) => {
    setFilters(newFilters);
    updateURL(newFilters);
  }, [updateURL]);

  // Filter resorts based on current filters
  useEffect(() => {
    let filtered = [...resorts];

    // Search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(resort =>
        resort.name.toLowerCase().includes(searchLower) ||
        resort.description.toLowerCase().includes(searchLower) ||
        resort.location.toLowerCase().includes(searchLower)
      );
    }

    // Location filter
    if (filters.location) {
      filtered = filtered.filter(resort => resort.location === filters.location);
    }

    setFilteredResorts(filtered);
  }, [resorts, filters]);

  // Real-time updates
  useEffect(() => {
    if (!enableRealTimeUpdates) return;

    const fetchUpdatedResorts = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/resorts");
        if (response.ok) {
          const updatedResorts = await response.json();
          setResorts(updatedResorts);
          setError(null);
        }
      } catch (err) {
        console.error("Error fetching updated resorts:", err);
        setError("Failed to update resorts");
      } finally {
        setIsLoading(false);
      }
    };

    const interval = setInterval(fetchUpdatedResorts, updateInterval);
    return () => clearInterval(interval);
  }, [enableRealTimeUpdates, updateInterval]);

  if (error) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="text-red-400 text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Resorts</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Real-time update indicator */}
      {enableRealTimeUpdates && isLoading && (
        <div className="fixed top-4 right-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm z-50">
          Updating...
        </div>
      )}

      {/* Filters */}
      <ResortFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        locations={locations}
      />

      {/* Results count */}
      <div className="flex justify-between items-center mb-6">
        <p className="text-gray-600">
          {filteredResorts.length} resort{filteredResorts.length !== 1 ? 's' : ''} found
        </p>
        {enableRealTimeUpdates && (
          <div className="flex items-center text-sm text-gray-500">
            <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
            Live updates enabled
          </div>
        )}
      </div>

      {/* Resort grid */}
      {filteredResorts.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No Resorts Found</h3>
          <p className="text-gray-600">Try adjusting your search criteria.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredResorts.map((resort) => (
            <ResortCard key={resort.id} resort={resort} />
          ))}
        </div>
      )}
    </div>
  );
}
