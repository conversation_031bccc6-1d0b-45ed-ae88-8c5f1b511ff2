import { prisma } from "@/lib/prisma";
import { Suspense } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  GridSkeleton,
  AdminCardSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface WellnessService {
  id: string;
  name: string;
  description: string;
  price: number;
  duration?: number | null;
  image: string;
  images: string[];
  category?: string | null;
  features: string[];
  instructor?: string | null;
  equipment: string[];
  maxCapacity?: number | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Wellness service card component
function WellnessCard({
  wellnessService,
}: {
  wellnessService: WellnessService;
}) {
  return (
    <div className="bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-48">
        <Image
          src={wellnessService.image}
          alt={wellnessService.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        <div className="absolute top-2 right-2 flex gap-2">
          <span className="bg-purple-600 text-white px-2 py-1 rounded text-sm font-semibold">
            ETB {wellnessService.price}
          </span>
          <span
            className={`px-2 py-1 rounded text-xs font-medium ${
              wellnessService.isActive
                ? "bg-green-100 text-green-800"
                : "bg-red-100 text-red-800"
            }`}
          >
            {wellnessService.isActive ? "Active" : "Inactive"}
          </span>
        </div>
        {wellnessService.duration && (
          <div className="absolute top-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
            {wellnessService.duration} min
          </div>
        )}
      </div>

      <div className="p-6">
        <div className="flex items-start justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
            {wellnessService.name}
          </h3>
          {wellnessService.category && (
            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
              {wellnessService.category}
            </span>
          )}
        </div>

        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {wellnessService.description}
        </p>

        <div className="space-y-2 mb-4">
          {wellnessService.instructor && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Instructor:</span>
              <span className="ml-1">{wellnessService.instructor}</span>
            </div>
          )}
          {wellnessService.maxCapacity && (
            <div className="flex items-center text-sm text-gray-500">
              <span className="font-medium">Max Capacity:</span>
              <span className="ml-1">{wellnessService.maxCapacity} people</span>
            </div>
          )}
        </div>

        {wellnessService.features.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {wellnessService.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="text-xs bg-purple-50 text-purple-700 px-2 py-1 rounded"
                >
                  {feature}
                </span>
              ))}
              {wellnessService.features.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{wellnessService.features.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {wellnessService.equipment.length > 0 && (
          <div className="mb-4">
            <p className="text-xs text-gray-500 mb-1">Equipment:</p>
            <div className="flex flex-wrap gap-1">
              {wellnessService.equipment.slice(0, 2).map((item, index) => (
                <span
                  key={index}
                  className="text-xs bg-green-50 text-green-700 px-2 py-1 rounded"
                >
                  {item}
                </span>
              ))}
              {wellnessService.equipment.length > 2 && (
                <span className="text-xs text-gray-500">
                  +{wellnessService.equipment.length - 2} more
                </span>
              )}
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Link
            href={`/admin/wellness/${wellnessService.id}/edit`}
            className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors text-center"
          >
            Edit
          </Link>
          <button
            type="button"
            onClick={() => {
              if (
                confirm(
                  "Are you sure you want to delete this wellness service?"
                )
              ) {
                // TODO: Implement delete functionality
                console.log("Delete wellness service:", wellnessService.id);
              }
            }}
            className="px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}

// Wellness services grid component
async function WellnessGrid() {
  try {
    const wellnessServices = await prisma.wellnessService.findMany({
      orderBy: { createdAt: "desc" },
    });

    if (wellnessServices.length === 0) {
      return (
        <EmptyState
          title="No Wellness Services Created Yet"
          message="Start by adding your first wellness service to showcase fitness classes, yoga sessions, and therapeutic services."
          icon={<div className="text-gray-400 text-6xl mb-4">🧘‍♀️</div>}
          actionLabel="+ Add First Wellness Service"
          actionHref="/admin/wellness/create"
        />
      );
    }

    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        aria-label="Wellness services list"
      >
        {wellnessServices.map((wellnessService) => (
          <WellnessCard
            key={wellnessService.id}
            wellnessService={wellnessService}
          />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching wellness services:", error);
    return (
      <LoadingError
        resource="wellness services"
        onRetry={() => window.location.reload()}
      />
    );
  }
}

export default async function AdminWellness() {
  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Wellness Services Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage fitness classes, yoga sessions, meditation, and therapeutic
              services
            </p>
          </div>
          <Link
            href="/admin/wellness/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
            aria-label="Create new wellness service"
          >
            <span className="mr-2">+</span>
            Add Wellness Service
          </Link>
        </div>

        <Suspense
          fallback={
            <GridSkeleton
              count={6}
              columns={3}
              SkeletonComponent={AdminCardSkeleton}
            />
          }
        >
          <WellnessGrid />
        </Suspense>
      </div>
    </ErrorBoundary>
  );
}
