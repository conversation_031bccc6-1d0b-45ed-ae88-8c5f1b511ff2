import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const wellnessServices = await prisma.wellnessService.findMany({
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(wellnessServices);
  } catch (error) {
    console.error("Error fetching wellness services:", error);
    return NextResponse.json(
      { error: "Failed to fetch wellness services" },
      { status: 500 }
    );
  }
}
