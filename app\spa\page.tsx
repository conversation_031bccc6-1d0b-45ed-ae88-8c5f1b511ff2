import { prisma } from "@/lib/prisma";
import DynamicSpaServiceCards from "@/app/components/DynamicSpaServiceCards";
import DynamicContentSection from "@/app/components/DynamicContentSection";
import { Suspense } from "react";
import {
  SpaCardSkeleton,
  GridSkeleton,
} from "@/app/components/ui/SkeletonComponents";

// TypeScript interfaces
interface SpaPageProps {
  searchParams?: {
    category?: string;
    priceRange?: string;
  };
}

export default async function SpaPage({ searchParams }: SpaPageProps) {
  // Fetch initial spa treatments data for SEO and initial render
  const initialTreatments = await prisma.spaTreatment.findMany({
    orderBy: { createdAt: "desc" },
  });

  return (
    <div className="max-w-7xl mx-auto px-4 py-10">
      {/* Hero Section */}
      <section className="text-center mb-16" role="banner">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Boston Day Spa
        </h1>
        <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          Welcome to Boston Day Spa – a sanctuary of wellness. Discover our
          premium spa treatments including massage, facials, steam therapy, and
          more in a tranquil environment designed for your complete relaxation.
        </p>
        <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
          <span className="flex items-center">
            <span
              className="w-2 h-2 bg-green-500 rounded-full mr-2"
              aria-hidden="true"
            ></span>
            Professional Therapists
          </span>
          <span className="flex items-center">
            <span
              className="w-2 h-2 bg-blue-500 rounded-full mr-2"
              aria-hidden="true"
            ></span>
            Premium Products
          </span>
          <span className="flex items-center">
            <span
              className="w-2 h-2 bg-purple-500 rounded-full mr-2"
              aria-hidden="true"
            ></span>
            Relaxing Environment
          </span>
        </div>
      </section>

      {/* Spa Treatments Section */}
      <section className="mb-16" role="main">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Our Spa Treatments
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose from our comprehensive range of spa treatments designed to
            rejuvenate your body, mind, and spirit.
          </p>
        </div>

        <Suspense
          fallback={
            <GridSkeleton
              count={8}
              columns={4}
              SkeletonComponent={SpaCardSkeleton}
            />
          }
        >
          <DynamicSpaServiceCards
            enableRealTimeUpdates={true}
            updateInterval={30000}
            showTitle={false}
            maxServices={12}
            className=""
          />
        </Suspense>
      </section>

      {/* Wellness Programs Section */}
      <DynamicContentSection
        title="Wellness Programs"
        description="Comprehensive wellness programs designed to enhance your overall well-being through holistic approaches."
        apiEndpoint="/api/wellness"
        contentType="wellness"
        enableRealTimeUpdates={true}
        updateInterval={30000}
        maxItems={6}
        gridCols={3}
        className="mb-16"
      />

      {/* Special Offers Section */}
      <DynamicContentSection
        title="Spa Packages & Special Offers"
        description="Take advantage of our exclusive spa packages and seasonal offers for the ultimate relaxation experience."
        apiEndpoint="/api/offer"
        contentType="offer"
        enableRealTimeUpdates={true}
        updateInterval={30000}
        maxItems={4}
        gridCols={2}
        className="mb-16"
      />
    </div>
  );
}
