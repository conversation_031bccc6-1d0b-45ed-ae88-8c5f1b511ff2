import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { logAuditEvent } from "@/lib/security";
import { requireRole } from "@/lib/checkRole";
import { z } from "zod";

// Validation schema for check-in
const CheckInSchema = z.object({
  status: z.enum(["CHECKED_IN"]),
  checkInTime: z.string().optional(),
  notes: z.string().optional(),
});

export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication and role
    const roleCheck = await requireRole(["admin", "manager", "receptionist"]);
    if (roleCheck instanceof NextResponse) {
      return roleCheck;
    }

    const rawData = await req.json();

    // Validate input data
    const validation = CheckInSchema.safeParse(rawData);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 }
      );
    }

    const { status, checkInTime, notes } = validation.data;

    // Check if booking exists
    const booking = await prisma.booking.findUnique({
      where: { id: params.id },
      include: {
        resort: true,
        spaTreatment: true,
      },
    });

    if (!booking) {
      return NextResponse.json(
        { error: "Booking not found" },
        { status: 404 }
      );
    }

    // Check if booking can be checked in
    if (booking.status === "CANCELLED") {
      return NextResponse.json(
        { error: "Cannot check in cancelled booking" },
        { status: 400 }
      );
    }

    if (booking.status === "CHECKED_IN") {
      return NextResponse.json(
        { error: "Guest is already checked in" },
        { status: 400 }
      );
    }

    // Update booking status
    const updatedBooking = await prisma.booking.update({
      where: { id: params.id },
      data: {
        status: "CONFIRMED", // Using existing enum value
        notes: notes || booking.notes,
        updatedAt: new Date(),
      },
      include: {
        resort: true,
        spaTreatment: true,
      },
    });

    // Log the check-in event
    await logAuditEvent({
      userId: roleCheck.user.id,
      action: "GUEST_CHECKED_IN",
      resource: `/api/reception/checkin/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    // TODO: Send check-in confirmation email/SMS to guest
    // TODO: Create notification for relevant staff
    // TODO: Update room assignment if applicable

    return NextResponse.json({
      ...updatedBooking,
      userEmail: updatedBooking.userEmail,
      message: "Guest checked in successfully",
    });
  } catch (error) {
    console.error("Error checking in guest:", error);
    await logAuditEvent({
      action: "GUEST_CHECKIN_ERROR",
      resource: `/api/reception/checkin/${params.id}`,
      resourceId: params.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Failed to check in guest" },
      { status: 500 }
    );
  }
}
