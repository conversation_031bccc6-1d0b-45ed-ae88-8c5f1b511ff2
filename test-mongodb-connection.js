const { MongoClient } = require("mongodb");

async function testConnection() {
  // Hardcoded URI for testing (replace with your actual URI)
  const uri =
    "mongodb+srv://ephshif7:<EMAIL>/kuriftu?retryWrites=true&w=majority&appName=Cluster0";

  if (!uri) {
    console.error("❌ MONGODB_URI environment variable is not set");
    return;
  }

  console.log("🔗 Testing MongoDB connection...");
  console.log("📍 URI:", uri.replace(/\/\/([^:]+):([^@]+)@/, "//***:***@")); // Hide credentials

  const client = new MongoClient(uri);

  try {
    // Connect to MongoDB
    console.log("⏳ Connecting to MongoDB Atlas...");
    await client.connect();

    // Test the connection
    console.log("✅ Successfully connected to MongoDB Atlas!");

    // List databases to verify connection
    const adminDb = client.db().admin();
    const databases = await adminDb.listDatabases();
    console.log(
      "📊 Available databases:",
      databases.databases.map((db) => db.name)
    );

    // Test the specific database
    const db = client.db("kuriftu");
    const collections = await db.listCollections().toArray();
    console.log(
      "📁 Collections in kuriftu database:",
      collections.map((col) => col.name)
    );
  } catch (error) {
    console.error("❌ MongoDB connection failed:");
    console.error("Error type:", error.constructor.name);
    console.error("Error message:", error.message);

    if (error.message.includes("ENOTFOUND") || error.message.includes("DNS")) {
      console.log("\n🔧 DNS Resolution Issue Detected:");
      console.log("1. Try changing your DNS servers to:");
      console.log("   - Primary: ******* (Google)");
      console.log("   - Secondary: ******* (Google)");
      console.log("2. Or try: ******* and ******* (Cloudflare)");
      console.log("3. Restart your network adapter after changing DNS");
    }

    if (error.message.includes("authentication")) {
      console.log("\n🔧 Authentication Issue:");
      console.log("1. Check your MongoDB Atlas username and password");
      console.log("2. Verify the user has proper database permissions");
      console.log("3. Check if IP address is whitelisted in MongoDB Atlas");
    }
  } finally {
    await client.close();
    console.log("🔌 Connection closed");
  }
}

testConnection().catch(console.error);
