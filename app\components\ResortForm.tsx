"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

export default function ResortForm({ resort }: { resort?: any }) {
  const router = useRouter();
  const [form, setForm] = useState({
    name: resort?.name || "",
    location: resort?.location || "",
    description: resort?.description || "",
    image: resort?.image || "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const method = resort ? "PATCH" : "POST";
    const url = resort ? `/api/resorts/${resort.id}` : "/api/resorts";

    await fetch(url, {
      method,
      body: JSON.stringify(form),
    });

    router.push("/dashboard/resorts");
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 p-6">
      <input
        type="text"
        placeholder="Name"
        value={form.name}
        onChange={(e) => setForm({ ...form, name: e.target.value })}
        className="input input-bordered w-full"
        required
      />
      <input
        type="text"
        placeholder="Location"
        value={form.location}
        onChange={(e) => setForm({ ...form, location: e.target.value })}
        className="input input-bordered w-full"
        required
      />
      <textarea
        placeholder="Description"
        value={form.description}
        onChange={(e) => setForm({ ...form, description: e.target.value })}
        className="textarea textarea-bordered w-full"
        required
      />
      <input
        type="text"
        placeholder="Image URL"
        value={form.image}
        onChange={(e) => setForm({ ...form, image: e.target.value })}
        className="input input-bordered w-full"
        required
      />
      <button type="submit" className="btn btn-primary">
        Save
      </button>
    </form>
  );
}
