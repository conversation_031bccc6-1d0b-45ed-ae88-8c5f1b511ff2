import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";
import { logAuditEvent } from "@/lib/security";

// GET /api/admin/reviews - Get all reviews for admin management
export async function GET(req: Request) {
  // Check authorization - only admin and manager can access
  const authResult = await requireRole(["admin", "manager"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(req.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status'); // 'pending', 'approved', 'published'
    const contentType = searchParams.get('contentType');
    const sortBy = searchParams.get('sortBy') || 'newest';

    // Build where clause
    const where: any = {};

    if (status === 'pending') {
      where.isApproved = false;
    } else if (status === 'approved') {
      where.isApproved = true;
      where.isPublished = false;
    } else if (status === 'published') {
      where.isPublished = true;
    }

    if (contentType) {
      switch (contentType) {
        case 'resort':
          where.resortId = { not: null };
          break;
        case 'spa':
          where.spaId = { not: null };
          break;
        case 'experience':
          where.experienceId = { not: null };
          break;
        case 'wellness':
          where.wellnessId = { not: null };
          break;
        case 'event':
          where.eventId = { not: null };
          break;
      }
    }

    // Build orderBy clause
    let orderBy: any = { createdAt: 'desc' };
    
    switch (sortBy) {
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'rating_high':
        orderBy = { rating: 'desc' };
        break;
      case 'rating_low':
        orderBy = { rating: 'asc' };
        break;
      case 'helpful':
        orderBy = { helpfulVotes: 'desc' };
        break;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Fetch reviews with all relations
    const [reviews, total] = await Promise.all([
      prisma.review.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          resort: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          room: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          spaTreatment: {
            select: {
              id: true,
              name: true,
            },
          },
          experience: {
            select: {
              id: true,
              name: true,
            },
          },
          wellnessService: {
            select: {
              id: true,
              name: true,
            },
          },
          event: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
      prisma.review.count({ where }),
    ]);

    // Get summary statistics
    const stats = await prisma.review.groupBy({
      by: ['isApproved', 'isPublished'],
      _count: {
        id: true,
      },
    });

    const summary = {
      total,
      pending: stats.find(s => !s.isApproved)?._count.id || 0,
      approved: stats.find(s => s.isApproved && !s.isPublished)?._count.id || 0,
      published: stats.find(s => s.isPublished)?._count.id || 0,
    };

    return NextResponse.json({
      reviews,
      total,
      page,
      limit,
      summary,
    });

  } catch (error) {
    console.error("Error fetching admin reviews:", error);
    return NextResponse.json(
      { error: "Failed to fetch reviews" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/reviews - Bulk update reviews
export async function PATCH(req: Request) {
  // Check authorization - only admin can bulk update
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { reviewIds, action, reason } = await req.json();

    if (!reviewIds || !Array.isArray(reviewIds) || reviewIds.length === 0) {
      return NextResponse.json(
        { error: "Review IDs are required" },
        { status: 400 }
      );
    }

    if (!action) {
      return NextResponse.json(
        { error: "Action is required" },
        { status: 400 }
      );
    }

    let updateData: any = {};

    switch (action) {
      case 'approve':
        updateData = { isApproved: true };
        break;
      case 'reject':
        updateData = { isApproved: false, isPublished: false };
        break;
      case 'publish':
        updateData = { isApproved: true, isPublished: true };
        break;
      case 'unpublish':
        updateData = { isPublished: false };
        break;
      case 'verify':
        updateData = { isVerified: true };
        break;
      case 'unverify':
        updateData = { isVerified: false };
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }

    // Update reviews
    const result = await prisma.review.updateMany({
      where: {
        id: { in: reviewIds },
      },
      data: updateData,
    });

    await logAuditEvent({
      action: `REVIEWS_BULK_${action.toUpperCase()}`,
      resource: `reviews_bulk`,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
      details: `Updated ${result.count} reviews with action: ${action}${reason ? `, reason: ${reason}` : ''}`,
    });

    return NextResponse.json({
      message: `Successfully ${action}ed ${result.count} reviews`,
      updatedCount: result.count,
    });

  } catch (error) {
    console.error("Error bulk updating reviews:", error);
    return NextResponse.json(
      { error: "Failed to update reviews" },
      { status: 500 }
    );
  }
}
