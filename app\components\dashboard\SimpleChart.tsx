"use client";

import React from 'react';

export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface SimpleChartProps {
  data: ChartDataPoint[];
  type: 'bar' | 'line' | 'pie' | 'area';
  title?: string;
  height?: number;
  width?: number;
  className?: string;
  showValues?: boolean;
  showGrid?: boolean;
  colors?: string[];
}

const defaultColors = [
  '#3B82F6', // blue
  '#10B981', // green
  '#F59E0B', // yellow
  '#EF4444', // red
  '#8B5CF6', // purple
  '#06B6D4', // cyan
  '#F97316', // orange
  '#84CC16', // lime
];

export default function SimpleChart({
  data,
  type,
  title,
  height = 300,
  width = 400,
  className = '',
  showValues = true,
  showGrid = true,
  colors = defaultColors,
}: SimpleChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        {title && <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>}
        <div className="flex items-center justify-center h-64 text-gray-500">
          No data available
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue || 1;

  const renderBarChart = () => {
    const barWidth = (width - 80) / data.length;
    const chartHeight = height - 80;

    return (
      <svg width={width} height={height} className="overflow-visible">
        {/* Grid lines */}
        {showGrid && (
          <g>
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, i) => (
              <g key={i}>
                <line
                  x1={60}
                  y1={60 + chartHeight * ratio}
                  x2={width - 20}
                  y2={60 + chartHeight * ratio}
                  stroke="#E5E7EB"
                  strokeWidth={1}
                />
                <text
                  x={50}
                  y={65 + chartHeight * ratio}
                  textAnchor="end"
                  className="text-xs fill-gray-500"
                >
                  {Math.round(maxValue * (1 - ratio))}
                </text>
              </g>
            ))}
          </g>
        )}

        {/* Bars */}
        {data.map((item, index) => {
          const barHeight = (item.value / maxValue) * chartHeight;
          const x = 60 + index * barWidth + barWidth * 0.1;
          const y = 60 + chartHeight - barHeight;
          const color = item.color || colors[index % colors.length];

          return (
            <g key={index}>
              <rect
                x={x}
                y={y}
                width={barWidth * 0.8}
                height={barHeight}
                fill={color}
                className="transition-all duration-300 hover:opacity-80"
              />
              {showValues && (
                <text
                  x={x + (barWidth * 0.8) / 2}
                  y={y - 5}
                  textAnchor="middle"
                  className="text-xs fill-gray-700 font-medium"
                >
                  {item.value}
                </text>
              )}
              <text
                x={x + (barWidth * 0.8) / 2}
                y={height - 20}
                textAnchor="middle"
                className="text-xs fill-gray-600"
              >
                {item.label}
              </text>
            </g>
          );
        })}
      </svg>
    );
  };

  const renderLineChart = () => {
    const pointSpacing = (width - 80) / (data.length - 1 || 1);
    const chartHeight = height - 80;

    const points = data.map((item, index) => ({
      x: 60 + index * pointSpacing,
      y: 60 + chartHeight - (item.value / maxValue) * chartHeight,
      ...item,
    }));

    const pathData = points
      .map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`)
      .join(' ');

    return (
      <svg width={width} height={height} className="overflow-visible">
        {/* Grid lines */}
        {showGrid && (
          <g>
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, i) => (
              <g key={i}>
                <line
                  x1={60}
                  y1={60 + chartHeight * ratio}
                  x2={width - 20}
                  y2={60 + chartHeight * ratio}
                  stroke="#E5E7EB"
                  strokeWidth={1}
                />
                <text
                  x={50}
                  y={65 + chartHeight * ratio}
                  textAnchor="end"
                  className="text-xs fill-gray-500"
                >
                  {Math.round(maxValue * (1 - ratio))}
                </text>
              </g>
            ))}
          </g>
        )}

        {/* Line */}
        <path
          d={pathData}
          fill="none"
          stroke={colors[0]}
          strokeWidth={3}
          className="transition-all duration-300"
        />

        {/* Points */}
        {points.map((point, index) => (
          <g key={index}>
            <circle
              cx={point.x}
              cy={point.y}
              r={4}
              fill={point.color || colors[0]}
              className="transition-all duration-300 hover:r-6"
            />
            {showValues && (
              <text
                x={point.x}
                y={point.y - 10}
                textAnchor="middle"
                className="text-xs fill-gray-700 font-medium"
              >
                {point.value}
              </text>
            )}
            <text
              x={point.x}
              y={height - 20}
              textAnchor="middle"
              className="text-xs fill-gray-600"
            >
              {point.label}
            </text>
          </g>
        ))}
      </svg>
    );
  };

  const renderPieChart = () => {
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 2 - 40;
    const total = data.reduce((sum, item) => sum + item.value, 0);

    let currentAngle = -90; // Start from top

    return (
      <svg width={width} height={height} className="overflow-visible">
        {data.map((item, index) => {
          const percentage = (item.value / total) * 100;
          const angle = (item.value / total) * 360;
          const startAngle = (currentAngle * Math.PI) / 180;
          const endAngle = ((currentAngle + angle) * Math.PI) / 180;

          const x1 = centerX + radius * Math.cos(startAngle);
          const y1 = centerY + radius * Math.sin(startAngle);
          const x2 = centerX + radius * Math.cos(endAngle);
          const y2 = centerY + radius * Math.sin(endAngle);

          const largeArcFlag = angle > 180 ? 1 : 0;

          const pathData = [
            `M ${centerX} ${centerY}`,
            `L ${x1} ${y1}`,
            `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
            'Z',
          ].join(' ');

          const labelAngle = currentAngle + angle / 2;
          const labelRadius = radius * 0.7;
          const labelX = centerX + labelRadius * Math.cos((labelAngle * Math.PI) / 180);
          const labelY = centerY + labelRadius * Math.sin((labelAngle * Math.PI) / 180);

          currentAngle += angle;

          return (
            <g key={index}>
              <path
                d={pathData}
                fill={item.color || colors[index % colors.length]}
                className="transition-all duration-300 hover:opacity-80"
              />
              {showValues && percentage > 5 && (
                <text
                  x={labelX}
                  y={labelY}
                  textAnchor="middle"
                  className="text-xs fill-white font-medium"
                >
                  {percentage.toFixed(1)}%
                </text>
              )}
            </g>
          );
        })}
      </svg>
    );
  };

  const renderChart = () => {
    switch (type) {
      case 'bar':
        return renderBarChart();
      case 'line':
        return renderLineChart();
      case 'pie':
        return renderPieChart();
      case 'area':
        return renderLineChart(); // Simplified area chart as line chart
      default:
        return renderBarChart();
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      {title && <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>}
      <div className="flex justify-center">
        {renderChart()}
      </div>
      {type === 'pie' && (
        <div className="mt-4 flex flex-wrap justify-center gap-4">
          {data.map((item, index) => (
            <div key={index} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: item.color || colors[index % colors.length] }}
              />
              <span className="text-sm text-gray-600">{item.label}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
