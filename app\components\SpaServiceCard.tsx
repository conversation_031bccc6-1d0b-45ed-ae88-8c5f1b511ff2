"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { SpaServiceCardProps } from "@/app/types/spa";

export default function SpaServiceCard({
  service,
  isLoading = false,
  showBookButton = true,
  showDetailsButton = true,
  onBook,
  onViewDetails,
  className = "",
}: SpaServiceCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Handle image load completion
  const handleImageLoad = () => {
    setImageLoading(false);
  };

  // Handle image error
  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-md overflow-hidden animate-pulse">
        <div className="w-full h-48 bg-gray-200"></div>
        <div className="p-6">
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-3 bg-gray-200 rounded mb-4"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group ${className}`}
      role="article"
      aria-labelledby={`spa-service-${service.id}`}
    >
      {/* Image Section */}
      <div className="relative w-full h-48 overflow-hidden">
        {imageLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="text-gray-400">Loading...</div>
          </div>
        )}

        {imageError ? (
          <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
            <div className="text-center text-gray-600">
              <div className="text-2xl mb-2">🧖‍♀️</div>
              <div className="text-sm">{service.category}</div>
            </div>
          </div>
        ) : (
          <Image
            src={service.image}
            alt={`${service.name} - ${service.category} service`}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            width={300}
            height={200}
            onLoad={handleImageLoad}
            onError={handleImageError}
            priority={false}
          />
        )}

        {/* Category Badge */}
        <div className="absolute top-3 left-3">
          <span className="bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
            {service.category}
          </span>
        </div>

        {/* Duration Badge */}
        {service.duration && (
          <div className="absolute top-3 right-3">
            <span className="bg-black/70 text-white px-3 py-1 rounded-full text-xs font-medium">
              {service.duration} min
            </span>
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="p-6">
        {/* Title */}
        <h3
          id={`spa-service-${service.id}`}
          className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors"
        >
          {service.name}
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {service.description}
        </p>

        {/* Features */}
        {service.features && service.features.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {service.features.slice(0, 3).map((feature, index) => (
                <span
                  key={index}
                  className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                >
                  {feature}
                </span>
              ))}
              {service.features.length > 3 && (
                <span className="text-gray-500 text-xs px-2 py-1">
                  +{service.features.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <span className="text-2xl font-bold text-blue-600">
              {service.price} ETB
            </span>
            {service.duration && (
              <span className="text-gray-500 text-sm ml-1">
                / {service.duration}min
              </span>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        {(showBookButton || showDetailsButton) && (
          <div className="flex gap-2">
            {showBookButton && (
              <>
                {onBook ? (
                  <button
                    type="button"
                    onClick={() => onBook(service.id)}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-3 px-4 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    aria-label={`Book ${service.name} service`}
                  >
                    Book Now
                  </button>
                ) : (
                  <Link
                    href={`/spa/book/${service.id}`}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-3 px-4 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    aria-label={`Book ${service.name} service`}
                  >
                    Book Now
                  </Link>
                )}
              </>
            )}

            {showDetailsButton && (
              <>
                {onViewDetails ? (
                  <button
                    type="button"
                    onClick={() => onViewDetails(service.id)}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                    aria-label={`Learn more about ${service.name}`}
                  >
                    Details
                  </button>
                ) : (
                  <Link
                    href={`/spa/services/${service.id}`}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                    aria-label={`Learn more about ${service.name}`}
                  >
                    Details
                  </Link>
                )}
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
