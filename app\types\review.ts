/**
 * TypeScript interfaces for Review and Testimonial system
 * Provides type safety for review-related operations
 */

// Base review interface
export interface Review {
  id: string;
  userId: string;
  
  // Content type IDs (only one should be set per review)
  resortId?: string;
  roomId?: string;
  spaId?: string;
  experienceId?: string;
  wellnessId?: string;
  eventId?: string;
  
  // Review content
  rating: number; // 1-5 stars
  title?: string; // Optional review title
  comment: string; // Review text
  images?: string[]; // Optional review images
  
  // Verification and moderation
  isVerified: boolean; // Verified guest (has booking)
  isApproved: boolean; // Admin approved
  isPublished: boolean; // Published on site
  
  // Engagement metrics
  helpfulVotes: number;
  reportCount: number;
  
  // Relations (populated when needed)
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
  };
  resort?: {
    id: string;
    name: string;
    slug: string;
  };
  room?: {
    id: string;
    name: string;
    type: string;
  };
  spaTreatment?: {
    id: string;
    name: string;
  };
  experience?: {
    id: string;
    name: string;
  };
  wellnessService?: {
    id: string;
    name: string;
  };
  event?: {
    id: string;
    name: string;
  };
  
  createdAt: string;
  updatedAt: string;
}

// Testimonial interface
export interface Testimonial {
  id: string;
  userId?: string; // Optional - can be anonymous
  
  // Testimonial content
  name: string; // Guest name (can be anonymous like "John D.")
  title?: string; // Optional title/headline
  content: string; // Testimonial text
  image?: string; // Optional guest photo
  location?: string; // Guest location (e.g., "Addis Ababa, Ethiopia")
  
  // Display settings
  isFeatured: boolean; // Featured on homepage
  isApproved: boolean; // Admin approved
  isPublished: boolean; // Published on site
  displayOrder?: number; // Order for featured testimonials
  
  // Relations
  user?: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
  };
  
  createdAt: string;
  updatedAt: string;
}

// Review submission interfaces
export interface CreateReviewInput {
  // Content type (only one should be provided)
  resortId?: string;
  roomId?: string;
  spaId?: string;
  experienceId?: string;
  wellnessId?: string;
  eventId?: string;
  
  // Review content
  rating: number; // 1-5 stars
  title?: string;
  comment: string;
  images?: string[];
}

export interface UpdateReviewInput extends Partial<CreateReviewInput> {
  id: string;
}

// Testimonial submission interfaces
export interface CreateTestimonialInput {
  name: string;
  title?: string;
  content: string;
  image?: string;
  location?: string;
}

export interface UpdateTestimonialInput extends Partial<CreateTestimonialInput> {
  id: string;
  isFeatured?: boolean;
  isApproved?: boolean;
  isPublished?: boolean;
  displayOrder?: number;
}

// Review filtering and pagination
export interface ReviewFilters {
  rating?: number; // Filter by specific rating
  minRating?: number; // Filter by minimum rating
  isVerified?: boolean; // Filter by verified reviews only
  contentType?: 'resort' | 'room' | 'spa' | 'experience' | 'wellness' | 'event';
  contentId?: string; // Filter by specific content ID
  userId?: string; // Filter by specific user
  sortBy?: 'newest' | 'oldest' | 'rating_high' | 'rating_low' | 'helpful';
  page?: number;
  limit?: number;
}

export interface TestimonialFilters {
  isFeatured?: boolean;
  isApproved?: boolean;
  isPublished?: boolean;
  sortBy?: 'newest' | 'oldest' | 'featured' | 'order';
  page?: number;
  limit?: number;
}

// API Response interfaces
export interface ReviewListResponse {
  reviews: Review[];
  total: number;
  page: number;
  limit: number;
  averageRating?: number;
  ratingDistribution?: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export interface TestimonialListResponse {
  testimonials: Testimonial[];
  total: number;
  page: number;
  limit: number;
}

// Review statistics interface
export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  verifiedReviews: number;
  recentReviews: number; // Reviews in last 30 days
}

// Admin moderation interfaces
export interface ReviewModerationAction {
  reviewId: string;
  action: 'approve' | 'reject' | 'publish' | 'unpublish' | 'verify' | 'unverify';
  reason?: string;
}

export interface TestimonialModerationAction {
  testimonialId: string;
  action: 'approve' | 'reject' | 'publish' | 'unpublish' | 'feature' | 'unfeature';
  displayOrder?: number;
  reason?: string;
}

// Form validation interfaces
export interface ReviewFormErrors {
  rating?: string;
  comment?: string;
  title?: string;
  general?: string;
}

export interface TestimonialFormErrors {
  name?: string;
  content?: string;
  title?: string;
  general?: string;
}

// Component prop interfaces
export interface ReviewCardProps {
  review: Review;
  showActions?: boolean;
  onHelpful?: (reviewId: string) => void;
  onReport?: (reviewId: string) => void;
  className?: string;
}

export interface TestimonialCardProps {
  testimonial: Testimonial;
  showActions?: boolean;
  className?: string;
}

export interface ReviewFormProps {
  contentType: 'resort' | 'room' | 'spa' | 'experience' | 'wellness' | 'event';
  contentId: string;
  contentName: string;
  onSubmit: (data: CreateReviewInput) => Promise<void>;
  loading?: boolean;
  className?: string;
}

export interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
  onChange?: (rating: number) => void;
  className?: string;
}
