import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function POST(req: Request) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult; // Return error response
  }

  try {
    const data = await req.json();

    // Basic input validation
    if (!data.name || !data.description || typeof data.price !== "number") {
      return NextResponse.json(
        { error: "Missing required fields: name, description, price" },
        { status: 400 }
      );
    }

    if (data.price <= 0) {
      return NextResponse.json(
        { error: "Price must be greater than 0" },
        { status: 400 }
      );
    }

    const treatment = await prisma.spaTreatment.create({ data });
    return NextResponse.json(treatment);
  } catch (error) {
    console.error("Error creating spa treatment:", error);
    return NextResponse.json(
      { error: "Failed to create spa treatment" },
      { status: 500 }
    );
  }
}
