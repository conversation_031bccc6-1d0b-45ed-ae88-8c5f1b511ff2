"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRole } from "./auth/RoleGuard";
import toast from "react-hot-toast";

interface Booking {
  id: string;
  userEmail: string;
  status: "PENDING" | "CONFIRMED" | "CANCELLED";
  checkIn: string;
  checkOut?: string;
  notes?: string;
  createdAt: string;
  resort?: {
    id: string;
    name: string;
    location: string;
  };
  spaTreatment?: {
    id: string;
    name: string;
    price: number;
  };
}

interface BookingManagementProps {
  userBookingsOnly?: boolean;
}

export default function BookingManagement({
  userBookingsOnly = false,
}: BookingManagementProps) {
  const { data: session } = useSession();
  const { isStaff } = useRole();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [updatingBooking, setUpdatingBooking] = useState<string | null>(null);
  const [filter, setFilter] = useState<
    "all" | "PENDING" | "CONFIRMED" | "CANCELLED"
  >("all");

  useEffect(() => {
    fetchBookings();
  }, [userBookingsOnly]);

  const fetchBookings = async () => {
    try {
      const endpoint = userBookingsOnly
        ? "/api/bookings/user"
        : "/api/bookings/admin";
      const response = await fetch(endpoint, {
        headers: {
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBookings(data);
      } else {
        toast.error("Failed to fetch bookings");
      }
    } catch (error) {
      console.error("Error fetching bookings:", error);
      toast.error("An error occurred while fetching bookings");
    } finally {
      setLoading(false);
    }
  };

  const updateBookingStatus = async (
    bookingId: string,
    newStatus: "CONFIRMED" | "CANCELLED"
  ) => {
    setUpdatingBooking(bookingId);

    try {
      const response = await fetch(`/api/bookings/${bookingId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        const updatedBooking = await response.json();
        setBookings((prev) =>
          prev.map((booking) =>
            booking.id === bookingId ? updatedBooking : booking
          )
        );
        toast.success(`Booking ${newStatus} successfully`);
      } else {
        const error = await response.json();
        toast.error(error.error || `Failed to ${newStatus} booking`);
      }
    } catch (error) {
      console.error("Error updating booking:", error);
      toast.error("An error occurred while updating the booking");
    } finally {
      setUpdatingBooking(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "CONFIRMED":
        return "bg-green-100 text-green-800";
      case "CANCELLED":
        return "bg-red-100 text-red-800";
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredBookings = bookings.filter(
    (booking) => filter === "all" || booking.status === filter
  );

  const canUpdateBooking = (booking: Booking) => {
    if (userBookingsOnly) {
      // Users can only cancel their own bookings and only if not started
      return (
        booking.status !== "CANCELLED" && new Date(booking.checkIn) > new Date()
      );
    }
    // Staff can update any booking
    return isStaff();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">
          {userBookingsOnly ? "My Bookings" : "All Bookings"}
        </h2>

        {/* Status Filter */}
        <div className="flex space-x-2">
          {[
            { value: "all", label: "All" },
            { value: "PENDING", label: "Pending" },
            { value: "CONFIRMED", label: "Confirmed" },
            { value: "CANCELLED", label: "Cancelled" },
          ].map((status) => (
            <button
              key={status.value}
              type="button"
              onClick={() => setFilter(status.value as any)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                filter === status.value
                  ? "bg-blue-600 text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              {status.label}
            </button>
          ))}
        </div>
      </div>

      {filteredBookings.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">
            {filter === "all"
              ? "No bookings found"
              : `No ${filter} bookings found`}
          </p>
        </div>
      ) : (
        <div className="grid gap-4">
          {filteredBookings.map((booking) => (
            <div
              key={booking.id}
              className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {booking.resort
                      ? booking.resort.name
                      : booking.spaTreatment?.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {booking.resort
                      ? `Resort • ${booking.resort.location}`
                      : "Spa Treatment"}
                  </p>
                  {!userBookingsOnly && (
                    <p className="text-sm text-gray-600">
                      Customer: {booking.userEmail}
                    </p>
                  )}
                </div>
                <span
                  className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(
                    booking.status
                  )}`}
                >
                  {booking.status.charAt(0).toUpperCase() +
                    booking.status.slice(1)}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm font-medium text-gray-700">Check-in</p>
                  <p className="text-sm text-gray-600">
                    {new Date(booking.checkIn).toLocaleDateString()}
                  </p>
                </div>
                {booking.checkOut && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">
                      Check-out
                    </p>
                    <p className="text-sm text-gray-600">
                      {new Date(booking.checkOut).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>

              {booking.notes && (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700">Notes</p>
                  <p className="text-sm text-gray-600">{booking.notes}</p>
                </div>
              )}

              <div className="flex justify-between items-center">
                <p className="text-xs text-gray-500">
                  Booked on {new Date(booking.createdAt).toLocaleDateString()}
                </p>

                {canUpdateBooking(booking) && (
                  <div className="flex space-x-2">
                    {booking.status === "PENDING" &&
                      !userBookingsOnly &&
                      isStaff() && (
                        <button
                          type="button"
                          onClick={() =>
                            updateBookingStatus(booking.id, "CONFIRMED")
                          }
                          disabled={updatingBooking === booking.id}
                          className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50"
                        >
                          {updatingBooking === booking.id ? "..." : "Confirm"}
                        </button>
                      )}
                    {booking.status !== "CANCELLED" && (
                      <button
                        type="button"
                        onClick={() =>
                          updateBookingStatus(booking.id, "CANCELLED")
                        }
                        disabled={updatingBooking === booking.id}
                        className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50"
                      >
                        {updatingBooking === booking.id ? "..." : "Cancel"}
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
