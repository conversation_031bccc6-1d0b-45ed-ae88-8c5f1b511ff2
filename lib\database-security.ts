import { PrismaClient } from '@prisma/client';

// Database security configuration
const databaseConfig = {
  // Connection pool settings
  connectionLimit: 10,
  acquireTimeout: 60000,
  timeout: 60000,
  
  // SSL/TLS settings for production
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: true,
    ca: process.env.DB_SSL_CA,
    cert: process.env.DB_SSL_CERT,
    key: process.env.DB_SSL_KEY,
  } : undefined,
  
  // Logging configuration
  log: process.env.NODE_ENV === 'development' 
    ? ['query', 'info', 'warn', 'error'] as const
    : ['warn', 'error'] as const,
};

// Enhanced Prisma client with security features
class SecurePrismaClient extends PrismaClient {
  constructor() {
    super({
      log: databaseConfig.log,
      datasources: {
        db: {
          url: process.env.MONGODB_URI,
        },
      },
    });

    // Add query logging middleware for security monitoring
    this.$use(async (params, next) => {
      const start = Date.now();
      
      try {
        const result = await next(params);
        const duration = Date.now() - start;
        
        // Log slow queries for performance monitoring
        if (duration > 1000) {
          console.warn(`Slow query detected: ${params.model}.${params.action} took ${duration}ms`);
        }
        
        return result;
      } catch (error) {
        // Log database errors for security monitoring
        console.error('Database error:', {
          model: params.model,
          action: params.action,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        });
        throw error;
      }
    });

    // Add data sanitization middleware
    this.$use(async (params, next) => {
      // Sanitize string inputs to prevent injection attacks
      if (params.args?.data && typeof params.args.data === 'object') {
        params.args.data = sanitizeData(params.args.data);
      }
      
      if (params.args?.where && typeof params.args.where === 'object') {
        params.args.where = sanitizeData(params.args.where);
      }
      
      return next(params);
    });
  }

  // Enhanced connection management
  async connect() {
    try {
      await this.$connect();
      console.log('✅ Database connected successfully');
      
      // Verify database health
      await this.healthCheck();
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      await this.$disconnect();
      console.log('✅ Database disconnected successfully');
    } catch (error) {
      console.error('❌ Database disconnection failed:', error);
      throw error;
    }
  }

  // Database health check
  async healthCheck() {
    try {
      // Simple query to verify database is responsive
      await this.$queryRaw`SELECT 1`;
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Database health check failed:', error);
      return { status: 'unhealthy', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Secure query execution with rate limiting
  async executeSecureQuery<T>(
    operation: () => Promise<T>,
    context: { userId?: string; action: string; resource: string }
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await operation();
      const duration = Date.now() - startTime;
      
      // Log successful operations
      console.log(`Database operation completed: ${context.action} on ${context.resource} (${duration}ms)`);
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Log failed operations for security monitoring
      console.error(`Database operation failed: ${context.action} on ${context.resource} (${duration}ms)`, {
        userId: context.userId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
      
      throw error;
    }
  }
}

// Data sanitization function
function sanitizeData(data: any): any {
  if (typeof data === 'string') {
    // Remove potential NoSQL injection patterns
    return data
      .replace(/\$where/gi, '')
      .replace(/\$regex/gi, '')
      .replace(/\$ne/gi, '')
      .replace(/\$gt/gi, '')
      .replace(/\$lt/gi, '')
      .replace(/\$in/gi, '')
      .replace(/\$nin/gi, '')
      .trim();
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeData);
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      // Skip potentially dangerous keys
      if (!key.startsWith('$') && !key.includes('..')) {
        sanitized[key] = sanitizeData(value);
      }
    }
    return sanitized;
  }
  
  return data;
}

// Database backup configuration
export const backupConfig = {
  enabled: process.env.NODE_ENV === 'production',
  schedule: '0 2 * * *', // Daily at 2 AM
  retention: 30, // Keep backups for 30 days
  compression: true,
  encryption: true,
  destinations: [
    {
      type: 'local',
      path: './backups',
    },
    {
      type: 's3',
      bucket: process.env.BACKUP_S3_BUCKET,
      region: process.env.BACKUP_S3_REGION,
    },
  ],
};

// Connection pool monitoring
export function monitorConnectionPool(prisma: PrismaClient) {
  setInterval(async () => {
    try {
      const metrics = await (prisma as any)._engine.getMetrics();
      
      if (metrics.poolSize > 8) {
        console.warn('High database connection pool usage:', metrics);
      }
    } catch (error) {
      // Metrics not available in all Prisma versions
    }
  }, 30000); // Check every 30 seconds
}

// Create singleton instance
const globalForPrisma = globalThis as unknown as {
  prisma: SecurePrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new SecurePrismaClient();

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// Initialize connection monitoring in production
if (process.env.NODE_ENV === 'production') {
  monitorConnectionPool(prisma);
}

export default prisma;
