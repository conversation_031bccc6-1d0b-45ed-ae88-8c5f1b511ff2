"use server";

import { prisma } from "./prisma";
import { revalidatePath } from "next/cache";

export async function deleteResort(formData: FormData) {
  const id = formData.get("id") as string;

  try {
    await prisma.resort.delete({ where: { id } });
    revalidatePath("/admin/resorts");
  } catch (err) {
    console.error("Delete failed", err);
  }
}

export async function deleteTreatment(formData: FormData) {
  const id = formData.get("id") as string;
  await prisma.spaTreatment.delete({ where: { id } });
  revalidatePath("/admin/spa");
}
