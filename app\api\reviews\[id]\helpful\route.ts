import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { logAuditEvent } from "@/lib/security";

// POST /api/reviews/[id]/helpful - Mark review as helpful
export async function POST(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if review exists
    const review = await prisma.review.findUnique({
      where: { id: params.id },
    });

    if (!review) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    if (!review.isPublished) {
      return NextResponse.json({ error: "Review not available" }, { status: 404 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Prevent users from marking their own reviews as helpful
    if (review.userId === user.id) {
      return NextResponse.json(
        { error: "Cannot mark your own review as helpful" },
        { status: 400 }
      );
    }

    // For now, we'll just increment the helpful votes
    // In a more complex system, you might want to track which users voted
    const updatedReview = await prisma.review.update({
      where: { id: params.id },
      data: {
        helpfulVotes: {
          increment: 1,
        },
      },
    });

    await logAuditEvent({
      action: "REVIEW_MARKED_HELPFUL",
      resource: `review_${params.id}`,
      userId: user.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json({
      message: "Review marked as helpful",
      helpfulVotes: updatedReview.helpfulVotes,
    });

  } catch (error) {
    console.error("Error marking review as helpful:", error);
    return NextResponse.json(
      { error: "Failed to mark review as helpful" },
      { status: 500 }
    );
  }
}
