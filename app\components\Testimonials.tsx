"use client";
import { motion } from "framer-motion";

const testimonials = [
  {
    name: "<PERSON><PERSON> T.",
    comment: "Amazing resort experience! Great hospitality and stunning views.",
  },
  {
    name: "<PERSON>",
    comment: "The spa treatments were exceptional. Highly recommend <PERSON><PERSON><PERSON>!",
  },
];

export default function Testimonials() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-4xl mx-auto px-4 text-center">
        <motion.h2
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-3xl font-bold mb-10"
        >
          What Our Guests Say
        </motion.h2>
        <div className="grid gap-6 md:grid-cols-2">
          {testimonials.map((t, i) => (
            <motion.div
              key={i}
              whileHover={{ scale: 1.02 }}
              className="p-6 bg-gray-50 rounded-xl shadow"
            >
              <p className="text-gray-700 italic mb-3">“{t.comment}”</p>
              <p className="font-semibold text-black">{t.name}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
