"use client";

import { useState } from "react";
import Image from "next/image";
import { format } from "date-fns";
import { ReviewCardProps } from "@/app/types/review";
import StarRating from "./StarRating";
import { motion } from "framer-motion";

const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  showActions = false,
  onHelpful,
  onReport,
  className = "",
}) => {
  const [isHelpfulLoading, setIsHelpfulLoading] = useState(false);
  const [isReportLoading, setIsReportLoading] = useState(false);
  const [showFullComment, setShowFullComment] = useState(false);

  const handleHelpful = async () => {
    if (!onHelpful || isHelpfulLoading) return;

    setIsHelpfulLoading(true);
    try {
      await onHelpful(review.id);
    } catch (error) {
      console.error("Error marking review as helpful:", error);
    } finally {
      setIsHelpfulLoading(false);
    }
  };

  const handleReport = async () => {
    if (!onReport || isReportLoading) return;

    setIsReportLoading(true);
    try {
      await onReport(review.id);
    } catch (error) {
      console.error("Error reporting review:", error);
    } finally {
      setIsReportLoading(false);
    }
  };

  const getContentName = () => {
    if (review.resort) return review.resort.name;
    if (review.room) return `${review.room.name} (${review.room.type})`;
    if (review.spaTreatment) return review.spaTreatment.name;
    if (review.experience) return review.experience.name;
    if (review.wellnessService) return review.wellnessService.name;
    if (review.event) return review.event.name;
    return "Unknown Service";
  };

  const getContentType = () => {
    if (review.resort) return "Resort";
    if (review.room) return "Room";
    if (review.spaTreatment) return "Spa Treatment";
    if (review.experience) return "Experience";
    if (review.wellnessService) return "Wellness Service";
    if (review.event) return "Event";
    return "Service";
  };

  const shouldTruncateComment = review.comment.length > 200;
  const displayComment =
    shouldTruncateComment && !showFullComment
      ? review.comment.substring(0, 200) + "..."
      : review.comment;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow ${className}`}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          {review.user?.image ? (
            <Image
              src={review.user.image}
              alt={review.user.name || "Guest"}
              width={48}
              height={48}
              className="rounded-full object-cover"
            />
          ) : (
            <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-gray-600 font-medium text-lg">
                {(review.user?.name || "G").charAt(0).toUpperCase()}
              </span>
            </div>
          )}

          <div>
            <div className="flex items-center space-x-2">
              <h4 className="font-semibold text-gray-900">
                {review.user?.name || "Anonymous Guest"}
              </h4>
              {review.isVerified && (
                <span
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                  title="Verified guest with confirmed booking"
                >
                  ✓ Verified
                </span>
              )}
            </div>
            <p className="text-sm text-gray-500">
              {format(new Date(review.createdAt), "MMM d, yyyy")}
            </p>
          </div>
        </div>

        <div className="text-right">
          <StarRating rating={review.rating} size="sm" />
          <p className="text-xs text-gray-500 mt-1">{getContentType()}</p>
        </div>
      </div>

      {/* Service/Content Info */}
      <div className="mb-3">
        <p className="text-sm font-medium text-gray-700">
          Reviewed: <span className="text-blue-600">{getContentName()}</span>
        </p>
      </div>

      {/* Review Title */}
      {review.title && (
        <h5 className="font-semibold text-gray-900 mb-2">{review.title}</h5>
      )}

      {/* Review Comment */}
      <div className="mb-4">
        <p className="text-gray-700 leading-relaxed">{displayComment}</p>

        {shouldTruncateComment && (
          <button
            type="button"
            onClick={() => setShowFullComment(!showFullComment)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium mt-1 focus:outline-none focus:underline"
          >
            {showFullComment ? "Show less" : "Read more"}
          </button>
        )}
      </div>

      {/* Review Images */}
      {review.images && review.images.length > 0 && (
        <div className="mb-4">
          <div className="flex space-x-2 overflow-x-auto">
            {review.images.map((image, index) => (
              <Image
                key={index}
                src={image}
                alt={`Review image ${index + 1}`}
                width={80}
                height={80}
                className="rounded-lg object-cover flex-shrink-0"
              />
            ))}
          </div>
        </div>
      )}

      {/* Actions */}
      {showActions && (
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-4">
            <button
              type="button"
              onClick={handleHelpful}
              disabled={isHelpfulLoading}
              className="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded px-2 py-1"
              aria-label="Mark this review as helpful"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v10M9 7H6a2 2 0 00-2 2v8a2 2 0 002 2h8.5"
                />
              </svg>
              <span className="text-sm">Helpful ({review.helpfulVotes})</span>
            </button>

            <button
              type="button"
              onClick={handleReport}
              disabled={isReportLoading}
              className="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 rounded px-2 py-1"
              aria-label="Report this review"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <span className="text-sm">Report</span>
            </button>
          </div>

          <div className="text-xs text-gray-400">
            {review.reportCount > 0 && (
              <span>Reported {review.reportCount} times</span>
            )}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default ReviewCard;
