import DiningForm from "@/app/components/admin/DiningForm";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

export default async function EditDining({ params }: { params: { id: string } }) {
  const diningOption = await prisma.diningOption.findUnique({
    where: { id: params.id },
  });

  if (!diningOption) return notFound();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Edit Dining Option</h1>
        <p className="text-gray-600 mt-1">
          Update the details for {diningOption.name}
        </p>
      </div>
      <DiningForm initialData={diningOption} />
    </div>
  );
}
