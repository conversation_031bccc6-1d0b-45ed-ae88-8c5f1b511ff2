import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { logAuditEvent } from "@/lib/security";

// GET /api/reviews/[id] - Get a specific review
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const review = await prisma.review.findUnique({
      where: { id: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        resort: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        room: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        spaTreatment: {
          select: {
            id: true,
            name: true,
          },
        },
        experience: {
          select: {
            id: true,
            name: true,
          },
        },
        wellnessService: {
          select: {
            id: true,
            name: true,
          },
        },
        event: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!review) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    // Only show published reviews to non-admin users
    const session = await getServerSession(authOptions);
    const isAdmin = session?.user?.role === 'admin';
    const isOwner = session?.user?.email && review.user.id === session.user.id;

    if (!review.isPublished && !isAdmin && !isOwner) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    return NextResponse.json(review);

  } catch (error) {
    console.error("Error fetching review:", error);
    return NextResponse.json(
      { error: "Failed to fetch review" },
      { status: 500 }
    );
  }
}

// PUT /api/reviews/[id] - Update a review
export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await req.json();

    // Get the existing review
    const existingReview = await prisma.review.findUnique({
      where: { id: params.id },
      include: {
        user: true,
      },
    });

    if (!existingReview) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    // Check if user owns the review or is admin
    const isAdmin = session.user.role === 'admin';
    const isOwner = existingReview.user.email === session.user.email;

    if (!isAdmin && !isOwner) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Validate rating if provided
    if (data.rating && (data.rating < 1 || data.rating > 5)) {
      return NextResponse.json(
        { error: "Rating must be between 1 and 5" },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    
    if (data.rating !== undefined) updateData.rating = data.rating;
    if (data.title !== undefined) updateData.title = data.title;
    if (data.comment !== undefined) updateData.comment = data.comment;
    if (data.images !== undefined) updateData.images = data.images;

    // Admin-only fields
    if (isAdmin) {
      if (data.isApproved !== undefined) updateData.isApproved = data.isApproved;
      if (data.isPublished !== undefined) updateData.isPublished = data.isPublished;
      if (data.isVerified !== undefined) updateData.isVerified = data.isVerified;
    }

    // If user updates their review, reset approval status
    if (!isAdmin && (data.rating || data.comment || data.title)) {
      updateData.isApproved = false;
      updateData.isPublished = false;
    }

    const updatedReview = await prisma.review.update({
      where: { id: params.id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    await logAuditEvent({
      action: "REVIEW_UPDATED",
      resource: `review_${params.id}`,
      userId: existingReview.user.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json(updatedReview);

  } catch (error) {
    console.error("Error updating review:", error);
    return NextResponse.json(
      { error: "Failed to update review" },
      { status: 500 }
    );
  }
}

// DELETE /api/reviews/[id] - Delete a review
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the existing review
    const existingReview = await prisma.review.findUnique({
      where: { id: params.id },
      include: {
        user: true,
      },
    });

    if (!existingReview) {
      return NextResponse.json({ error: "Review not found" }, { status: 404 });
    }

    // Check if user owns the review or is admin
    const isAdmin = session.user.role === 'admin';
    const isOwner = existingReview.user.email === session.user.email;

    if (!isAdmin && !isOwner) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    await prisma.review.delete({
      where: { id: params.id },
    });

    await logAuditEvent({
      action: "REVIEW_DELETED",
      resource: `review_${params.id}`,
      userId: existingReview.user.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json({ message: "Review deleted successfully" });

  } catch (error) {
    console.error("Error deleting review:", error);
    return NextResponse.json(
      { error: "Failed to delete review" },
      { status: 500 }
    );
  }
}
