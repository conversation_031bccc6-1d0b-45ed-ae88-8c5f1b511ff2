import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const diningOptions = await prisma.diningOption.findMany({
      orderBy: { createdAt: "desc" },
    });
    return NextResponse.json(diningOptions);
  } catch (error) {
    console.error("Error fetching dining options:", error);
    return NextResponse.json(
      { error: "Failed to fetch dining options" },
      { status: 500 }
    );
  }
}
