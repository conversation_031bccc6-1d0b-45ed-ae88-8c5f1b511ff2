import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  const resorts = await prisma.resort.findMany({
    orderBy: { createdAt: "desc" },
  });
  return NextResponse.json(resorts);
}

export async function POST(req: Request) {
  const data = await req.json();

  const newResort = await prisma.resort.create({
    data,
  });

  return NextResponse.json(newResort);
}
