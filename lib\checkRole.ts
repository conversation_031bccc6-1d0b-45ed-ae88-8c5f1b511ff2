import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "./prisma";
import { NextResponse } from "next/server";

// Define role hierarchy for permission inheritance
const ROLE_HIERARCHY = {
  admin: ["admin", "manager", "receptionist", "user"],
  manager: ["manager", "receptionist", "user"],
  receptionist: ["receptionist", "user"],
  user: ["user"],
} as const;

export type UserRole = keyof typeof ROLE_HIERARCHY;

export async function checkUserRole(allowedRoles: string[]): Promise<boolean> {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) return false;

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    select: { role: true },
  });

  if (!user) return false;

  // Normalize roles to lowercase
  const userRole = user.role.toLowerCase();
  const normalizedAllowedRoles = allowedRoles.map((role) => role.toLowerCase());

  return normalizedAllowedRoles.includes(userRole);
}

export async function requireRole(
  allowedRoles: string[]
): Promise<{ user: any } | NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
  });

  if (!user) {
    return NextResponse.json({ error: "User not found" }, { status: 404 });
  }

  const userRole = user.role.toLowerCase();
  const normalizedAllowedRoles = allowedRoles.map((role) => role.toLowerCase());

  if (!normalizedAllowedRoles.includes(userRole)) {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  return { user };
}

export async function getCurrentUserWithRole() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) return null;

  return await prisma.user.findUnique({
    where: { email: session.user.email },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      createdAt: true,
    },
  });
}
