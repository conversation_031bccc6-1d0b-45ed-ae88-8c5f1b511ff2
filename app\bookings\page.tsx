import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import BookingHistory from "../components/user/BookingHistory";
import { ListItemSkeleton } from "../components/ui/SkeletonComponents";
import {
  LoadingError,
  PermissionError,
} from "../components/ui/ErrorComponents";
import ErrorBoundary from "../components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface BookingPageProps {
  searchParams?: {
    tab?: "resorts" | "spa" | "all";
    status?: "pending" | "confirmed" | "cancelled";
  };
}

interface UserBookingData {
  bookings: any[];
  spaBookings: any[];
}

// Loading component for booking history
function BookingHistoryLoading() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-10">
      <div className="mb-8">
        <div className="h-8 bg-gray-200 rounded w-64 mb-4 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>
      </div>

      <div className="space-y-6">
        <div>
          <div className="h-6 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <ListItemSkeleton key={index} />
            ))}
          </div>
        </div>

        <div>
          <div className="h-6 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
          <div className="space-y-4">
            {Array.from({ length: 2 }).map((_, index) => (
              <ListItemSkeleton key={index} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Server component to fetch user data
async function UserBookingsData({ userEmail }: { userEmail: string }) {
  try {
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      include: {
        bookings: {
          include: {
            room: { include: { resort: true } },
          },
          orderBy: { checkIn: "desc" },
        },
        spaBookings: {
          include: {
            treatment: true,
          },
          orderBy: { date: "desc" },
        },
      },
    });

    if (!user) {
      return (
        <LoadingError
          resource="user data"
          onRetry={() => window.location.reload()}
        />
      );
    }

    return (
      <BookingHistory
        resortBookings={user.bookings || []}
        spaBookings={user.spaBookings || []}
      />
    );
  } catch (error) {
    console.error("Error fetching user bookings:", error);
    return (
      <LoadingError
        resource="booking data"
        onRetry={() => window.location.reload()}
      />
    );
  }
}

export default async function BookingPage({ searchParams }: BookingPageProps) {
  const session = await getServerSession(authOptions);

  // Authentication check
  if (!session?.user?.email) {
    return redirect("/login");
  }

  return (
    <ErrorBoundary
      onError={(error) => {
        console.error("Booking page error:", error);
      }}
    >
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-10">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              My Bookings
            </h1>
            <p className="text-gray-600">
              View and manage your resort and spa bookings
            </p>
          </div>

          <Suspense fallback={<BookingHistoryLoading />}>
            <UserBookingsData userEmail={session.user.email} />
          </Suspense>
        </div>
      </div>
    </ErrorBoundary>
  );
}
