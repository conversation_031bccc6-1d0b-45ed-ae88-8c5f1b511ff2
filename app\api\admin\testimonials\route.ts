import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";
import { logAuditEvent } from "@/lib/security";

// GET /api/admin/testimonials - Get all testimonials for admin management
export async function GET(req: Request) {
  // Check authorization - only admin and manager can access
  const authResult = await requireRole(["admin", "manager"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { searchParams } = new URL(req.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status'); // 'pending', 'approved', 'published', 'featured'
    const sortBy = searchParams.get('sortBy') || 'newest';

    // Build where clause
    const where: any = {};

    if (status === 'pending') {
      where.isApproved = false;
    } else if (status === 'approved') {
      where.isApproved = true;
      where.isPublished = false;
    } else if (status === 'published') {
      where.isPublished = true;
    } else if (status === 'featured') {
      where.isFeatured = true;
      where.isPublished = true;
    }

    // Build orderBy clause
    let orderBy: any = { createdAt: 'desc' };
    
    switch (sortBy) {
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'featured':
        orderBy = [
          { isFeatured: 'desc' },
          { displayOrder: 'asc' },
          { createdAt: 'desc' }
        ];
        break;
      case 'order':
        orderBy = [
          { displayOrder: 'asc' },
          { createdAt: 'desc' }
        ];
        break;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Fetch testimonials with relations
    const [testimonials, total] = await Promise.all([
      prisma.testimonial.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      }),
      prisma.testimonial.count({ where }),
    ]);

    // Get summary statistics
    const stats = await prisma.testimonial.groupBy({
      by: ['isApproved', 'isPublished', 'isFeatured'],
      _count: {
        id: true,
      },
    });

    const summary = {
      total,
      pending: stats.find(s => !s.isApproved)?._count.id || 0,
      approved: stats.find(s => s.isApproved && !s.isPublished)?._count.id || 0,
      published: stats.find(s => s.isPublished)?._count.id || 0,
      featured: stats.find(s => s.isFeatured)?._count.id || 0,
    };

    return NextResponse.json({
      testimonials,
      total,
      page,
      limit,
      summary,
    });

  } catch (error) {
    console.error("Error fetching admin testimonials:", error);
    return NextResponse.json(
      { error: "Failed to fetch testimonials" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/testimonials - Bulk update testimonials
export async function PATCH(req: Request) {
  // Check authorization - only admin can bulk update
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const { testimonialIds, action, reason, displayOrder } = await req.json();

    if (!testimonialIds || !Array.isArray(testimonialIds) || testimonialIds.length === 0) {
      return NextResponse.json(
        { error: "Testimonial IDs are required" },
        { status: 400 }
      );
    }

    if (!action) {
      return NextResponse.json(
        { error: "Action is required" },
        { status: 400 }
      );
    }

    let updateData: any = {};

    switch (action) {
      case 'approve':
        updateData = { isApproved: true };
        break;
      case 'reject':
        updateData = { isApproved: false, isPublished: false, isFeatured: false };
        break;
      case 'publish':
        updateData = { isApproved: true, isPublished: true };
        break;
      case 'unpublish':
        updateData = { isPublished: false, isFeatured: false };
        break;
      case 'feature':
        updateData = { 
          isApproved: true, 
          isPublished: true, 
          isFeatured: true,
          displayOrder: displayOrder || null
        };
        break;
      case 'unfeature':
        updateData = { isFeatured: false, displayOrder: null };
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }

    // Update testimonials
    const result = await prisma.testimonial.updateMany({
      where: {
        id: { in: testimonialIds },
      },
      data: updateData,
    });

    await logAuditEvent({
      action: `TESTIMONIALS_BULK_${action.toUpperCase()}`,
      resource: `testimonials_bulk`,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
      details: `Updated ${result.count} testimonials with action: ${action}${reason ? `, reason: ${reason}` : ''}`,
    });

    return NextResponse.json({
      message: `Successfully ${action}ed ${result.count} testimonials`,
      updatedCount: result.count,
    });

  } catch (error) {
    console.error("Error bulk updating testimonials:", error);
    return NextResponse.json(
      { error: "Failed to update testimonials" },
      { status: 500 }
    );
  }
}
