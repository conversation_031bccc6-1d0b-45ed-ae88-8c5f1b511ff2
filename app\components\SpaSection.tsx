"use client";
import { motion } from "framer-motion";

export default function SpaSection() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 flex flex-col md:flex-row items-center gap-10">
        <motion.img
          src="/images/spa.jpg"
          alt="Boston Day Spa"
          className="rounded-xl w-full md:w-1/2 shadow-lg"
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
        />
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          className="md:w-1/2"
        >
          <h2 className="text-3xl font-bold mb-4">Boston Day Spa</h2>
          <p className="mb-6">
            Discover world-class treatments, traditional therapies, and
            luxurious self-care in the heart of Ethiopia.
          </p>
          <button className="bg-black text-white px-6 py-3 rounded-xl font-medium hover:bg-gray-800 transition">
            View Spa Services
          </button>
        </motion.div>
      </div>
    </section>
  );
}
