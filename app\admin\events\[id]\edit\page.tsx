import EventForm from "@/app/components/admin/EventForm";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

export default async function EditEvent({ params }: { params: { id: string } }) {
  const event = await prisma.event.findUnique({
    where: { id: params.id },
  });

  if (!event) return notFound();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Edit Event</h1>
        <p className="text-gray-600 mt-1">
          Update the details for {event.name}
        </p>
      </div>
      <EventForm initialData={event} />
    </div>
  );
}
