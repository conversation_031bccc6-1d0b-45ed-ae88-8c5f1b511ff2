import { prisma } from "@/lib/prisma";
import DynamicSpaServiceCards from "@/app/components/DynamicSpaServiceCards";
import DynamicContentSection from "@/app/components/DynamicContentSection";
import { Suspense } from "react";
import ResortsWithFilters from "@/app/components/ResortsWithFilters";

// Loading component for resort cards
function ResortCardSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow animate-pulse">
      <div className="w-full h-48 bg-gray-200 rounded-t-xl"></div>
      <div className="p-4">
        <div className="h-6 bg-gray-200 rounded mx-auto w-3/4"></div>
      </div>
    </div>
  );
}

export default async function ResortsListPage() {
  // Fetch initial resorts data
  const initialResorts = await prisma.resort.findMany({
    orderBy: { createdAt: "desc" },
  });

  return (
    <div className="max-w-7xl mx-auto px-4 py-10">
      {/* Resorts Section */}
      <section className="mb-16">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Our Resorts</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our collection of luxury resorts, each offering unique
            experiences and world-class amenities.
          </p>
        </div>

        <Suspense
          fallback={
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="h-10 bg-gray-200 rounded"></div>
                  <div className="h-10 bg-gray-200 rounded"></div>
                  <div className="h-10 bg-gray-200 rounded"></div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {Array.from({ length: 8 }).map((_, i) => (
                  <ResortCardSkeleton key={i} />
                ))}
              </div>
            </div>
          }
        >
          <ResortsWithFilters
            initialResorts={initialResorts}
            enableRealTimeUpdates={true}
            updateInterval={30000}
          />
        </Suspense>
      </section>

      {/* Spa Services Section */}
      <DynamicSpaServiceCards
        enableRealTimeUpdates={true}
        updateInterval={30000}
        showTitle={true}
        className="mb-16"
      />

      {/* Accommodation Section */}
      <DynamicContentSection
        title="Luxury Accommodation"
        description="Experience comfort and elegance in our carefully designed rooms and suites, each offering stunning views and premium amenities."
        apiEndpoint="/api/accommodation"
        contentType="accommodation"
        enableRealTimeUpdates={true}
        updateInterval={30000}
        maxItems={8}
        className="mb-16"
      />

      {/* Experiences Section */}
      <div id="experiences">
        <DynamicContentSection
          title="Unforgettable Experiences"
          description="Discover thrilling adventures and cultural experiences that will create lasting memories during your stay."
          apiEndpoint="/api/experience"
          contentType="experience"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={8}
          className="mb-16"
        />
      </div>

      {/* Wellness Section */}
      <div id="wellness">
        <DynamicContentSection
          title="Wellness & Fitness"
          description="Rejuvenate your body and mind with our comprehensive wellness programs, fitness classes, and therapeutic services."
          apiEndpoint="/api/wellness"
          contentType="wellness"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Dining Section */}
      <div id="dining">
        <DynamicContentSection
          title="Exceptional Dining"
          description="Savor exquisite flavors from around the world at our diverse restaurants, cafes, and bars."
          apiEndpoint="/api/dining"
          contentType="dining"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Events Section */}
      <div id="events">
        <DynamicContentSection
          title="Special Events"
          description="Host your special occasions in our elegant venues, from intimate celebrations to grand corporate events."
          apiEndpoint="/api/event"
          contentType="event"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Special Offers Section */}
      <div id="offers">
        <DynamicContentSection
          title="Special Offers & Packages"
          description="Take advantage of our exclusive deals and packages designed to make your stay even more memorable."
          apiEndpoint="/api/offer"
          contentType="offer"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={4}
          gridCols={2}
          className="mb-16"
        />
      </div>
    </div>
  );
}
