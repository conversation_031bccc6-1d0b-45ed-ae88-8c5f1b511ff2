import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const data = await req.json();
    
    const wellnessService = await prisma.wellnessService.update({
      where: { id: params.id },
      data,
    });
    return NextResponse.json(wellnessService);
  } catch (error) {
    console.error("Error updating wellness service:", error);
    return NextResponse.json(
      { error: "Failed to update wellness service" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    await prisma.wellnessService.delete({
      where: { id: params.id },
    });
    return NextResponse.json({ message: "Wellness service deleted successfully" });
  } catch (error) {
    console.error("Error deleting wellness service:", error);
    return NextResponse.json(
      { error: "Failed to delete wellness service" },
      { status: 500 }
    );
  }
}

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const wellnessService = await prisma.wellnessService.findUnique({
      where: { id: params.id },
    });

    if (!wellnessService) {
      return NextResponse.json(
        { error: "Wellness service not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(wellnessService);
  } catch (error) {
    console.error("Error fetching wellness service:", error);
    return NextResponse.json(
      { error: "Failed to fetch wellness service" },
      { status: 500 }
    );
  }
}
