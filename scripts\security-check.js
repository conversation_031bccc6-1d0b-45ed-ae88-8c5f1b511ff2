#!/usr/bin/env node

/**
 * Security Check Script
 * Performs comprehensive security audits and checks
 */

import path from "path";
import fs from "fs";

class SecurityChecker {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.info = [];
  }

  addIssue(severity, category, message, file = null) {
    const issue = {
      severity,
      category,
      message,
      file,
      timestamp: new Date().toISOString(),
    };

    switch (severity) {
      case "critical":
      case "high":
        this.issues.push(issue);
        break;
      case "medium":
        this.warnings.push(issue);
        break;
      case "low":
      case "info":
        this.info.push(issue);
        break;
    }
  }

  checkEnvironmentSecurity() {
    console.log("🔍 Checking environment security...");

    // Check if .env exists in git
    if (fs.existsSync(".env") && fs.existsSync(".git")) {
      try {
        const gitignore = fs.readFileSync(".gitignore", "utf8");
        if (!gitignore.includes(".env")) {
          this.addIssue(
            "critical",
            "Environment",
            ".env file not in .gitignore - secrets may be exposed!"
          );
        }
      } catch {
        this.addIssue("high", "Environment", ".gitignore file not found");
      }
    }

    // Check environment variables
    if (fs.existsSync(".env")) {
      const envContent = fs.readFileSync(".env", "utf8");
      const lines = envContent
        .split("\n")
        .filter((line) => line.trim() && !line.startsWith("#"));

      for (const line of lines) {
        const [key, value] = line.split("=");

        if (key && value) {
          // Check for weak secrets
          if (key.includes("SECRET") || key.includes("KEY")) {
            if (value.length < 32) {
              this.addIssue(
                "high",
                "Environment",
                `Weak secret detected: ${key} (should be at least 32 characters)`
              );
            }
            if (value === "your-secret-key-here" || value === "change-me") {
              this.addIssue(
                "critical",
                "Environment",
                `Default secret value detected: ${key}`
              );
            }
          }

          // Check for exposed credentials
          if (value.includes("password") || value.includes("admin")) {
            this.addIssue(
              "medium",
              "Environment",
              `Potentially exposed credential in ${key}`
            );
          }
        }
      }
    }
  }

  checkDependencySecurity() {
    console.log("🔍 Checking dependency security...");

    if (fs.existsSync("package.json")) {
      const packageJson = JSON.parse(fs.readFileSync("package.json", "utf8"));

      // Check for known vulnerable packages
      const vulnerablePackages = [
        "lodash@4.17.20",
        "axios@0.21.0",
        "node-fetch@2.6.6",
      ];

      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies,
      };

      for (const [pkg, version] of Object.entries(allDeps)) {
        const pkgVersion = `${pkg}@${version}`;
        if (vulnerablePackages.some((vuln) => pkgVersion.includes(vuln))) {
          this.addIssue(
            "high",
            "Dependencies",
            `Potentially vulnerable package: ${pkgVersion}`
          );
        }
      }

      // Check for missing security packages
      const securityPackages = ["helmet", "cors", "express-rate-limit"];
      for (const pkg of securityPackages) {
        if (!allDeps[pkg]) {
          this.addIssue(
            "medium",
            "Dependencies",
            `Missing security package: ${pkg}`
          );
        }
      }
    }
  }

  checkCodeSecurity() {
    console.log("🔍 Checking code security...");

    const codeFiles = this.findFiles(".", [".js", ".ts", ".jsx", ".tsx"]);

    for (const file of codeFiles) {
      if (file.includes("node_modules") || file.includes(".git")) continue;

      try {
        const content = fs.readFileSync(file, "utf8");

        // Check for hardcoded secrets
        const secretPatterns = [
          /password\s*=\s*["'][^"']+["']/gi,
          /api[_-]?key\s*=\s*["'][^"']+["']/gi,
          /secret\s*=\s*["'][^"']+["']/gi,
          /token\s*=\s*["'][^"']+["']/gi,
        ];

        for (const pattern of secretPatterns) {
          const matches = content.match(pattern);
          if (matches) {
            this.addIssue(
              "high",
              "Code",
              `Potential hardcoded secret in ${file}: ${matches[0]}`,
              file
            );
          }
        }

        // Check for SQL injection vulnerabilities
        if (content.includes("query(") && content.includes("${")) {
          this.addIssue(
            "high",
            "Code",
            `Potential SQL injection vulnerability in ${file}`,
            file
          );
        }

        // Check for XSS vulnerabilities
        if (
          content.includes("dangerouslySetInnerHTML") ||
          content.includes("innerHTML")
        ) {
          this.addIssue(
            "medium",
            "Code",
            `Potential XSS vulnerability in ${file}`,
            file
          );
        }

        // Check for missing input validation
        if (content.includes("req.body") && !content.includes("validate")) {
          this.addIssue(
            "medium",
            "Code",
            `Missing input validation in ${file}`,
            file
          );
        }

        // Check for missing authentication
        if (
          content.includes("export async function") &&
          content.includes("/api/") &&
          !content.includes("auth")
        ) {
          this.addIssue(
            "medium",
            "Code",
            `Potential missing authentication in API route ${file}`,
            file
          );
        }
      } catch {
        // Skip files that can't be read
      }
    }
  }

  checkDatabaseSecurity() {
    console.log("🔍 Checking database security...");

    // Check Prisma schema
    if (fs.existsSync("prisma/schema.prisma")) {
      const schema = fs.readFileSync("prisma/schema.prisma", "utf8");

      // Check for missing indexes
      if (!schema.includes("@@index")) {
        this.addIssue(
          "medium",
          "Database",
          "No database indexes found - may impact performance"
        );
      }

      // Check for missing constraints
      if (!schema.includes("@unique")) {
        this.addIssue(
          "low",
          "Database",
          "Consider adding unique constraints for data integrity"
        );
      }

      // Check for sensitive data without encryption
      const sensitiveFields = ["password", "ssn", "credit_card", "phone"];
      for (const field of sensitiveFields) {
        if (schema.includes(field) && !schema.includes("@encrypted")) {
          this.addIssue(
            "medium",
            "Database",
            `Sensitive field '${field}' may need encryption`
          );
        }
      }
    }
  }

  checkFilePermissions() {
    console.log("🔍 Checking file permissions...");

    const sensitiveFiles = [".env", "prisma/schema.prisma", "package.json"];

    for (const file of sensitiveFiles) {
      if (fs.existsSync(file)) {
        try {
          const stats = fs.statSync(file);
          const mode = stats.mode & parseInt("777", 8);

          if (mode > parseInt("644", 8)) {
            this.addIssue(
              "medium",
              "Permissions",
              `File ${file} has overly permissive permissions: ${mode.toString(
                8
              )}`
            );
          }
        } catch {
          // Skip if can't check permissions
        }
      }
    }
  }

  findFiles(dir, extensions) {
    const files = [];

    try {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          files.push(...this.findFiles(fullPath, extensions));
        } else if (extensions.some((ext) => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch {
      // Skip directories we can't read
    }

    return files;
  }

  generateReport() {
    console.log("\n📊 Security Check Report");
    console.log("========================\n");

    if (this.issues.length > 0) {
      console.log("🚨 CRITICAL/HIGH ISSUES:");
      this.issues.forEach((issue, index) => {
        console.log(
          `${index + 1}. [${issue.severity.toUpperCase()}] ${issue.category}: ${
            issue.message
          }`
        );
        if (issue.file) console.log(`   File: ${issue.file}`);
      });
      console.log("");
    }

    if (this.warnings.length > 0) {
      console.log("⚠️  WARNINGS:");
      this.warnings.forEach((warning, index) => {
        console.log(
          `${index + 1}. [${warning.severity.toUpperCase()}] ${
            warning.category
          }: ${warning.message}`
        );
        if (warning.file) console.log(`   File: ${warning.file}`);
      });
      console.log("");
    }

    if (this.info.length > 0) {
      console.log("ℹ️  INFORMATION:");
      this.info.forEach((info, index) => {
        console.log(
          `${index + 1}. [${info.severity.toUpperCase()}] ${info.category}: ${
            info.message
          }`
        );
      });
      console.log("");
    }

    // Summary
    const totalIssues = this.issues.length + this.warnings.length;
    console.log("📈 SUMMARY:");
    console.log(`Critical/High Issues: ${this.issues.length}`);
    console.log(`Warnings: ${this.warnings.length}`);
    console.log(`Info: ${this.info.length}`);
    console.log(`Total Issues: ${totalIssues}`);

    if (totalIssues === 0) {
      console.log("✅ No security issues found!");
    } else if (this.issues.length > 0) {
      console.log(
        "❌ Critical security issues found - please address immediately!"
      );
      process.exit(1);
    } else {
      console.log("⚠️  Some warnings found - consider addressing them.");
    }
  }

  run() {
    console.log("🛡️  Starting Security Check...\n");

    this.checkEnvironmentSecurity();
    this.checkDependencySecurity();
    this.checkCodeSecurity();
    this.checkDatabaseSecurity();
    this.checkFilePermissions();

    this.generateReport();
  }
}

// Run the security check
const checker = new SecurityChecker();
checker.run();
