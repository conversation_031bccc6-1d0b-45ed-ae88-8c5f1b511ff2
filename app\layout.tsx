import type { Metadata } from "next";
import "./globals.css";
import { ReactNode } from "react";
import Navbar from "./components/Navbar";
import { SessionProvider } from "next-auth/react";
import Footer from "./components/Footer";
import { ToastContainer } from "react-toast";

export const metadata: Metadata = {
  title: {
    default: "Kuriftu Resorts & Spa - Luxury Lakeside Resort in Ethiopia",
    template: "%s | Kuriftu Resorts & Spa",
  },
  description:
    "Experience luxury and tranquility at Kuriftu Resorts & Spa, Ethiopia's premier lakeside resort destination on Lake Tana. Discover world-class accommodations, spa treatments, dining, and unforgettable experiences.",
  keywords: [
    "Kuriftu Resorts",
    "Ethiopia resort",
    "Lake Tana",
    "luxury hotel",
    "spa resort",
    "Bahir Dar",
    "Ethiopian hospitality",
    "lakeside resort",
    "wellness retreat",
    "adventure tourism",
  ],
  authors: [{ name: "Kuriftu Resorts & Spa" }],
  creator: "Kuriftu Resorts & Spa",
  publisher: "Kuriftu Resorts & Spa",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://www.kuriftu.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Kuriftu Resorts & Spa - Luxury Lakeside Resort in Ethiopia",
    description:
      "Experience luxury and tranquility at Ethiopia's premier lakeside resort destination on Lake Tana.",
    url: "https://www.kuriftu.com",
    siteName: "Kuriftu Resorts & Spa",
    images: [
      {
        url: "/images/kuriftu-hero.jpg",
        width: 1200,
        height: 630,
        alt: "Kuriftu Resorts & Spa - Luxury lakeside resort in Ethiopia",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Kuriftu Resorts & Spa - Luxury Lakeside Resort in Ethiopia",
    description:
      "Experience luxury and tranquility at Ethiopia's premier lakeside resort destination on Lake Tana.",
    images: ["/images/kuriftu-hero.jpg"],
    creator: "@kuriftu",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="antialiased">
        <SessionProvider>
          <div className="flex flex-col min-h-screen">
            <Navbar />
            <main className="flex-grow">{children}</main>
            <Footer />
          </div>
          <ToastContainer position="top-right" delay={4000} />
        </SessionProvider>
      </body>
    </html>
  );
}
