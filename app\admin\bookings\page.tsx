import { prisma } from "@/lib/prisma";
import { Suspense } from "react";
import Link from "next/link";
import {
  ListItemSkeleton,
  StatCardSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface Booking {
  id: string;
  userEmail: string;
  checkIn: Date;
  checkOut?: Date;
  notes?: string;
  status: string;
  createdAt: Date;
  resort?: {
    id: string;
    name: string;
    location: string;
  };
  spaTreatment?: {
    id: string;
    name: string;
    price: number;
  };
}

interface BookingStats {
  total: number;
  pending: number;
  confirmed: number;
  cancelled: number;
  todayBookings: number;
}

// Booking card component
function BookingCard({ booking }: { booking: Booking }) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="bg-white border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="font-semibold text-gray-900">{booking.userEmail}</h3>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                booking.status
              )}`}
            >
              {booking.status}
            </span>
          </div>

          <div className="space-y-1 text-sm text-gray-600">
            {booking.resort && (
              <p>
                <span className="font-medium">Resort:</span>{" "}
                {booking.resort.name}
                <span className="text-gray-400 ml-2">
                  ({booking.resort.location})
                </span>
              </p>
            )}
            {booking.spaTreatment && (
              <p>
                <span className="font-medium">Spa Treatment:</span>{" "}
                {booking.spaTreatment.name}
                <span className="text-gray-400 ml-2">
                  ({booking.spaTreatment.price} ETB)
                </span>
              </p>
            )}

            <p>
              <span className="font-medium">Check-in:</span>{" "}
              {new Date(booking.checkIn).toLocaleDateString()}
            </p>

            {booking.checkOut && (
              <p>
                <span className="font-medium">Check-out:</span>{" "}
                {new Date(booking.checkOut).toLocaleDateString()}
              </p>
            )}

            {booking.notes && (
              <p>
                <span className="font-medium">Notes:</span> {booking.notes}
              </p>
            )}

            <p className="text-xs text-gray-500">
              Booked: {new Date(booking.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <Link
            href={`/admin/bookings/${booking.id}`}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            aria-label={`View details for booking ${booking.id}`}
          >
            View Details
          </Link>
        </div>
      </div>
    </div>
  );
}

// Stats component
function BookingStats({ stats }: { stats: BookingStats }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
        <div className="text-gray-600">Total Bookings</div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-2xl font-bold text-yellow-600">
          {stats.pending}
        </div>
        <div className="text-gray-600">Pending</div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-2xl font-bold text-green-600">
          {stats.confirmed}
        </div>
        <div className="text-gray-600">Confirmed</div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-2xl font-bold text-red-600">{stats.cancelled}</div>
        <div className="text-gray-600">Cancelled</div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="text-2xl font-bold text-blue-600">
          {stats.todayBookings}
        </div>
        <div className="text-gray-600">Today's Bookings</div>
      </div>
    </div>
  );
}

// Main bookings grid component
async function BookingsGrid() {
  try {
    const [bookings, stats] = await Promise.all([
      prisma.booking.findMany({
        include: {
          resort: {
            select: { id: true, name: true, location: true },
          },
          spaTreatment: {
            select: { id: true, name: true, price: true },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 50, // Limit for performance
      }),

      // Calculate stats
      Promise.all([
        prisma.booking.count(),
        prisma.booking.count({ where: { status: "pending" } }),
        prisma.booking.count({ where: { status: "confirmed" } }),
        prisma.booking.count({ where: { status: "cancelled" } }),
        prisma.booking.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
            },
          },
        }),
      ]).then(([total, pending, confirmed, cancelled, todayBookings]) => ({
        total,
        pending,
        confirmed,
        cancelled,
        todayBookings,
      })),
    ]);

    if (bookings.length === 0) {
      return (
        <>
          <BookingStats stats={stats} />
          <EmptyState
            title="No Bookings Found"
            message="No bookings have been made yet. Bookings will appear here once customers start making reservations."
            icon={<div className="text-gray-400 text-6xl mb-4">📅</div>}
          />
        </>
      );
    }

    return (
      <>
        <BookingStats stats={stats} />
        <div className="space-y-4">
          {bookings.map((booking) => (
            <BookingCard key={booking.id} booking={booking} />
          ))}
        </div>
      </>
    );
  } catch (error) {
    console.error("Error fetching bookings:", error);
    return (
      <LoadingError
        resource="booking data"
        onRetry={() => window.location.reload()}
      />
    );
  }
}

export default async function AdminBookings() {
  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Booking Management
            </h1>
            <p className="text-gray-600 mt-1">
              View and manage all customer bookings
            </p>
          </div>

          <div className="flex gap-3">
            <Link
              href="/admin/bookings/export"
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              aria-label="Export booking data"
            >
              Export Data
            </Link>
            <Link
              href="/admin/dashboard"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              aria-label="Go to admin dashboard"
            >
              Dashboard
            </Link>
          </div>
        </div>

        <Suspense
          fallback={
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                {Array.from({ length: 5 }).map((_, i) => (
                  <StatCardSkeleton key={i} />
                ))}
              </div>
              <div className="space-y-4">
                {Array.from({ length: 8 }).map((_, i) => (
                  <ListItemSkeleton key={i} />
                ))}
              </div>
            </div>
          }
        >
          <BookingsGrid />
        </Suspense>
      </div>
    </ErrorBoundary>
  );
}
