"use client";

import React, { useState, useCallback, useMemo } from "react";
import { ChartProps, ChartDataPoint, TimeSeriesDataPoint } from "./types";
import { useErrorHandler } from "./ErrorBoundary";

const defaultColors = [
  "#3B82F6", // blue
  "#10B981", // green
  "#F59E0B", // yellow
  "#EF4444", // red
  "#8B5CF6", // purple
  "#06B6D4", // cyan
  "#F97316", // orange
  "#84CC16", // lime
];

export default function InteractiveChart({
  data,
  type,
  title,
  height = 300,
  width = 400,
  className = "",
  showValues = true,
  showGrid = true,
  showLegend = true,
  colors = defaultColors,
  interactive = true,
  onDataPointClick,
  loading = false,
  error,
  ariaLabel,
  description,
}: ChartProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const { handleError } = useErrorHandler();

  // Memoize chart calculations for performance
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    try {
      return data.map((item, index) => ({
        ...item,
        color: (item as ChartDataPoint).color || colors[index % colors.length],
        index,
      }));
    } catch (err) {
      handleError(err as Error);
      return [];
    }
  }, [data, colors, handleError]);

  const maxValue = useMemo(() => {
    return Math.max(...chartData.map((d) => d.value));
  }, [chartData]);

  const minValue = useMemo(() => {
    return Math.min(...chartData.map((d) => d.value));
  }, [chartData]);

  const handleDataPointClick = useCallback(
    (dataPoint: ChartDataPoint | TimeSeriesDataPoint, index: number) => {
      if (!interactive) return;

      setSelectedIndex(index);
      if (onDataPointClick) {
        onDataPointClick(dataPoint);
      }
    },
    [interactive, onDataPointClick]
  );

  const handleKeyDown = useCallback(
    (
      event: React.KeyboardEvent,
      dataPoint: ChartDataPoint | TimeSeriesDataPoint,
      index: number
    ) => {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        handleDataPointClick(dataPoint, index);
      }
    },
    [handleDataPointClick]
  );

  // Loading state
  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        )}
        <div
          className="flex items-center justify-center h-64"
          role="status"
          aria-label="Loading chart"
        >
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="sr-only">Loading chart data...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        )}
        <div
          className="flex items-center justify-center h-64 text-red-500"
          role="alert"
        >
          <div className="text-center">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <p className="text-sm">Failed to load chart data</p>
            <p className="text-xs text-gray-500 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // Empty state
  if (!chartData || chartData.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        )}
        <div className="flex items-center justify-center h-64 text-gray-500">
          <div className="text-center">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            <p className="text-sm">No data available</p>
          </div>
        </div>
      </div>
    );
  }

  const renderBarChart = () => {
    const barWidth = (width - 80) / chartData.length;
    const chartHeight = height - 80;

    return (
      <svg
        width={width}
        height={height}
        className="overflow-visible"
        role="img"
        aria-label={ariaLabel || `Bar chart showing ${title || "data"}`}
        aria-describedby={description ? "chart-description" : undefined}
      >
        {description && <desc id="chart-description">{description}</desc>}

        {/* Grid lines */}
        {showGrid && (
          <g className="opacity-20">
            {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => (
              <line
                key={index}
                x1={60}
                y1={60 + chartHeight * ratio}
                x2={width - 20}
                y2={60 + chartHeight * ratio}
                stroke="#6B7280"
                strokeWidth={1}
              />
            ))}
          </g>
        )}

        {/* Y-axis */}
        <line
          x1={60}
          y1={60}
          x2={60}
          y2={60 + chartHeight}
          stroke="#6B7280"
          strokeWidth={2}
        />

        {/* X-axis */}
        <line
          x1={60}
          y1={60 + chartHeight}
          x2={width - 20}
          y2={60 + chartHeight}
          stroke="#6B7280"
          strokeWidth={2}
        />

        {/* Y-axis labels */}
        {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => (
          <text
            key={index}
            x={50}
            y={60 + chartHeight * (1 - ratio) + 5}
            textAnchor="end"
            className="text-xs fill-gray-600"
          >
            {Math.round(minValue + (maxValue - minValue) * ratio)}
          </text>
        ))}

        {/* Bars */}
        {chartData.map((item, index) => {
          const barHeight = (item.value / maxValue) * chartHeight;
          const x = 60 + index * barWidth + barWidth * 0.1;
          const y = 60 + chartHeight - barHeight;
          const isHovered = hoveredIndex === index;
          const isSelected = selectedIndex === index;

          return (
            <g key={index}>
              <rect
                x={x}
                y={y}
                width={barWidth * 0.8}
                height={barHeight}
                fill={item.color}
                className={`transition-all duration-300 ${
                  interactive ? "cursor-pointer hover:opacity-80" : ""
                } ${isHovered ? "opacity-90 stroke-2 stroke-gray-800" : ""} ${
                  isSelected ? "opacity-100 stroke-2 stroke-blue-600" : ""
                }`}
                onMouseEnter={() => interactive && setHoveredIndex(index)}
                onMouseLeave={() => interactive && setHoveredIndex(null)}
                onClick={() => handleDataPointClick(item, index)}
                onKeyDown={(e) => handleKeyDown(e, item, index)}
                tabIndex={interactive ? 0 : -1}
                aria-label={`${item.label}: ${item.value}`}
                {...(interactive ? { role: "button" } : {})}
              />

              {/* Value labels */}
              {showValues && (
                <text
                  x={x + (barWidth * 0.8) / 2}
                  y={y - 5}
                  textAnchor="middle"
                  className="text-xs fill-gray-700 font-medium pointer-events-none"
                >
                  {item.value}
                </text>
              )}

              {/* X-axis labels */}
              <text
                x={x + (barWidth * 0.8) / 2}
                y={height - 20}
                textAnchor="middle"
                className="text-xs fill-gray-600 pointer-events-none"
              >
                {item.label}
              </text>
            </g>
          );
        })}

        {/* Tooltip for hovered item */}
        {interactive && hoveredIndex !== null && (
          <g>
            <rect
              x={60 + hoveredIndex * barWidth}
              y={10}
              width={120}
              height={40}
              fill="rgba(0, 0, 0, 0.8)"
              rx={4}
              className="pointer-events-none"
            />
            <text
              x={60 + hoveredIndex * barWidth + 60}
              y={25}
              textAnchor="middle"
              className="text-xs fill-white font-medium pointer-events-none"
            >
              {chartData[hoveredIndex].label}
            </text>
            <text
              x={60 + hoveredIndex * barWidth + 60}
              y={40}
              textAnchor="middle"
              className="text-xs fill-white pointer-events-none"
            >
              Value: {chartData[hoveredIndex].value}
            </text>
          </g>
        )}
      </svg>
    );
  };

  const renderChart = () => {
    switch (type) {
      case "bar":
        return renderBarChart();
      case "line":
      case "area":
      case "pie":
      case "donut":
      case "scatter":
        // For now, fallback to bar chart - these can be implemented later
        return renderBarChart();
      default:
        return renderBarChart();
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      {title && (
        <h3
          className="text-lg font-semibold text-gray-900 mb-4"
          id="chart-title"
        >
          {title}
        </h3>
      )}

      <div className="flex justify-center">{renderChart()}</div>

      {/* Legend */}
      {showLegend && type === "pie" && (
        <div
          className="mt-4 flex flex-wrap justify-center gap-4"
          role="list"
          aria-label="Chart legend"
        >
          {chartData.map((item, index) => (
            <div key={index} className="flex items-center" role="listitem">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: item.color }}
                aria-hidden="true"
              />
              <span className="text-sm text-gray-600">{item.label}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
