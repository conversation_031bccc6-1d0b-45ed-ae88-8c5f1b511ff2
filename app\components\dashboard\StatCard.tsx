"use client";

import React, { memo, useCallback, useMemo } from "react";
import { StatCardProps } from "./types";
import { StatCardSkeleton } from "../ui/SkeletonComponents";
import { ErrorDisplay } from "../ui/ErrorComponents";

// Enhanced color classes with better accessibility
const colorClasses = {
  blue: {
    bg: "bg-blue-50",
    border: "border-blue-200",
    icon: "bg-blue-100 text-blue-600",
    text: "text-blue-600",
  },
  green: {
    bg: "bg-green-50",
    border: "border-green-200",
    icon: "bg-green-100 text-green-600",
    text: "text-green-600",
  },
  yellow: {
    bg: "bg-yellow-50",
    border: "border-yellow-200",
    icon: "bg-yellow-100 text-yellow-600",
    text: "text-yellow-600",
  },
  red: {
    bg: "bg-red-50",
    border: "border-red-200",
    icon: "bg-red-100 text-red-600",
    text: "text-red-600",
  },
  purple: {
    bg: "bg-purple-50",
    border: "border-purple-200",
    icon: "bg-purple-100 text-purple-600",
    text: "text-purple-600",
  },
  indigo: {
    bg: "bg-indigo-50",
    border: "border-indigo-200",
    icon: "bg-indigo-100 text-indigo-600",
    text: "text-indigo-600",
  },
  gray: {
    bg: "bg-gray-50",
    border: "border-gray-200",
    icon: "bg-gray-100 text-gray-600",
    text: "text-gray-600",
  },
};

// Helper functions for change indicators
const getChangeIcon = (type: "increase" | "decrease" | "neutral") => {
  switch (type) {
    case "increase":
      return (
        <svg
          className="w-4 h-4"
          fill="currentColor"
          viewBox="0 0 20 20"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z"
            clipRule="evenodd"
          />
        </svg>
      );
    case "decrease":
      return (
        <svg
          className="w-4 h-4"
          fill="currentColor"
          viewBox="0 0 20 20"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z"
            clipRule="evenodd"
          />
        </svg>
      );
    case "neutral":
      return (
        <svg
          className="w-4 h-4"
          fill="currentColor"
          viewBox="0 0 20 20"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
            clipRule="evenodd"
          />
        </svg>
      );
  }
};

const getChangeColor = (type: "increase" | "decrease" | "neutral") => {
  switch (type) {
    case "increase":
      return "text-green-600";
    case "decrease":
      return "text-red-600";
    case "neutral":
      return "text-gray-600";
  }
};

const StatCard = memo(function StatCard({
  title,
  value,
  change,
  icon,
  color = "blue",
  loading = false,
  error,
  onClick,
  className = "",
  ariaLabel,
  description,
}: StatCardProps) {
  const colors = useMemo(() => colorClasses[color], [color]);

  const formatValue = useCallback((val: string | number): string => {
    if (typeof val === "number") {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}K`;
      }
      return val.toLocaleString();
    }
    return val;
  }, []);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (onClick && (event.key === "Enter" || event.key === " ")) {
        event.preventDefault();
        onClick();
      }
    },
    [onClick]
  );

  const formattedValue = useMemo(
    () => formatValue(value),
    [formatValue, value]
  );

  const ariaLabelText = useMemo(() => {
    if (ariaLabel) return ariaLabel;

    const baseLabel = `${title}: ${formattedValue}`;
    if (change) {
      return `${baseLabel}, ${change.type} ${Math.abs(change.value)}% vs ${
        change.period
      }`;
    }
    return baseLabel;
  }, [ariaLabel, title, formattedValue, change]);

  const descriptionId = useMemo(
    () =>
      description
        ? `${title.replace(/\s+/g, "-").toLowerCase()}-description`
        : undefined,
    [description, title]
  );

  // Error state
  if (error) {
    return (
      <div className={className} role="alert" aria-live="assertive">
        <ErrorDisplay
          title={`Error loading ${title}`}
          message={error}
          variant="error"
          showRetry={false}
          className="bg-white rounded-lg shadow-sm border border-red-200 p-6"
        />
      </div>
    );
  }

  // Loading state
  if (loading) {
    return (
      <StatCardSkeleton
        className={className}
        showIcon={!!icon}
        showChange={!!change}
      />
    );
  }

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border ${
        colors.border
      } p-6 transition-all duration-200 hover:shadow-md ${
        onClick
          ? "cursor-pointer hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          : ""
      } ${className}`}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      role={onClick ? "button" : "region"}
      tabIndex={onClick ? 0 : undefined}
      aria-label={ariaLabelText}
      aria-describedby={descriptionId}
      aria-live={change ? "polite" : undefined}
    >
      {description && (
        <div id={descriptionId} className="sr-only">
          {description}
        </div>
      )}

      <div className="flex items-center">
        {icon && (
          <div className="flex-shrink-0" aria-hidden="true">
            <div
              className={`w-12 h-12 ${colors.icon} rounded-lg flex items-center justify-center`}
            >
              {icon}
            </div>
          </div>
        )}
        <div className={`${icon ? "ml-4" : ""} flex-1`}>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900" aria-live="polite">
            {formattedValue}
          </p>
          {change && (
            <div className="flex items-center mt-2" aria-live="polite">
              <span
                className={`flex items-center text-sm font-medium ${getChangeColor(
                  change.type
                )}`}
                aria-label={`${change.type} ${Math.abs(
                  change.value
                )}% compared to ${change.period}`}
              >
                {getChangeIcon(change.type)}
                <span className="ml-1">
                  {change.isPercentage
                    ? ""
                    : change.type === "increase"
                    ? "+"
                    : change.type === "decrease"
                    ? "-"
                    : ""}
                  {Math.abs(change.value)}
                  {change.isPercentage ? "%" : ""}
                </span>
              </span>
              <span className="text-sm text-gray-500 ml-2">
                vs {change.period}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

// Export the component as default
export default StatCard;

// Predefined icon components for common metrics
export const StatIcons = {
  Users: () => (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
      />
    </svg>
  ),
  Bookings: () => (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
      />
    </svg>
  ),
  Revenue: () => (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
      />
    </svg>
  ),
  Resorts: () => (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      />
    </svg>
  ),
  Spa: () => (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
      />
    </svg>
  ),
  Activity: () => (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M13 10V3L4 14h7v7l9-11h-7z"
      />
    </svg>
  ),
  Occupancy: () => (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
      />
    </svg>
  ),
  Performance: () => (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
      />
    </svg>
  ),
};
