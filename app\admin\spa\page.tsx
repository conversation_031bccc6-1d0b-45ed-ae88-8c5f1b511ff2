import Link from "next/link";
import { prisma } from "@/lib/prisma";
import { deleteTreatment } from "@/lib/actions";
import Image from "next/image";
import { Suspense } from "react";
import type { SpaTreatment } from "@/app/types/spa";
import {
  AdminCardSkeleton,
  GridSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { EmptyState, LoadingError } from "@/app/components/ui/ErrorComponents";

// TypeScript interfaces
interface TreatmentCardProps {
  treatment: SpaTreatment;
}

// Treatment card component
function TreatmentCard({ treatment }: TreatmentCardProps) {
  return (
    <div className="border rounded-xl p-4 bg-white shadow-sm hover:shadow-md transition-shadow">
      <div className="relative mb-3">
        <Image
          src={treatment.image}
          alt={treatment.name}
          width={400}
          height={160}
          className="w-full h-40 object-cover rounded"
          loading="lazy"
        />
        <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
          {treatment.duration} min
        </div>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-gray-900">
          {treatment.name}
        </h3>
        <p className="text-sm text-gray-600 line-clamp-2">
          {treatment.description}
        </p>
        <p className="font-bold text-blue-600">{treatment.price} ETB</p>
      </div>

      <div className="mt-4 space-y-2">
        <div className="flex justify-between items-center">
          <Link
            href={`/admin/spa/${treatment.id}/edit`}
            className="text-blue-600 hover:text-blue-800 font-medium text-sm"
            aria-label={`Edit ${treatment.name} treatment`}
          >
            Edit Treatment
          </Link>
          <div className="flex gap-2">
            <Link
              href={`/spa/treatments/${treatment.id}`}
              target="_blank"
              className="text-green-600 hover:text-green-800 font-medium text-sm"
              aria-label={`View ${treatment.name} treatment details`}
            >
              View
            </Link>
            <form action={deleteTreatment} className="inline">
              <input type="hidden" name="id" value={treatment.id} />
              <button
                type="submit"
                className="text-red-600 hover:text-red-800 font-medium text-sm"
                onClick={(e) => {
                  if (
                    !confirm(
                      `Are you sure you want to delete "${treatment.name}"? This action cannot be undone.`
                    )
                  ) {
                    e.preventDefault();
                  }
                }}
                aria-label={`Delete ${treatment.name} treatment`}
              >
                Delete
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

// Treatments grid component
async function TreatmentsGrid() {
  try {
    const treatments = await prisma.spaTreatment.findMany({
      orderBy: { createdAt: "desc" },
    });

    if (treatments.length === 0) {
      return (
        <EmptyState
          title="No Spa Treatments Created Yet"
          message="Start by creating your first spa treatment offering."
          actionLabel="+ Create First Treatment"
          actionHref="/admin/spa/create"
          icon={<div className="text-gray-400 text-6xl mb-4">🧘‍♀️</div>}
        />
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {treatments.map((treatment) => (
          <TreatmentCard key={treatment.id} treatment={treatment} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching spa treatments:", error);
    return (
      <LoadingError
        resource="spa treatments"
        onRetry={() => window.location.reload()}
      />
    );
  }
}

export default async function AdminSpa() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Spa Treatment Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your spa treatments and wellness services
          </p>
        </div>
        <Link
          href="/admin/spa/create"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
          aria-label="Create new spa treatment"
        >
          <span className="mr-2">+</span>
          Add Treatment
        </Link>
      </div>

      <Suspense
        fallback={
          <GridSkeleton
            count={6}
            columns={3}
            SkeletonComponent={AdminCardSkeleton}
          />
        }
      >
        <TreatmentsGrid />
      </Suspense>
    </div>
  );
}
