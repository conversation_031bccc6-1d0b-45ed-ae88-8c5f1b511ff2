"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";

export interface Notification {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
}

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
}

export function useNotifications() {
  const { data: session } = useSession();
  const [state, setState] = useState<NotificationState>({
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
  });

  // Fetch notifications from API
  const fetchNotifications = useCallback(async () => {
    if (!session?.user?.email) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch("/api/notifications", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch notifications: ${response.status}`);
      }

      const notifications: Notification[] = await response.json();
      const unreadCount = notifications.filter(n => !n.read).length;

      setState(prev => ({
        ...prev,
        notifications,
        unreadCount,
        loading: false,
      }));
    } catch (error) {
      console.error("Error fetching notifications:", error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to load notifications",
        loading: false,
      }));
    }
  }, [session?.user?.email]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to mark notification as read");
      }

      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n =>
          n.id === notificationId ? { ...n, read: true } : n
        ),
        unreadCount: Math.max(0, prev.unreadCount - 1),
      }));
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch("/api/notifications/read-all", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to mark all notifications as read");
      }

      setState(prev => ({
        ...prev,
        notifications: prev.notifications.map(n => ({ ...n, read: true })),
        unreadCount: 0,
      }));
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  }, []);

  // Add new notification (for real-time updates)
  const addNotification = useCallback((notification: Omit<Notification, "id" | "timestamp">) => {
    const newNotification: Notification = {
      ...notification,
      id: crypto.randomUUID(),
      timestamp: new Date(),
    };

    setState(prev => ({
      ...prev,
      notifications: [newNotification, ...prev.notifications],
      unreadCount: notification.read ? prev.unreadCount : prev.unreadCount + 1,
    }));
  }, []);

  // Show toast notification
  const showToast = useCallback((
    type: Notification["type"],
    title: string,
    message: string,
    options?: { actionUrl?: string; actionLabel?: string }
  ) => {
    addNotification({
      type,
      title,
      message,
      read: false,
      ...options,
    });
  }, [addNotification]);

  // Initial fetch
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Set up real-time updates (polling every 30 seconds)
  useEffect(() => {
    if (!session?.user?.email) return;

    const interval = setInterval(fetchNotifications, 30000);
    return () => clearInterval(interval);
  }, [session?.user?.email, fetchNotifications]);

  return {
    notifications: state.notifications,
    unreadCount: state.unreadCount,
    loading: state.loading,
    error: state.error,
    markAsRead,
    markAllAsRead,
    addNotification,
    showToast,
    refetch: fetchNotifications,
  };
}

// Hook for showing simple toast notifications
export function useToast() {
  const { showToast } = useNotifications();

  const toast = {
    success: (title: string, message: string) => showToast("success", title, message),
    error: (title: string, message: string) => showToast("error", title, message),
    warning: (title: string, message: string) => showToast("warning", title, message),
    info: (title: string, message: string) => showToast("info", title, message),
  };

  return toast;
}
