"use client";

import { useState, useCallback } from 'react';
import { validateImageFile, validateImageDimensions } from '@/lib/security';

export interface UploadedImage {
  public_id: string;
  secure_url: string;
  url: string;
  width: number;
  height: number;
  format: string;
  bytes: number;
  created_at: string;
}

export interface UploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  uploadedImage: UploadedImage | null;
}

export interface UseImageUploadOptions {
  uploadType?: string;
  maxWidth?: number;
  maxHeight?: number;
  onUploadComplete?: (image: UploadedImage) => void;
  onUploadError?: (error: string) => void;
}

export function useImageUpload(options: UseImageUploadOptions = {}) {
  const {
    uploadType = 'general',
    maxWidth = 4096,
    maxHeight = 4096,
    onUploadComplete,
    onUploadError,
  } = options;

  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    uploadedImage: null,
  });

  const validateFile = useCallback((file: File): Promise<{ isValid: boolean; error?: string }> => {
    return new Promise((resolve) => {
      // Basic file validation
      const basicValidation = validateImageFile(file);
      if (!basicValidation.isValid) {
        resolve(basicValidation);
        return;
      }

      // Check image dimensions
      const img = new Image();
      img.onload = () => {
        const dimensionValidation = validateImageDimensions(
          img.width,
          img.height,
          maxWidth,
          maxHeight
        );
        resolve(dimensionValidation);
      };
      img.onerror = () => {
        resolve({
          isValid: false,
          error: 'Invalid image file',
        });
      };
      img.src = URL.createObjectURL(file);
    });
  }, [maxWidth, maxHeight]);

  const uploadImage = useCallback(async (file: File): Promise<UploadedImage | null> => {
    try {
      setUploadState(prev => ({
        ...prev,
        isUploading: true,
        progress: 0,
        error: null,
      }));

      // Validate file
      const validation = await validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', uploadType);

      // Upload with progress tracking
      const xhr = new XMLHttpRequest();
      
      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadState(prev => ({
              ...prev,
              progress,
            }));
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              const uploadedImage: UploadedImage = response;
              
              setUploadState(prev => ({
                ...prev,
                isUploading: false,
                progress: 100,
                uploadedImage,
              }));

              onUploadComplete?.(uploadedImage);
              resolve(uploadedImage);
            } catch (error) {
              const errorMessage = 'Failed to parse upload response';
              setUploadState(prev => ({
                ...prev,
                isUploading: false,
                error: errorMessage,
              }));
              onUploadError?.(errorMessage);
              reject(new Error(errorMessage));
            }
          } else {
            try {
              const errorResponse = JSON.parse(xhr.responseText);
              const errorMessage = errorResponse.error || 'Upload failed';
              setUploadState(prev => ({
                ...prev,
                isUploading: false,
                error: errorMessage,
              }));
              onUploadError?.(errorMessage);
              reject(new Error(errorMessage));
            } catch {
              const errorMessage = `Upload failed with status ${xhr.status}`;
              setUploadState(prev => ({
                ...prev,
                isUploading: false,
                error: errorMessage,
              }));
              onUploadError?.(errorMessage);
              reject(new Error(errorMessage));
            }
          }
        });

        xhr.addEventListener('error', () => {
          const errorMessage = 'Network error during upload';
          setUploadState(prev => ({
            ...prev,
            isUploading: false,
            error: errorMessage,
          }));
          onUploadError?.(errorMessage);
          reject(new Error(errorMessage));
        });

        xhr.open('POST', '/api/upload');
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest'); // CSRF protection
        xhr.send(formData);
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
      }));
      onUploadError?.(errorMessage);
      return null;
    }
  }, [uploadType, validateFile, onUploadComplete, onUploadError]);

  const resetUpload = useCallback(() => {
    setUploadState({
      isUploading: false,
      progress: 0,
      error: null,
      uploadedImage: null,
    });
  }, []);

  const clearError = useCallback(() => {
    setUploadState(prev => ({
      ...prev,
      error: null,
    }));
  }, []);

  return {
    uploadState,
    uploadImage,
    resetUpload,
    clearError,
    validateFile,
  };
}
