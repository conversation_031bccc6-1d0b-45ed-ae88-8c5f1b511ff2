import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { logAuditEvent } from "@/lib/security";
import { requireRole } from "@/lib/checkRole";

export async function GET(req: Request) {
  try {
    // Check authentication and role
    const roleCheck = await requireRole(["admin", "manager", "receptionist"]);
    if (roleCheck instanceof NextResponse) {
      return roleCheck;
    }

    const url = new URL(req.url);
    const date = url.searchParams.get("date");

    if (!date) {
      return NextResponse.json(
        { error: "Date parameter is required" },
        { status: 400 }
      );
    }

    // Parse date and create date range for the day
    const selectedDate = new Date(date);
    const startOfDay = new Date(selectedDate);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(selectedDate);
    endOfDay.setHours(23, 59, 59, 999);

    // Fetch bookings for the selected date
    const bookings = await prisma.booking.findMany({
      where: {
        OR: [
          {
            checkIn: {
              gte: startOfDay,
              lte: endOfDay,
            },
          },
          {
            checkOut: {
              gte: startOfDay,
              lte: endOfDay,
            },
          },
          {
            AND: [
              { checkIn: { lte: startOfDay } },
              { 
                OR: [
                  { checkOut: { gte: endOfDay } },
                  { checkOut: null }
                ]
              }
            ]
          }
        ],
        status: {
          not: "CANCELLED"
        }
      },
      include: {
        resort: {
          select: {
            name: true,
            location: true,
          },
        },
        spaTreatment: {
          select: {
            name: true,
            duration: true,
          },
        },
      },
      orderBy: {
        checkIn: "asc",
      },
    });

    // Transform bookings to guest format
    const guests = bookings.map(booking => ({
      id: booking.id,
      userEmail: booking.userEmail,
      userName: null, // Would need to join with User table if needed
      userPhone: null, // Would need to join with User table if needed
      checkIn: booking.checkIn.toISOString(),
      checkOut: booking.checkOut?.toISOString(),
      status: booking.status,
      resort: booking.resort,
      spaTreatment: booking.spaTreatment,
      notes: booking.notes,
      roomNumber: null, // Would need room assignment system
      specialRequests: null, // Could be added to booking model
    }));

    await logAuditEvent({
      userId: roleCheck.user.id,
      action: "GUESTS_FETCHED",
      resource: "/api/reception/guests",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json(guests);
  } catch (error) {
    console.error("Error fetching guests:", error);
    await logAuditEvent({
      action: "GUESTS_FETCH_ERROR",
      resource: "/api/reception/guests",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Failed to fetch guests" },
      { status: 500 }
    );
  }
}
