# MongoDB Atlas Connectivity Test Script

Write-Host "🔍 MongoDB Atlas Connectivity Diagnostic" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Test 1: DNS Resolution with different DNS servers
Write-Host "`n1. Testing DNS Resolution..." -ForegroundColor Yellow

$hostname = "cluster0.qohp7.mongodb.net"
$dnsServers = @(
    @{Name="System Default"; Server=""},
    @{Name="Google DNS"; Server="*******"},
    @{Name="Cloudflare DNS"; Server="*******"},
    @{Name="OpenDNS"; Server="**************"}
)

foreach ($dns in $dnsServers) {
    try {
        Write-Host "   Testing with $($dns.Name)..." -NoNewline
        if ($dns.Server -eq "") {
            $result = Resolve-DnsName -Name $hostname -ErrorAction Stop
        } else {
            $result = Resolve-DnsName -Name $hostname -Server $dns.Server -ErrorAction Stop
        }
        Write-Host " ✅ Success" -ForegroundColor Green
        Write-Host "      IP: $($result.IPAddress -join ', ')" -ForegroundColor Gray
    } catch {
        Write-Host " ❌ Failed" -ForegroundColor Red
        Write-Host "      Error: $($_.Exception.Message)" -ForegroundColor Gray
    }
}

# Test 2: Port Connectivity
Write-Host "`n2. Testing Port Connectivity..." -ForegroundColor Yellow
$ports = @(27017, 443)

foreach ($port in $ports) {
    Write-Host "   Testing port $port..." -NoNewline
    try {
        $connection = Test-NetConnection -ComputerName $hostname -Port $port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host " ✅ Open" -ForegroundColor Green
        } else {
            Write-Host " ❌ Closed/Filtered" -ForegroundColor Red
        }
    } catch {
        Write-Host " ❌ Failed" -ForegroundColor Red
        Write-Host "      Error: $($_.Exception.Message)" -ForegroundColor Gray
    }
}

# Test 3: Current DNS Configuration
Write-Host "`n3. Current DNS Configuration..." -ForegroundColor Yellow
try {
    $dnsConfig = Get-DnsClientServerAddress -AddressFamily IPv4
    foreach ($adapter in $dnsConfig) {
        if ($adapter.ServerAddresses.Count -gt 0) {
            Write-Host "   $($adapter.InterfaceAlias): $($adapter.ServerAddresses -join ', ')" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "   Could not retrieve DNS configuration" -ForegroundColor Red
}

# Test 4: Recommendations
Write-Host "`n4. Recommendations:" -ForegroundColor Yellow
Write-Host "   📋 If DNS resolution fails with system default:" -ForegroundColor Cyan
Write-Host "      - Change DNS to ******* and ******* (Google)" -ForegroundColor Gray
Write-Host "      - Or use ******* and ******* (Cloudflare)" -ForegroundColor Gray
Write-Host "   📋 If port 27017 is blocked:" -ForegroundColor Cyan
Write-Host "      - Check firewall settings" -ForegroundColor Gray
Write-Host "      - Contact network administrator" -ForegroundColor Gray
Write-Host "   📋 If all tests fail:" -ForegroundColor Cyan
Write-Host "      - Check MongoDB Atlas IP whitelist" -ForegroundColor Gray
Write-Host "      - Verify cluster is running" -ForegroundColor Gray

Write-Host "`n✅ Diagnostic complete!" -ForegroundColor Green
