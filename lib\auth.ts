import { AuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { prisma } from "./prisma";
import { logAuditEvent } from "./security";

// Session security configuration
const sessionConfig = {
  strategy: "jwt" as const,
  maxAge: 24 * 60 * 60, // 24 hours
  updateAge: 60 * 60, // 1 hour
};

// Cookie security configuration
const cookieConfig = {
  sessionToken: {
    name:
      process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.session-token"
        : "next-auth.session-token",
    options: {
      httpOnly: true,
      sameSite: "lax" as const,
      path: "/",
      secure: process.env.NODE_ENV === "production",
    },
  },
  callbackUrl: {
    name:
      process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.callback-url"
        : "next-auth.callback-url",
    options: {
      sameSite: "lax" as const,
      path: "/",
      secure: process.env.NODE_ENV === "production",
    },
  },
  csrfToken: {
    name:
      process.env.NODE_ENV === "production"
        ? "__Host-next-auth.csrf-token"
        : "next-auth.csrf-token",
    options: {
      httpOnly: true,
      sameSite: "lax" as const,
      path: "/",
      secure: process.env.NODE_ENV === "production",
    },
  },
};

export const authOptions: AuthOptions = {
  adapter: PrismaAdapter(prisma),
  session: sessionConfig,
  cookies: cookieConfig,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile",
        },
      },
    }),
  ],
  pages: {
    signIn: "/login",
    error: "/auth/error",
    signOut: "/auth/signout",
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        // Log sign-in attempts
        await logAuditEvent({
          userId: user.id,
          action: "SIGN_IN_ATTEMPT",
          resource: "auth",
          ip: "unknown", // Will be populated by middleware
          userAgent: "unknown", // Will be populated by middleware
          success: true,
        });

        return true;
      } catch (error) {
        console.error("Sign-in error:", error);
        await logAuditEvent({
          userId: user.id,
          action: "SIGN_IN_FAILED",
          resource: "auth",
          ip: "unknown",
          userAgent: "unknown",
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
        return false;
      }
    },
    async session({ session, token }) {
      if (session.user && token) {
        session.user.id = token.sub || "";
        session.user.role = token.role || "user";

        // Add session metadata for security
        (session as any).sessionId = token.sessionId;
        (session as any).issuedAt = token.iat;
      }
      return session;
    },
    async jwt({ token, user, trigger }) {
      // Only fetch user data on sign-in or when explicitly triggered
      if (user || trigger === "update") {
        try {
          const userId = user?.id || token.sub;
          if (userId) {
            const dbUser = await prisma.user.findUnique({
              where: { id: userId },
              select: { role: true }, // Only select the role field for efficiency
            });

            // Validate role and provide fallback
            const validRoles = ["admin", "manager", "receptionist", "user"];
            const userRole = dbUser?.role || "user";
            token.role = validRoles.includes(userRole) ? userRole : "user";

            // Add session security metadata
            token.iat = Math.floor(Date.now() / 1000);
            token.sessionId = crypto.randomUUID();
          }
        } catch (error) {
          console.error("Error fetching user role:", error);
          // Fallback to default role if database query fails
          token.role = "user";
        }
      }

      // Check token expiration and refresh if needed
      const now = Math.floor(Date.now() / 1000);
      if (token.iat && now - (token.iat as number) > sessionConfig.updateAge) {
        // Refresh user data periodically
        try {
          const dbUser = await prisma.user.findUnique({
            where: { id: token.sub! },
            select: { role: true },
          });

          if (dbUser) {
            token.role = dbUser.role;
          }
        } catch (error) {
          console.error("Token refresh error:", error);
        }
      }

      return token;
    },
    async redirect({ url, baseUrl }) {
      // Ensure redirects are safe
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  events: {
    async signIn({ user, isNewUser }) {
      await logAuditEvent({
        userId: user.id,
        action: isNewUser ? "USER_REGISTERED" : "USER_SIGNED_IN",
        resource: "auth",
        ip: "unknown",
        userAgent: "unknown",
        success: true,
      });
    },
    async signOut({ token, session }) {
      await logAuditEvent({
        userId: token?.sub || session?.user?.id,
        action: "USER_SIGNED_OUT",
        resource: "auth",
        ip: "unknown",
        userAgent: "unknown",
        success: true,
      });
    },
    async createUser({ user }) {
      await logAuditEvent({
        userId: user.id,
        action: "USER_CREATED",
        resource: "auth",
        ip: "unknown",
        userAgent: "unknown",
        success: true,
      });
    },
  },
  debug: process.env.NODE_ENV === "development",
};
