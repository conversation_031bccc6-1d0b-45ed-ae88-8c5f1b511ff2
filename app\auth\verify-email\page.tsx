"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { CheckCircle, XCircle, Mail, RefreshCw, AlertCircle } from "lucide-react";
import toast from "react-hot-toast";

export default function VerifyEmailPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<"loading" | "success" | "error" | "resend">("loading");
  const [message, setMessage] = useState("");
  const [email, setEmail] = useState("");
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    const success = searchParams.get("success");
    const error = searchParams.get("error");
    const emailParam = searchParams.get("email");
    const token = searchParams.get("token");

    if (emailParam) {
      setEmail(emailParam);
    }

    if (success === "true") {
      setStatus("success");
      setMessage("Your email has been verified successfully!");
    } else if (error) {
      setStatus("error");
      switch (error) {
        case "missing-params":
          setMessage("Invalid verification link. Please check your email for the correct link.");
          break;
        case "invalid-params":
          setMessage("Invalid verification parameters. Please request a new verification email.");
          break;
        case "verification-failed":
          setMessage("Email verification failed. The link may be expired or invalid.");
          break;
        case "server-error":
          setMessage("A server error occurred. Please try again later.");
          break;
        default:
          setMessage(decodeURIComponent(error));
      }
      setStatus("resend");
    } else if (token && emailParam) {
      // Auto-verify if token and email are present
      verifyEmail(emailParam, token);
    } else {
      setStatus("resend");
      setMessage("Please enter your email to receive a verification link.");
    }
  }, [searchParams]);

  const verifyEmail = async (email: string, token: string) => {
    try {
      const response = await fetch("/api/auth/verify-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, token }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setStatus("success");
        setMessage("Your email has been verified successfully!");
        toast.success("Email verified successfully!");
      } else {
        setStatus("error");
        setMessage(data.error || "Verification failed");
        toast.error(data.error || "Verification failed");
      }
    } catch (error) {
      setStatus("error");
      setMessage("An error occurred during verification");
      toast.error("An error occurred during verification");
    }
  };

  const resendVerification = async () => {
    if (!email) {
      toast.error("Please enter your email address");
      return;
    }

    setIsResending(true);
    try {
      const response = await fetch("/api/auth/send-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast.success("Verification email sent! Please check your inbox.");
        setMessage("Verification email sent! Please check your inbox and spam folder.");
      } else {
        toast.error(data.error || "Failed to send verification email");
        setMessage(data.error || "Failed to send verification email");
      }
    } catch (error) {
      toast.error("An error occurred while sending verification email");
      setMessage("An error occurred while sending verification email");
    } finally {
      setIsResending(false);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case "success":
        return <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />;
      case "error":
        return <XCircle className="w-16 h-16 text-red-500 mx-auto" />;
      case "loading":
        return <RefreshCw className="w-16 h-16 text-blue-500 mx-auto animate-spin" />;
      default:
        return <Mail className="w-16 h-16 text-blue-500 mx-auto" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "success":
        return "text-green-600";
      case "error":
        return "text-red-600";
      case "loading":
        return "text-blue-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center">
          {getStatusIcon()}
          
          <h1 className="text-2xl font-bold text-gray-900 mt-4 mb-2">
            Email Verification
          </h1>
          
          <p className={`text-sm ${getStatusColor()} mb-6`}>
            {message}
          </p>

          {status === "success" && (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                  <span className="text-green-700 text-sm">
                    Your account is now active and ready to use!
                  </span>
                </div>
              </div>
              
              <button
                onClick={() => router.push("/login")}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Continue to Login
              </button>
            </div>
          )}

          {(status === "error" || status === "resend") && (
            <div className="space-y-4">
              {status === "error" && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                    <span className="text-red-700 text-sm">
                      Verification failed. You can request a new verification email below.
                    </span>
                  </div>
                </div>
              )}

              <div className="space-y-3">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                
                <button
                  onClick={resendVerification}
                  disabled={isResending || !email}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                >
                  {isResending ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="w-4 h-4 mr-2" />
                      Send Verification Email
                    </>
                  )}
                </button>
              </div>

              <div className="text-center">
                <button
                  onClick={() => router.push("/login")}
                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                >
                  Back to Login
                </button>
              </div>
            </div>
          )}

          {status === "loading" && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <span className="text-blue-700 text-sm">
                Verifying your email address...
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
