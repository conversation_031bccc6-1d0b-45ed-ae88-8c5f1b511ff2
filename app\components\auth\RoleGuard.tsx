"use client";

import { useSession } from "next-auth/react";
import { ReactNode } from "react";

interface RoleGuardProps {
  allowedRoles: string[];
  children: ReactNode;
  fallback?: ReactNode;
  requireAll?: boolean; // If true, user must have ALL roles, if false, user needs ANY role
}

export default function RoleGuard({ 
  allowedRoles, 
  children, 
  fallback = null,
  requireAll = false 
}: RoleGuardProps) {
  const { data: session, status } = useSession();

  // Show loading state while session is being fetched
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If no session, don't render children
  if (!session?.user) {
    return <>{fallback}</>;
  }

  const userRole = session.user.role?.toLowerCase();
  const normalizedAllowedRoles = allowedRoles.map(role => role.toLowerCase());

  // Check if user has required role(s)
  const hasAccess = requireAll 
    ? normalizedAllowedRoles.every(role => userRole === role)
    : normalizedAllowedRoles.includes(userRole);

  if (!hasAccess) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Convenience components for specific roles
export function AdminOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={["admin"]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function ManagerOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={["manager"]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function ReceptionistOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={["receptionist"]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function StaffOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={["admin", "manager", "receptionist"]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

// Hook for checking roles in components
export function useRole() {
  const { data: session } = useSession();
  
  const hasRole = (roles: string | string[]): boolean => {
    if (!session?.user?.role) return false;
    
    const userRole = session.user.role.toLowerCase();
    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    const normalizedRoles = allowedRoles.map(role => role.toLowerCase());
    
    return normalizedRoles.includes(userRole);
  };

  const isAdmin = (): boolean => hasRole("admin");
  const isManager = (): boolean => hasRole("manager");
  const isReceptionist = (): boolean => hasRole("receptionist");
  const isStaff = (): boolean => hasRole(["admin", "manager", "receptionist"]);

  return {
    role: session?.user?.role?.toLowerCase(),
    hasRole,
    isAdmin,
    isManager,
    isReceptionist,
    isStaff,
  };
}
