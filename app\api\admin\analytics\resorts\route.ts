import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/auth-utils";

export async function GET() {
  // Check authorization
  const authResult = await requireRole(["admin", "manager"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    // Get current date and calculate date ranges
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Basic resort statistics
    const totalResorts = await prisma.resort.count();
    const totalRooms = await prisma.room.count();
    const activeRooms = await prisma.room.count({
      where: { isActive: true }
    });

    // Booking statistics
    const totalBookings = await prisma.booking.count({
      where: {
        OR: [
          { resortId: { not: null } },
          { roomId: { not: null } }
        ]
      }
    });

    const bookingsThisMonth = await prisma.booking.count({
      where: {
        createdAt: { gte: thisMonth },
        OR: [
          { resortId: { not: null } },
          { roomId: { not: null } }
        ]
      }
    });

    const bookingsLastMonth = await prisma.booking.count({
      where: {
        createdAt: { 
          gte: lastMonth,
          lt: thisMonth
        },
        OR: [
          { resortId: { not: null } },
          { roomId: { not: null } }
        ]
      }
    });

    // Revenue calculations (simplified - you might want to add actual pricing logic)
    const confirmedBookings = await prisma.booking.findMany({
      where: {
        status: "CONFIRMED",
        OR: [
          { resortId: { not: null } },
          { roomId: { not: null } }
        ]
      },
      include: {
        room: true,
        resort: true
      }
    });

    const totalRevenue = confirmedBookings.reduce((sum, booking) => {
      if (booking.room) {
        const days = booking.checkOut && booking.checkIn 
          ? Math.ceil((new Date(booking.checkOut).getTime() - new Date(booking.checkIn).getTime()) / (1000 * 60 * 60 * 24))
          : 1;
        return sum + (booking.room.price * days);
      }
      return sum;
    }, 0);

    // Top performing resorts
    const resortBookingCounts = await prisma.booking.groupBy({
      by: ['resortId'],
      where: {
        resortId: { not: null },
        createdAt: { gte: thirtyDaysAgo }
      },
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      },
      take: 5
    });

    const topResorts = await Promise.all(
      resortBookingCounts.map(async (item) => {
        const resort = await prisma.resort.findUnique({
          where: { id: item.resortId! }
        });
        return {
          resort,
          bookings: item._count.id,
          revenue: confirmedBookings
            .filter(b => b.resortId === item.resortId && b.room)
            .reduce((sum, booking) => {
              const days = booking.checkOut && booking.checkIn 
                ? Math.ceil((new Date(booking.checkOut).getTime() - new Date(booking.checkIn).getTime()) / (1000 * 60 * 60 * 24))
                : 1;
              return sum + (booking.room!.price * days);
            }, 0)
        };
      })
    );

    // Occupancy rate calculation
    const totalPossibleNights = activeRooms * 30; // 30 days
    const bookedNights = confirmedBookings
      .filter(b => b.checkIn && b.checkOut)
      .reduce((sum, booking) => {
        const checkIn = new Date(booking.checkIn);
        const checkOut = new Date(booking.checkOut!);
        const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
        return sum + nights;
      }, 0);
    
    const occupancyRate = totalPossibleNights > 0 ? (bookedNights / totalPossibleNights) * 100 : 0;

    // Booking trends (last 7 days)
    const bookingTrends = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);
      
      const dayBookings = await prisma.booking.count({
        where: {
          createdAt: {
            gte: startOfDay,
            lt: endOfDay
          },
          OR: [
            { resortId: { not: null } },
            { roomId: { not: null } }
          ]
        }
      });

      bookingTrends.push({
        date: startOfDay.toISOString().split('T')[0],
        bookings: dayBookings
      });
    }

    // Review statistics
    const totalReviews = await prisma.review.count({
      where: {
        OR: [
          { resortId: { not: null } },
          { roomId: { not: null } }
        ]
      }
    });

    const averageRating = await prisma.review.aggregate({
      where: {
        OR: [
          { resortId: { not: null } },
          { roomId: { not: null } }
        ],
        isPublished: true
      },
      _avg: {
        rating: true
      }
    });

    // Calculate growth rates
    const bookingGrowthRate = bookingsLastMonth > 0 
      ? ((bookingsThisMonth - bookingsLastMonth) / bookingsLastMonth) * 100 
      : 0;

    const response = {
      overview: {
        totalResorts,
        totalRooms,
        activeRooms,
        totalBookings,
        totalRevenue,
        averageOccupancy: Math.round(occupancyRate * 100) / 100,
        averageRating: averageRating._avg.rating ? Math.round(averageRating._avg.rating * 10) / 10 : 0,
        totalReviews
      },
      growth: {
        bookingsThisMonth,
        bookingsLastMonth,
        bookingGrowthRate: Math.round(bookingGrowthRate * 100) / 100
      },
      topPerformingResorts: topResorts,
      bookingTrends,
      lastUpdated: now.toISOString()
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching resort analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch analytics data" },
      { status: 500 }
    );
  }
}
