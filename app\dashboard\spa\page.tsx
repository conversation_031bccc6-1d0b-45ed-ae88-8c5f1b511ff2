"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { SpaTreatment } from "@/app/types/spa";
import {
  AdminCardSkeleton,
  GridSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";

// TypeScript interfaces
interface SpaListProps {
  className?: string;
}

interface SpaListState {
  treatments: SpaTreatment[];
  loading: boolean;
  error: string | null;
}

export default function SpaList({ className = "" }: SpaListProps) {
  const [state, setState] = useState<SpaListState>({
    treatments: [],
    loading: true,
    error: null,
  });

  const fetchTreatments = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const response = await fetch("/api/spa");
      if (!response.ok) {
        throw new Error(`Failed to fetch spa treatments: ${response.status}`);
      }

      const data = await response.json();
      setState({
        treatments: data,
        loading: false,
        error: null,
      });
    } catch (error) {
      console.error("Error fetching spa treatments:", error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      }));
    }
  };

  useEffect(() => {
    fetchTreatments();
  }, []);

  const handleRetry = () => {
    fetchTreatments();
  };

  return (
    <div className={`p-6 max-w-7xl mx-auto ${className}`}>
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Spa Treatment Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage your spa treatments and wellness services
          </p>
        </div>
        <Link
          href="/admin/spa/create"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
          aria-label="Create new spa treatment"
        >
          <span className="mr-2">+</span>
          Add Treatment
        </Link>
      </div>

      {/* Loading State */}
      {state.loading && (
        <GridSkeleton
          count={6}
          columns={3}
          SkeletonComponent={AdminCardSkeleton}
        />
      )}

      {/* Error State */}
      {state.error && !state.loading && (
        <LoadingError resource="spa treatments" onRetry={handleRetry} />
      )}

      {/* Empty State */}
      {!state.loading && !state.error && state.treatments.length === 0 && (
        <EmptyState
          title="No Spa Treatments Found"
          message="Start by creating your first spa treatment."
          actionLabel="+ Create First Treatment"
          actionHref="/admin/spa/create"
          icon={<div className="text-gray-400 text-6xl mb-4">🧘‍♀️</div>}
        />
      )}

      {/* Treatments Grid */}
      {!state.loading && !state.error && state.treatments.length > 0 && (
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          role="list"
          aria-label="Spa treatments list"
        >
          {state.treatments.map((treatment) => (
            <div
              key={treatment.id}
              className="border rounded-lg p-4 shadow-sm bg-white hover:shadow-md transition-shadow"
              role="listitem"
            >
              <div className="relative mb-3">
                <Image
                  src={treatment.image}
                  alt={treatment.name}
                  width={400}
                  height={160}
                  className="h-40 w-full object-cover rounded"
                  loading="lazy"
                />
                <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                  {treatment.duration} min
                </div>
              </div>

              <div className="space-y-2">
                <h2 className="text-xl font-semibold text-gray-900">
                  {treatment.name}
                </h2>
                <p className="text-sm text-gray-600 line-clamp-2">
                  {treatment.description}
                </p>
                <p className="font-bold text-blue-600">{treatment.price} ETB</p>
              </div>

              <div className="mt-4 flex justify-between items-center">
                <Link
                  href={`/admin/spa/${treatment.id}/edit`}
                  className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                  aria-label={`Edit ${treatment.name} treatment`}
                >
                  Edit Treatment
                </Link>
                <Link
                  href={`/spa/treatments/${treatment.id}`}
                  target="_blank"
                  className="text-green-600 hover:text-green-800 font-medium text-sm"
                  aria-label={`View ${treatment.name} treatment details`}
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
