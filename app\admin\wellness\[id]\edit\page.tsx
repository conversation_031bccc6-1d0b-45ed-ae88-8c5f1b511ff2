import WellnessForm from "@/app/components/admin/WellnessForm";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

export default async function EditWellness({ params }: { params: { id: string } }) {
  const wellnessService = await prisma.wellnessService.findUnique({
    where: { id: params.id },
  });

  if (!wellnessService) return notFound();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Edit Wellness Service</h1>
        <p className="text-gray-600 mt-1">
          Update the details for {wellnessService.name}
        </p>
      </div>
      <WellnessForm initialData={wellnessService} />
    </div>
  );
}
