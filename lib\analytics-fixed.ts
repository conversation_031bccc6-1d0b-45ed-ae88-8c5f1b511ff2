import { prisma } from "./prisma";
import { logAuditEvent } from "./security";
import type { NextRequest, NextResponse } from "next/server";

// Type definitions for better type safety
interface BookingWithRelations {
  id: string;
  userEmail: string;
  resortId: string | null;
  roomId: string | null;
  spaId: string | null;
  experienceId: string | null;
  wellnessId: string | null;
  eventId: string | null;
  checkIn: Date;
  checkOut: Date | null;
  status: string;
  totalAmount: number | null;
  createdAt: Date;
  resort?: { id: string; name: string } | null;
  spaTreatment?: { id: string; name: string } | null;
}

interface MetricData {
  calls: number;
  totalDuration: number;
  errors: number;
  lastCleanup: Date;
}

interface UserActionMetadata {
  [key: string]: string | number | boolean | null;
}

export interface BookingAnalytics {
  totalBookings: number;
  bookingsByStatus: Record<string, number>;
  bookingsByType: Record<string, number>;
  revenueByMonth: Array<{ month: string; revenue: number; bookings: number }>;
  revenueByQuarter: Array<{ quarter: string; revenue: number; growth: number }>;
  popularResorts: Array<{ name: string; bookings: number; revenue: number }>;
  popularSpaTreatments: Array<{
    name: string;
    bookings: number;
    revenue: number;
  }>;
  averageBookingValue: number;
  cancellationRate: number;
  occupancyRate: number;
  seasonalTrends: Array<{ period: string; bookings: number; revenue: number }>;
  customerRetentionRate: number;
  averageLeadTime: number;
  peakBookingHours: Array<{ hour: number; bookings: number }>;
  weekdayVsWeekendRatio: { weekday: number; weekend: number };
}

export interface SystemMetrics {
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  databaseConnections: number;
  memoryUsage: number;
  cpuUsage: number;
}

export class AnalyticsService {
  private static instance: AnalyticsService;
  private metrics: Map<string, MetricData> = new Map();
  private readonly MAX_METRICS_SIZE = 10000;
  private readonly CLEANUP_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // Input validation helper
  private validateDateRange(startDate: Date, endDate: Date): void {
    if (!(startDate instanceof Date) || !(endDate instanceof Date)) {
      throw new Error(
        "Invalid date parameters: startDate and endDate must be Date objects"
      );
    }

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error("Invalid date parameters: dates contain invalid values");
    }

    if (startDate >= endDate) {
      throw new Error("Invalid date range: startDate must be before endDate");
    }

    const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 year
    if (endDate.getTime() - startDate.getTime() > maxRange) {
      throw new Error("Date range too large: maximum range is 1 year");
    }
  }

  // Memory management for metrics
  private cleanupMetrics(): void {
    if (this.metrics.size > this.MAX_METRICS_SIZE) {
      const now = new Date();
      const cutoff = new Date(now.getTime() - this.CLEANUP_INTERVAL);

      for (const [key, metric] of this.metrics.entries()) {
        if (metric.lastCleanup < cutoff) {
          this.metrics.delete(key);
        }
      }
    }
  }

  // Enhanced error handling
  private async handleAnalyticsError(
    operation: string,
    error: unknown,
    context?: Record<string, unknown>
  ): Promise<never> {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    const errorContext = {
      operation,
      error: errorMessage,
      context,
      timestamp: new Date().toISOString(),
    };

    console.error("Analytics error:", errorContext);

    try {
      await logAuditEvent({
        action: "ANALYTICS_ERROR",
        resource: operation,
        ip: "system",
        userAgent: "analytics_service",
        success: false,
        error: JSON.stringify(errorContext),
      });
    } catch (auditError) {
      console.error("Failed to log analytics error:", auditError);
    }

    throw new Error(
      `Analytics operation failed: ${operation} - ${errorMessage}`
    );
  }

  // Optimized booking analytics with proper validation
  async getBookingAnalytics(
    startDate: Date = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    endDate: Date = new Date()
  ): Promise<BookingAnalytics> {
    try {
      // Validate inputs
      this.validateDateRange(startDate, endDate);

      // Single optimized query with all necessary data
      const bookings = await prisma.booking.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          resort: {
            select: { id: true, name: true },
          },
          spaTreatment: {
            select: { id: true, name: true },
          },
        },
      });

      // Calculate all metrics from the single query result
      const analytics = this.calculateAnalyticsFromBookings(bookings);

      // Get additional metrics that require separate queries
      const [
        revenueByMonth,
        revenueByQuarter,
        seasonalTrends,
        customerRetentionRate,
      ] = await Promise.all([
        this.calculateOptimizedRevenueByMonth(startDate, endDate),
        this.calculateOptimizedRevenueByQuarter(startDate, endDate),
        this.calculateOptimizedSeasonalTrends(startDate, endDate),
        this.calculateCustomerRetentionRate(),
      ]);

      return {
        ...analytics,
        revenueByMonth,
        revenueByQuarter,
        seasonalTrends,
        customerRetentionRate,
      };
    } catch (error) {
      await this.handleAnalyticsError("getBookingAnalytics", error, {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      });
    }
  }

  // Helper method to calculate analytics from booking data
  private calculateAnalyticsFromBookings(
    bookings: BookingWithRelations[]
  ): Partial<BookingAnalytics> {
    const totalBookings = bookings.length;

    const bookingsByStatus = bookings.reduce((acc, booking) => {
      acc[booking.status] = (acc[booking.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const bookingsByType = bookings.reduce((acc, booking) => {
      const type = booking.resortId
        ? "Resort"
        : booking.spaId
        ? "Spa"
        : booking.experienceId
        ? "Experience"
        : booking.wellnessId
        ? "Wellness"
        : booking.eventId
        ? "Event"
        : "Other";
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Calculate popular items with proper revenue calculation
    const popularResorts = this.getPopularItemsWithRevenue(
      bookings.filter((b) => b.resort),
      "resort"
    );

    const popularSpaTreatments = this.getPopularItemsWithRevenue(
      bookings.filter((b) => b.spaTreatment),
      "spaTreatment"
    );

    // Calculate rates with proper validation
    const cancelledBookings = bookings.filter(
      (b) => b.status === "CANCELLED"
    ).length;
    const confirmedBookings = bookings.filter(
      (b) => b.status === "CONFIRMED"
    ).length;

    const cancellationRate =
      totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0;
    const occupancyRate =
      totalBookings > 0 ? (confirmedBookings / totalBookings) * 100 : 0;

    return {
      totalBookings,
      bookingsByStatus,
      bookingsByType,
      popularResorts,
      popularSpaTreatments,
      averageBookingValue: this.calculateAverageBookingValue(bookings),
      cancellationRate: Math.round(cancellationRate * 100) / 100,
      occupancyRate: Math.round(occupancyRate * 100) / 100,
      averageLeadTime: this.calculateAverageLeadTime(bookings),
      peakBookingHours: this.calculatePeakBookingHours(bookings),
      weekdayVsWeekendRatio: this.calculateWeekdayWeekendRatio(bookings),
    };
  }

  // Optimized revenue calculation using aggregation
  private async calculateOptimizedRevenueByMonth(
    startDate: Date,
    endDate: Date
  ): Promise<Array<{ month: string; revenue: number; bookings: number }>> {
    try {
      // Use Prisma aggregation for better performance
      const monthlyData = await prisma.booking.groupBy({
        by: ["createdAt"],
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
          status: "CONFIRMED",
        },
        _count: {
          id: true,
        },
        _sum: {
          totalAmount: true,
        },
      });

      // Process the aggregated data
      const monthlyStats = new Map<
        string,
        { bookings: number; revenue: number }
      >();

      monthlyData.forEach((data) => {
        const monthKey = data.createdAt.toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
        });

        const existing = monthlyStats.get(monthKey) || {
          bookings: 0,
          revenue: 0,
        };
        existing.bookings += data._count.id;
        existing.revenue += data._sum.totalAmount || 200; // Fallback to default if no amount
        monthlyStats.set(monthKey, existing);
      });

      return Array.from(monthlyStats.entries()).map(([month, data]) => ({
        month,
        ...data,
      }));
    } catch (error) {
      await this.handleAnalyticsError(
        "calculateOptimizedRevenueByMonth",
        error
      );
    }
  }

  // Optimized quarterly revenue calculation
  private async calculateOptimizedRevenueByQuarter(
    startDate: Date,
    endDate: Date
  ): Promise<Array<{ quarter: string; revenue: number; growth: number }>> {
    try {
      const quarters: Array<{
        quarter: string;
        revenue: number;
        growth: number;
      }> = [];
      const currentYear = startDate.getFullYear();
      const endYear = endDate.getFullYear();

      for (let year = currentYear; year <= endYear; year++) {
        for (let quarter = 1; quarter <= 4; quarter++) {
          const quarterStart = new Date(year, (quarter - 1) * 3, 1);
          const quarterEnd = new Date(year, quarter * 3, 0);

          if (quarterStart > endDate) break;
          if (quarterEnd < startDate) continue;

          const quarterData = await prisma.booking.aggregate({
            where: {
              createdAt: {
                gte: quarterStart,
                lte: quarterEnd,
              },
              status: "CONFIRMED",
            },
            _count: {
              id: true,
            },
            _sum: {
              totalAmount: true,
            },
          });

          const revenue =
            quarterData._sum.totalAmount || quarterData._count.id * 200;
          const previousQuarterRevenue =
            quarters.length > 0
              ? quarters[quarters.length - 1].revenue
              : revenue;
          const growth =
            previousQuarterRevenue > 0
              ? ((revenue - previousQuarterRevenue) / previousQuarterRevenue) *
                100
              : 0;

          quarters.push({
            quarter: `Q${quarter} ${year}`,
            revenue,
            growth: Math.round(growth * 100) / 100,
          });
        }
      }

      return quarters;
    } catch (error) {
      await this.handleAnalyticsError(
        "calculateOptimizedRevenueByQuarter",
        error
      );
    }
  }

  // Fixed seasonal trends calculation
  private async calculateOptimizedSeasonalTrends(
    startDate: Date,
    endDate: Date
  ): Promise<Array<{ period: string; bookings: number; revenue: number }>> {
    try {
      const seasons = [
        { name: "Spring", months: [2, 3, 4] },
        { name: "Summer", months: [5, 6, 7] },
        { name: "Fall", months: [8, 9, 10] },
        { name: "Winter", months: [11, 0, 1] },
      ];

      const trends = await Promise.all(
        seasons.map(async (season) => {
          // Create proper date ranges for each season
          const seasonBookings = await prisma.booking.aggregate({
            where: {
              createdAt: {
                gte: startDate,
                lte: endDate,
              },
              status: "CONFIRMED",
              AND: {
                OR: season.months.map((month) => ({
                  createdAt: {
                    gte: new Date(startDate.getFullYear(), month, 1),
                    lt: new Date(startDate.getFullYear(), month + 1, 1),
                  },
                })),
              },
            },
            _count: {
              id: true,
            },
            _sum: {
              totalAmount: true,
            },
          });

          return {
            period: season.name,
            bookings: seasonBookings._count.id,
            revenue:
              seasonBookings._sum.totalAmount || seasonBookings._count.id * 200,
          };
        })
      );

      return trends;
    } catch (error) {
      await this.handleAnalyticsError(
        "calculateOptimizedSeasonalTrends",
        error
      );
    }
  }

  // Enhanced performance monitoring with cleanup
  async trackApiCall(
    endpoint: string,
    method: string,
    duration: number,
    success: boolean
  ): Promise<void> {
    try {
      // Input validation
      if (
        !endpoint ||
        !method ||
        typeof duration !== "number" ||
        duration < 0
      ) {
        throw new Error("Invalid parameters for API call tracking");
      }

      const key = `api_${endpoint}_${method}`;
      const existing = this.metrics.get(key) || {
        calls: 0,
        totalDuration: 0,
        errors: 0,
        lastCleanup: new Date(),
      };

      existing.calls += 1;
      existing.totalDuration += duration;
      if (!success) existing.errors += 1;
      existing.lastCleanup = new Date();

      this.metrics.set(key, existing);

      // Cleanup metrics periodically
      this.cleanupMetrics();

      // Log slow API calls with more context
      if (duration > 2000) {
        await logAuditEvent({
          action: "SLOW_API_CALL",
          resource: endpoint,
          ip: "system",
          userAgent: "performance_monitor",
          success: true,
          error: `${method} ${endpoint} took ${duration}ms (calls: ${
            existing.calls
          }, avg: ${Math.round(existing.totalDuration / existing.calls)}ms)`,
        });
      }
    } catch (error) {
      await this.handleAnalyticsError("trackApiCall", error, {
        endpoint,
        method,
        duration,
        success,
      });
    }
  }

  // Enhanced system metrics with error handling
  async getSystemMetrics(): Promise<SystemMetrics> {
    try {
      const apiMetrics = Array.from(this.metrics.entries())
        .filter(([key]) => key.startsWith("api_"))
        .map(([, value]) => value);

      const totalCalls = apiMetrics.reduce(
        (sum, metric) => sum + metric.calls,
        0
      );
      const totalDuration = apiMetrics.reduce(
        (sum, metric) => sum + metric.totalDuration,
        0
      );
      const totalErrors = apiMetrics.reduce(
        (sum, metric) => sum + metric.errors,
        0
      );

      const averageResponseTime =
        totalCalls > 0 ? totalDuration / totalCalls : 0;
      const errorRate = totalCalls > 0 ? (totalErrors / totalCalls) * 100 : 0;

      // Get active users with better query
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const activeUsersCount = await prisma.booking.findMany({
        where: {
          createdAt: {
            gte: oneHourAgo,
          },
        },
        select: {
          userEmail: true,
        },
        distinct: ["userEmail"],
      });

      return {
        responseTime: Math.round(averageResponseTime),
        errorRate: Math.round(errorRate * 100) / 100,
        activeUsers: activeUsersCount.length,
        databaseConnections: 0, // Would need database-specific monitoring
        memoryUsage: 0, // Would need system monitoring
        cpuUsage: 0, // Would need system monitoring
      };
    } catch (error) {
      await this.handleAnalyticsError("getSystemMetrics", error);
    }
  }

  // Enhanced user action tracking with proper types
  async trackUserAction(
    userId: string,
    action: string,
    metadata?: UserActionMetadata
  ): Promise<void> {
    try {
      // Input validation
      if (!userId || !action) {
        throw new Error(
          "userId and action are required for user action tracking"
        );
      }

      await logAuditEvent({
        userId,
        action: `USER_ACTION_${action.toUpperCase()}`,
        resource: "user_behavior",
        ip: "system",
        userAgent: "analytics_service",
        success: true,
        error: metadata ? JSON.stringify(metadata) : undefined,
      });
    } catch (error) {
      console.error("Error tracking user action:", error);
      // Don't throw here to avoid breaking user flows
    }
  }

  // Enhanced helper methods with proper typing
  private getPopularItemsWithRevenue(
    bookings: BookingWithRelations[],
    type: "resort" | "spaTreatment"
  ): Array<{ name: string; bookings: number; revenue: number }> {
    const items = bookings.reduce((acc, booking) => {
      const item = booking[type];
      if (item) {
        const name = item.name;
        if (!acc[name]) {
          acc[name] = { bookings: 0, revenue: 0 };
        }
        acc[name].bookings += 1;
        acc[name].revenue += booking.totalAmount || 200; // Use actual amount or fallback
      }
      return acc;
    }, {} as Record<string, { bookings: number; revenue: number }>);

    return Object.entries(items)
      .map(([name, data]) => ({ name, ...data }))
      .sort((a, b) => b.bookings - a.bookings)
      .slice(0, 5);
  }

  private calculateAverageBookingValue(
    bookings: BookingWithRelations[]
  ): number {
    const confirmedBookings = bookings.filter((b) => b.status === "CONFIRMED");
    if (confirmedBookings.length === 0) return 0;

    const totalValue = confirmedBookings.reduce((sum, booking) => {
      return sum + (booking.totalAmount || 200); // Use actual amount or fallback
    }, 0);

    return Math.round((totalValue / confirmedBookings.length) * 100) / 100;
  }

  private async calculateCustomerRetentionRate(): Promise<number> {
    try {
      // Calculate customers who made multiple bookings
      const userBookings = await prisma.booking.groupBy({
        by: ["userEmail"],
        _count: {
          id: true,
        },
        having: {
          id: {
            _count: {
              gt: 1,
            },
          },
        },
      });

      const totalUsers = await prisma.booking.findMany({
        select: { userEmail: true },
        distinct: ["userEmail"],
      });

      return totalUsers.length > 0
        ? Math.round((userBookings.length / totalUsers.length) * 100 * 100) /
            100
        : 0;
    } catch (error) {
      await this.handleAnalyticsError("calculateCustomerRetentionRate", error);
    }
  }

  private calculateAverageLeadTime(bookings: BookingWithRelations[]): number {
    const leadTimes = bookings
      .filter((b) => b.checkIn)
      .map((b) => {
        const leadTime = Math.floor(
          (new Date(b.checkIn).getTime() - new Date(b.createdAt).getTime()) /
            (1000 * 60 * 60 * 24)
        );
        return Math.max(0, leadTime); // Ensure non-negative lead times
      });

    return leadTimes.length > 0
      ? Math.round(
          leadTimes.reduce((sum, time) => sum + time, 0) / leadTimes.length
        )
      : 0;
  }

  private calculatePeakBookingHours(
    bookings: BookingWithRelations[]
  ): Array<{ hour: number; bookings: number }> {
    const hourCounts = bookings.reduce((acc, booking) => {
      const hour = new Date(booking.createdAt).getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    return Object.entries(hourCounts)
      .map(([hour, bookings]) => ({ hour: parseInt(hour), bookings }))
      .sort((a, b) => b.bookings - a.bookings)
      .slice(0, 5);
  }

  private calculateWeekdayWeekendRatio(bookings: BookingWithRelations[]): {
    weekday: number;
    weekend: number;
  } {
    const weekdayBookings = bookings.filter((b) => {
      const day = new Date(b.createdAt).getDay();
      return day >= 1 && day <= 5; // Monday to Friday
    }).length;

    const weekendBookings = bookings.filter((b) => {
      const day = new Date(b.createdAt).getDay();
      return day === 0 || day === 6; // Saturday and Sunday
    }).length;

    const total = weekdayBookings + weekendBookings;
    return {
      weekday: total > 0 ? Math.round((weekdayBookings / total) * 100) : 0,
      weekend: total > 0 ? Math.round((weekendBookings / total) * 100) : 0,
    };
  }

  // Clear metrics (for testing or periodic cleanup)
  clearMetrics(): void {
    this.metrics.clear();
  }

  // Export analytics data with proper error handling
  async exportAnalytics(
    format: "json" | "csv" = "json"
  ): Promise<string | object> {
    try {
      const [analytics, systemMetrics] = await Promise.all([
        this.getBookingAnalytics(),
        this.getSystemMetrics(),
      ]);

      const data = {
        timestamp: new Date().toISOString(),
        bookingAnalytics: analytics,
        systemMetrics,
      };

      if (format === "json") {
        return JSON.stringify(data, null, 2);
      }

      // CSV export would require additional formatting
      return data;
    } catch (error) {
      await this.handleAnalyticsError("exportAnalytics", error, { format });
    }
  }
}

// Singleton instance
export const analytics = AnalyticsService.getInstance();

// Corrected middleware with proper types
export function createAnalyticsMiddleware() {
  return async (
    req: NextRequest,
    res: NextResponse,
    next: () => Promise<void>
  ): Promise<void> => {
    const start = Date.now();
    const endpoint = new URL(req.url).pathname;
    const method = req.method;

    try {
      await next();
      const duration = Date.now() - start;
      await analytics.trackApiCall(endpoint, method, duration, true);
    } catch (error) {
      const duration = Date.now() - start;
      await analytics.trackApiCall(endpoint, method, duration, false);
      throw error;
    }
  };
}
