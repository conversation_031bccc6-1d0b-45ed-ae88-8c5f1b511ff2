# Kuriftu Water Park - Secure Booking System

A comprehensive, enterprise-grade booking system for Kuriftu Water Park built with Next.js, featuring advanced security, role-based access control, and real-time analytics.

## 🔒 Security Features

- **Enterprise-grade Authentication** with NextAuth.js and Google OAuth
- **Role-based Access Control (RBAC)** with granular permissions
- **Advanced Input Validation** using Zod schemas
- **CSRF Protection** and security headers
- **Rate Limiting** and DDoS protection
- **Audit Logging** for all security events
- **File Upload Security** with content scanning
- **Database Security** with connection pooling and SSL
- **Encrypted Backups** with automated retention

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- MongoDB Atlas account
- Google OAuth credentials
- Cloudinary account (for image uploads)
- Email service (Gmail/SMTP)

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd kuriftu_weterpark
   ```

2. **Install dependencies**

   ```bash
   npm install --legacy-peer-deps
   ```

3. **Set up environment variables**

   ```bash
   npm run setup:env
   ```

   This creates a `.env` file from the template with secure secrets.

4. **Configure your environment**
   Edit `.env` and add your actual credentials:

   - MongoDB connection string
   - Google OAuth credentials
   - Cloudinary settings
   - Email configuration

5. **Validate environment**

   ```bash
   npm run validate:env
   ```

6. **Set up the database**

   ```bash
   npm run db:push
   npm run db:seed
   ```

7. **Run the development server**
   ```bash
   npm run dev
   ```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 🛡️ Security Setup

### Environment Security

1. **Never commit `.env` files** - they're automatically added to `.gitignore`
2. **Use strong secrets** - minimum 32 characters for all secret keys
3. **Rotate secrets regularly** - especially in production
4. **Use environment-specific configurations** for different deployment stages

### Database Security

1. **Enable SSL/TLS** in production:

   ```env
   MONGODB_URI=mongodb+srv://user:<EMAIL>/db?ssl=true&authSource=admin
   ```

2. **Use connection pooling** - automatically configured in `lib/database-security.ts`

3. **Regular backups**:
   ```bash
   npm run backup:db
   ```

### Authentication & Authorization

The system implements a 4-tier role hierarchy:

- **Admin**: Full system access, user management, security dashboard
- **Manager**: Resort/spa management, booking oversight, analytics
- **Receptionist**: Booking management, customer service
- **User**: Personal bookings, profile management

### Security Monitoring

Access the security dashboard at `/admin/security` (admin only) to monitor:

- Failed login attempts
- Suspicious activity
- System performance metrics
- Security alerts and threats

## 📊 Available Scripts

### Development

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Database

- `npm run db:migrate` - Run database migrations
- `npm run db:push` - Push schema changes
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with sample data

### Security & Maintenance

- `npm run setup:env` - Create environment file
- `npm run validate:env` - Validate environment configuration
- `npm run security:audit` - Run security audit
- `npm run backup:db` - Create database backup

### Testing

- `npm run test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:security` - Run security-specific tests

## 🏗️ Architecture

### Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: MongoDB with Prisma
- **Authentication**: NextAuth.js with Google OAuth
- **File Storage**: Cloudinary
- **Email**: Nodemailer with SMTP
- **Validation**: Zod schemas
- **Security**: Custom middleware and utilities

### Project Structure

```
├── app/                    # Next.js 13+ app directory
│   ├── api/               # API routes
│   ├── components/        # React components
│   ├── admin/            # Admin dashboard
│   └── auth/             # Authentication pages
├── lib/                   # Utility libraries
│   ├── auth.ts           # Authentication configuration
│   ├── security.ts       # Security utilities
│   ├── validation.ts     # Input validation schemas
│   └── analytics.ts      # Performance monitoring
├── prisma/               # Database schema and migrations
├── scripts/              # Utility scripts
│   ├── setup-env.js      # Environment setup
│   ├── security-check.js # Security auditing
│   └── backup-db.js      # Database backup
└── __tests__/            # Test files
```

## 🔐 Security Best Practices

### For Developers

1. **Input Validation**: Always validate user input with Zod schemas
2. **Authorization Checks**: Use `requireRole()` for API endpoints
3. **Error Handling**: Never expose sensitive information in errors
4. **Logging**: Use `logAuditEvent()` for security-relevant actions
5. **File Uploads**: Validate file types and scan content

### For Deployment

1. **HTTPS Only**: Always use HTTPS in production
2. **Environment Variables**: Use secure secret management
3. **Database**: Enable SSL and use strong passwords
4. **Monitoring**: Set up alerts for security events
5. **Backups**: Automated, encrypted, and tested regularly

## 📈 Monitoring & Analytics

### Performance Monitoring

The system includes built-in performance monitoring:

- API response times
- Error rates
- Database query performance
- User activity patterns

### Security Analytics

Comprehensive security monitoring:

- Authentication failures
- Authorization violations
- Suspicious activity patterns
- Rate limit violations

### Business Analytics

Booking system analytics:

- Booking patterns and trends
- Revenue analytics
- Popular services
- Customer behavior insights

## 🧪 Testing

### Security Testing

Run the security test suite:

```bash
npm run test:security
```

This includes tests for:

- Authentication requirements
- Authorization boundaries
- Input validation
- CSRF protection
- Rate limiting

### Test Accounts

After running `npm run db:seed`, you can use these test accounts:

- **Admin**: <EMAIL>
- **Manager**: <EMAIL>
- **Receptionist**: <EMAIL>
- **User**: <EMAIL>

## 🚨 Security Incident Response

### Immediate Response

1. **Identify the scope** of the security incident
2. **Contain the threat** by blocking malicious IPs or disabling accounts
3. **Preserve evidence** by backing up logs and system state

### Investigation

1. **Review audit logs** in the security dashboard
2. **Identify affected users** and data
3. **Determine root cause** and attack vector

### Recovery

1. **Patch vulnerabilities** immediately
2. **Reset compromised credentials**
3. **Notify affected users** if required by law
4. **Update security measures** to prevent recurrence

## 📞 Support & Contact

For security issues or questions:

- Create a security issue in the repository
- Follow responsible disclosure practices
- Include detailed reproduction steps
- Provide impact assessment

## 📄 License

This project is licensed under the MIT License.

---

**⚠️ Security Notice**: This system handles sensitive user data. Always follow security best practices and keep dependencies updated. Regular security audits are recommended.
