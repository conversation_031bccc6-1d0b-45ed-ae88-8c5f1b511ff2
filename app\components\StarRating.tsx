"use client";

import { useState } from "react";
import { StarRatingProps } from "@/app/types/review";

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxRating = 5,
  size = 'md',
  interactive = false,
  onChange,
  className = "",
}) => {
  const [hoverRating, setHoverRating] = useState<number>(0);

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const handleClick = (value: number) => {
    if (interactive && onChange) {
      onChange(value);
    }
  };

  const handleMouseEnter = (value: number) => {
    if (interactive) {
      setHoverRating(value);
    }
  };

  const handleMouseLeave = () => {
    if (interactive) {
      setHoverRating(0);
    }
  };

  const getStarFill = (starIndex: number) => {
    const currentRating = interactive && hoverRating > 0 ? hoverRating : rating;
    
    if (starIndex <= currentRating) {
      return 'text-yellow-400'; // Filled star
    } else if (starIndex - 0.5 <= currentRating) {
      return 'text-yellow-400'; // Half-filled star (for future enhancement)
    } else {
      return 'text-gray-300'; // Empty star
    }
  };

  return (
    <div
      className={`flex items-center space-x-1 ${className}`}
      role={interactive ? "radiogroup" : "img"}
      aria-label={interactive ? "Rate this item" : `Rating: ${rating} out of ${maxRating} stars`}
    >
      {Array.from({ length: maxRating }, (_, index) => {
        const starValue = index + 1;
        return (
          {interactive ? (
            <button
              key={index}
              type="button"
              className={`
                ${sizeClasses[size]}
                cursor-pointer hover:scale-110 transition-transform
                ${getStarFill(starValue)}
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded
              `}
              onClick={() => handleClick(starValue)}
              onMouseEnter={() => handleMouseEnter(starValue)}
              onMouseLeave={handleMouseLeave}
              aria-label={`Rate ${starValue} stars`}
              role="radio"
              aria-checked={starValue === rating}
            >
              <svg
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
                className="w-full h-full"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </button>
          ) : (
            <span
              key={index}
              className={`
                ${sizeClasses[size]}
                cursor-default
                ${getStarFill(starValue)}
              `}
              aria-hidden="true"
            >
              <svg
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
                className="w-full h-full"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </span>
          )}
        );
      })}
      
      {!interactive && (
        <span className="ml-2 text-sm text-gray-600">
          ({rating.toFixed(1)})
        </span>
      )}
    </div>
  );
};

export default StarRating;
