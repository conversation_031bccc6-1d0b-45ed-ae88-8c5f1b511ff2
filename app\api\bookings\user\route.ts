import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { logAuditEvent } from "@/lib/security";

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      await logAuditEvent({
        action: "UNAUTHORIZED_USER_BOOKINGS_ACCESS",
        resource: "/api/bookings/user",
        ip: "unknown",
        userAgent: "unknown",
        success: false,
        error: "No authentication session",
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's bookings
    const bookings = await prisma.booking.findMany({
      where: {
        userEmail: session.user.email,
      },
      include: {
        resort: true,
        spaTreatment: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    await logAuditEvent({
      userId: session.user.id,
      action: "USER_BOOKINGS_ACCESSED",
      resource: "/api/bookings/user",
      ip: "unknown",
      userAgent: "unknown",
      success: true,
    });

    return NextResponse.json(bookings);
  } catch (error) {
    console.error("Error fetching user bookings:", error);
    return NextResponse.json(
      { error: "Failed to fetch bookings" },
      { status: 500 }
    );
  }
}
