"use client";

import React, { useRef, useState, useCallback } from 'react';
import { useImageUpload, UploadedImage } from '@/app/hooks/useImageUpload';
import Image from 'next/image';

export interface MultipleImageUploadProps {
  value: string[];
  onChange: (urls: string[]) => void;
  uploadType?: string;
  maxImages?: number;
  maxWidth?: number;
  maxHeight?: number;
  className?: string;
  disabled?: boolean;
}

export default function MultipleImageUpload({
  value = [],
  onChange,
  uploadType = 'gallery',
  maxImages = 10,
  maxWidth = 4096,
  maxHeight = 4096,
  className = '',
  disabled = false,
}: MultipleImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadQueue, setUploadQueue] = useState<string[]>([]);

  const { uploadState, uploadImage, clearError } = useImageUpload({
    uploadType,
    maxWidth,
    maxHeight,
    onUploadComplete: (image: UploadedImage) => {
      const newUrls = [...value, image.secure_url];
      onChange(newUrls);
      setUploadQueue(prev => prev.filter(id => id !== image.public_id));
    },
    onUploadError: (error: string) => {
      console.error('Upload error:', error);
      setUploadQueue([]);
    },
  });

  const handleFileSelect = useCallback(async (files: File[]) => {
    if (disabled) return;
    
    const remainingSlots = maxImages - value.length;
    const filesToUpload = files.slice(0, remainingSlots);
    
    if (filesToUpload.length === 0) return;
    
    clearError();
    
    // Add temporary IDs to upload queue for UI feedback
    const tempIds = filesToUpload.map(() => Math.random().toString(36).substring(7));
    setUploadQueue(tempIds);

    // Upload files sequentially to avoid overwhelming the server
    for (let i = 0; i < filesToUpload.length; i++) {
      await uploadImage(filesToUpload[i]);
    }
  }, [disabled, maxImages, value.length, uploadImage, clearError]);

  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleClick = useCallback(() => {
    if (disabled || uploadState.isUploading || value.length >= maxImages) return;
    fileInputRef.current?.click();
  }, [disabled, uploadState.isUploading, value.length, maxImages]);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    if (disabled || value.length >= maxImages) return;
    
    event.preventDefault();
    setIsDragOver(true);
  }, [disabled, value.length, maxImages]);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    if (disabled) return;
    
    event.preventDefault();
    setIsDragOver(false);
  }, [disabled]);

  const handleDrop = useCallback((event: React.DragEvent) => {
    if (disabled || value.length >= maxImages) return;
    
    event.preventDefault();
    setIsDragOver(false);

    const files = Array.from(event.dataTransfer.files).filter(file => 
      file.type.startsWith('image/')
    );
    
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [disabled, value.length, maxImages, handleFileSelect]);

  const handleRemoveImage = useCallback((index: number) => {
    if (disabled) return;
    
    const newUrls = value.filter((_, i) => i !== index);
    onChange(newUrls);
  }, [disabled, value, onChange]);

  const canUploadMore = value.length < maxImages && !disabled;

  const getUploadAreaClasses = () => {
    const baseClasses = `
      border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
      transition-all duration-200 ease-in-out
      ${className}
    `;

    if (!canUploadMore) {
      return `${baseClasses} border-gray-300 bg-gray-100 cursor-not-allowed`;
    }

    if (uploadState.isUploading) {
      return `${baseClasses} border-blue-400 bg-blue-50`;
    }

    if (isDragOver) {
      return `${baseClasses} border-blue-500 bg-blue-50`;
    }

    if (uploadState.error) {
      return `${baseClasses} border-red-400 bg-red-50 hover:border-red-500`;
    }

    return `${baseClasses} border-gray-300 hover:border-gray-400 hover:bg-gray-50`;
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      {canUploadMore && (
        <div
          className={getUploadAreaClasses()}
          onClick={handleClick}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          role="button"
          tabIndex={0}
          aria-label="Upload images"
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileInputChange}
            className="hidden"
            disabled={disabled}
          />

          {uploadState.isUploading ? (
            <div className="space-y-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-600">Uploading images...</p>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadState.progress}%` }}
                ></div>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
              </div>
              <p className="text-sm text-gray-600">
                Click to upload or drag and drop images
              </p>
              <p className="text-xs text-gray-500">
                PNG, JPG, GIF, WebP up to 10MB each
              </p>
              <p className="text-xs text-gray-500">
                {value.length} of {maxImages} images uploaded
              </p>
            </div>
          )}
        </div>
      )}

      {/* Error Display */}
      {uploadState.error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{uploadState.error}</p>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={clearError}
                className="text-red-400 hover:text-red-600"
                aria-label="Dismiss error"
              >
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image Grid */}
      {value.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {value.map((url, index) => (
            <div key={index} className="relative group">
              <div className="relative w-full h-32 bg-gray-100 rounded-lg overflow-hidden">
                <Image
                  src={url}
                  alt={`Uploaded image ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                />
              </div>
              {!disabled && (
                <button
                  onClick={() => handleRemoveImage(index)}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                  aria-label={`Remove image ${index + 1}`}
                >
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
          ))}
          
          {/* Upload Queue Placeholders */}
          {uploadQueue.map((id) => (
            <div key={id} className="relative">
              <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Upload Status */}
      {value.length >= maxImages && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <p className="text-sm text-blue-800">
            Maximum number of images ({maxImages}) reached.
          </p>
        </div>
      )}
    </div>
  );
}
