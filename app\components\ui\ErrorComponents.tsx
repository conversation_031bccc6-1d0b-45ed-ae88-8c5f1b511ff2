// Standardized error components for consistent error handling across the application

import React from "react";
import Link from "next/link";

// Base error display component
interface ErrorDisplayProps {
  title?: string;
  message: string;
  details?: string;
  onRetry?: () => void;
  retryLabel?: string;
  showRetry?: boolean;
  className?: string;
  icon?: React.ReactNode;
  variant?: "error" | "warning" | "info";
}

export function ErrorDisplay({
  title = "Something went wrong",
  message,
  details,
  onRetry,
  retryLabel = "Try Again",
  showRetry = true,
  className = "",
  icon,
  variant = "error"
}: ErrorDisplayProps) {
  const variantStyles = {
    error: {
      iconColor: "text-red-500",
      titleColor: "text-gray-900",
      messageColor: "text-gray-600",
      buttonColor: "bg-red-600 hover:bg-red-700"
    },
    warning: {
      iconColor: "text-yellow-500",
      titleColor: "text-gray-900",
      messageColor: "text-gray-600",
      buttonColor: "bg-yellow-600 hover:bg-yellow-700"
    },
    info: {
      iconColor: "text-blue-500",
      titleColor: "text-gray-900",
      messageColor: "text-gray-600",
      buttonColor: "bg-blue-600 hover:bg-blue-700"
    }
  }[variant];

  const defaultIcon = (
    <svg 
      className={`w-8 h-8 ${variantStyles.iconColor}`}
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
      />
    </svg>
  );

  return (
    <div 
      className={`text-center py-12 ${className}`}
      role="alert"
      aria-live="assertive"
    >
      <div className="max-w-md mx-auto">
        <div className="flex justify-center mb-4">
          {icon || defaultIcon}
        </div>
        
        <h3 className={`text-xl font-semibold ${variantStyles.titleColor} mb-2`}>
          {title}
        </h3>
        
        <p className={`${variantStyles.messageColor} mb-4`}>
          {message}
        </p>
        
        {details && (
          <details className="mb-4">
            <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
              Show details
            </summary>
            <pre className="mt-2 text-xs text-gray-600 bg-gray-50 p-3 rounded text-left overflow-auto">
              {details}
            </pre>
          </details>
        )}
        
        {showRetry && onRetry && (
          <button
            type="button"
            onClick={onRetry}
            className={`inline-flex items-center px-4 py-2 text-white rounded-md transition-colors ${variantStyles.buttonColor}`}
            aria-label={retryLabel}
          >
            {retryLabel}
          </button>
        )}
      </div>
    </div>
  );
}

// Specific error components for common scenarios

// Network/API error
export function NetworkError({ 
  onRetry, 
  className = "" 
}: { 
  onRetry?: () => void; 
  className?: string; 
}) {
  return (
    <ErrorDisplay
      title="Connection Error"
      message="Unable to connect to the server. Please check your internet connection and try again."
      onRetry={onRetry}
      className={className}
      icon={
        <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
        </svg>
      }
    />
  );
}

// Not found error
export function NotFoundError({ 
  title = "Not Found",
  message = "The item you're looking for doesn't exist or has been removed.",
  backLink = "/",
  backLabel = "Go Back",
  className = "" 
}: { 
  title?: string;
  message?: string;
  backLink?: string;
  backLabel?: string;
  className?: string; 
}) {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="text-gray-400 text-6xl mb-4">🔍</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        {title}
      </h3>
      <p className="text-gray-600 mb-4">
        {message}
      </p>
      <Link
        href={backLink}
        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
      >
        {backLabel}
      </Link>
    </div>
  );
}

// Empty state component
export function EmptyState({ 
  title,
  message,
  actionLabel,
  actionHref,
  onAction,
  icon,
  className = "" 
}: { 
  title: string;
  message: string;
  actionLabel?: string;
  actionHref?: string;
  onAction?: () => void;
  icon?: React.ReactNode;
  className?: string; 
}) {
  const defaultIcon = <div className="text-gray-400 text-6xl mb-4">📭</div>;

  return (
    <div className={`text-center py-12 ${className}`}>
      {icon || defaultIcon}
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        {title}
      </h3>
      <p className="text-gray-600 mb-4">
        {message}
      </p>
      {(actionLabel && (actionHref || onAction)) && (
        <>
          {actionHref ? (
            <Link
              href={actionHref}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              {actionLabel}
            </Link>
          ) : (
            <button
              type="button"
              onClick={onAction}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              {actionLabel}
            </button>
          )}
        </>
      )}
    </div>
  );
}

// Loading error (for when data fails to load)
export function LoadingError({ 
  resource = "data",
  onRetry,
  className = "" 
}: { 
  resource?: string;
  onRetry?: () => void;
  className?: string; 
}) {
  return (
    <ErrorDisplay
      title={`Error Loading ${resource.charAt(0).toUpperCase() + resource.slice(1)}`}
      message={`Unable to load ${resource}. Please try refreshing the page.`}
      onRetry={onRetry}
      retryLabel="Refresh"
      className={className}
    />
  );
}

// Permission error
export function PermissionError({ 
  message = "You don't have permission to access this resource.",
  className = "" 
}: { 
  message?: string;
  className?: string; 
}) {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="text-red-400 text-6xl mb-4">🔒</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        Access Denied
      </h3>
      <p className="text-gray-600 mb-4">
        {message}
      </p>
      <Link
        href="/"
        className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
      >
        Go Home
      </Link>
    </div>
  );
}

// Maintenance mode
export function MaintenanceError({ 
  message = "We're currently performing maintenance. Please try again later.",
  className = "" 
}: { 
  message?: string;
  className?: string; 
}) {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="text-yellow-400 text-6xl mb-4">🔧</div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        Under Maintenance
      </h3>
      <p className="text-gray-600 mb-4">
        {message}
      </p>
      <button
        type="button"
        onClick={() => window.location.reload()}
        className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
      >
        Check Again
      </button>
    </div>
  );
}

// Inline error for form fields
export function InlineError({ 
  message, 
  className = "" 
}: { 
  message: string; 
  className?: string; 
}) {
  return (
    <div 
      className={`flex items-center mt-1 text-sm text-red-600 ${className}`}
      role="alert"
    >
      <svg 
        className="w-4 h-4 mr-1 flex-shrink-0" 
        fill="currentColor" 
        viewBox="0 0 20 20"
        aria-hidden="true"
      >
        <path 
          fillRule="evenodd" 
          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
          clipRule="evenodd" 
        />
      </svg>
      <span>{message}</span>
    </div>
  );
}
