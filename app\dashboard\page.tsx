import { authOptions } from "@/lib/auth";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import Link from "next/link";
import { StatCardSkeleton } from "@/app/components/ui/SkeletonComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces
interface DashboardPageProps {
  searchParams?: {
    tab?: string;
  };
}

// Dashboard card component
function DashboardCard({
  title,
  description,
  href,
  icon,
  className = "",
}: {
  title: string;
  description: string;
  href: string;
  icon: string;
  className?: string;
}) {
  return (
    <Link
      href={href}
      className={`block p-6 bg-white rounded-lg shadow hover:shadow-md transition-shadow border ${className}`}
      aria-label={`Navigate to ${title}`}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="text-3xl">{icon}</div>
      </div>
      <h2 className="text-xl font-semibold mb-2 text-gray-900">{title}</h2>
      <p className="text-gray-600">{description}</p>
    </Link>
  );
}

// Loading component for dashboard
function DashboardLoading() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <div className="h-8 bg-gray-200 rounded w-64 mb-2 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {Array.from({ length: 6 }).map((_, i) => (
          <StatCardSkeleton key={i} />
        ))}
      </div>
    </div>
  );
}

export default async function DashboardPage({
  searchParams,
}: DashboardPageProps) {
  const session = await getServerSession(authOptions);

  if (!session || session.user.role !== "admin") {
    return redirect("/");
  }

  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Admin Dashboard
          </h1>
          <p className="text-gray-600">
            Welcome back, {session.user.name}! Manage your resort and spa
            operations from here.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <DashboardCard
              title="Resort Management"
              description="Manage resort properties and accommodations"
              href="/admin/resorts"
              icon="🏨"
            />

            <DashboardCard
              title="Spa Services"
              description="Manage spa treatments and wellness services"
              href="/admin/spa"
              icon="🧘‍♀️"
            />

            <DashboardCard
              title="Booking Management"
              description="View and manage customer bookings"
              href="/admin/bookings"
              icon="📅"
            />

            <DashboardCard
              title="Review Management"
              description="Manage customer reviews and feedback"
              href="/admin/reviews"
              icon="⭐"
            />

            <DashboardCard
              title="Testimonials"
              description="Manage customer testimonials"
              href="/admin/testimonials"
              icon="💬"
            />

            <DashboardCard
              title="Analytics"
              description="View detailed reports and analytics"
              href="/admin/dashboard"
              icon="📊"
            />
          </div>
        </div>

        {/* System Management */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            System Management
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <DashboardCard
              title="User Management"
              description="Manage user accounts and permissions"
              href="/admin/users"
              icon="👥"
            />

            <DashboardCard
              title="Content Management"
              description="Manage website content and media"
              href="/admin/content"
              icon="📝"
            />

            <DashboardCard
              title="Settings"
              description="Configure system settings"
              href="/admin/settings"
              icon="⚙️"
            />

            <DashboardCard
              title="Logs"
              description="View system logs and activity"
              href="/admin/logs"
              icon="📋"
            />
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Recent Activity
          </h2>
          <div className="bg-white rounded-lg shadow border p-6">
            <p className="text-gray-600 text-center py-8">
              Recent activity will be displayed here. This section can be
              enhanced with real-time data.
            </p>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}
