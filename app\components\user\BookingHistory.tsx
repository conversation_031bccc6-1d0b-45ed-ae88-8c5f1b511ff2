"use client";
import { useRouter } from "next/navigation";
import { toast } from "react-toast";
import { format } from "date-fns";
import { useState } from "react";

interface Resort {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
}

interface SpaTreatment {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
}

interface Room {
  id: string;
  name: string;
  type: string;
  price: number;
  image: string;
  resort: Resort;
}

interface ResortBooking {
  id: string;
  userEmail: string;
  checkIn: string;
  checkOut: string;
  createdAt: string;
  notes?: string;
  status: "pending" | "confirmed" | "cancelled";
  room: Room;
}

interface SpaBooking {
  id: string;
  userEmail: string;
  date: string;
  time: string;
  createdAt: string;
  notes?: string;
  status: "pending" | "confirmed" | "cancelled";
  treatment: SpaTreatment;
}

interface BookingHistoryProps {
  resortBookings: ResortBooking[];
  spaBookings: SpaBooking[];
}

export default function BookingHistory({
  resortBookings,
  spaBookings,
}: BookingHistoryProps) {
  const router = useRouter();
  const [cancellingBookings, setCancellingBookings] = useState<Set<string>>(
    new Set()
  );

  const cancelBooking = async (id: string) => {
    if (cancellingBookings.has(id)) {
      return;
    }

    if (!confirm("Are you sure you want to cancel this booking?")) {
      return;
    }

    setCancellingBookings((prev) => new Set(prev).add(id));

    try {
      const res = await fetch(`/api/bookings/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: "cancelled" }),
      });

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to cancel booking (${res.status})`
        );
      }

      toast.success("Booking cancelled successfully");
      router.refresh();
    } catch (error) {
      console.error("Error cancelling booking:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to cancel booking. Please try again."
      );
    } finally {
      setCancellingBookings((prev) => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Your Bookings</h1>

      {resortBookings && resortBookings.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-3">Resort Bookings</h2>
          <ul className="space-y-4" role="list">
            {resortBookings.map((booking) => (
              <li
                key={booking.id}
                className="border p-4 rounded-md shadow-sm flex justify-between items-center"
              >
                <div className="flex-1">
                  <p className="font-medium">
                    {booking.room?.resort?.name || "Unknown Resort"} –{" "}
                    {booking.room?.name || "Unknown Room"}
                  </p>
                  <p className="text-sm text-gray-600">
                    {format(new Date(booking.checkIn), "PPP")} to{" "}
                    {booking.checkOut
                      ? format(new Date(booking.checkOut), "PPP")
                      : "TBD"}
                  </p>
                  {booking.status && (
                    <p className="text-xs text-gray-500 mt-1">
                      Status:{" "}
                      <span
                        className={`capitalize ${
                          booking.status === "confirmed"
                            ? "text-green-600"
                            : booking.status === "cancelled"
                            ? "text-red-600"
                            : "text-yellow-600"
                        }`}
                      >
                        {booking.status}
                      </span>
                    </p>
                  )}
                </div>
                {booking.status !== "cancelled" && (
                  <button
                    type="button"
                    onClick={() => cancelBooking(booking.id)}
                    disabled={cancellingBookings.has(booking.id)}
                    aria-label={`Cancel booking for ${
                      booking.room?.resort?.name || "resort"
                    }`}
                    className={`text-red-500 hover:underline text-sm px-2 py-1 rounded transition-colors ${
                      cancellingBookings.has(booking.id)
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:bg-red-50"
                    }`}
                  >
                    {cancellingBookings.has(booking.id)
                      ? "Cancelling..."
                      : "Cancel"}
                  </button>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      {spaBookings && spaBookings.length > 0 && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-3">Spa Bookings</h2>
          <ul className="space-y-4" role="list">
            {spaBookings.map((booking) => (
              <li
                key={booking.id}
                className="border p-4 rounded-md shadow-sm flex justify-between items-center"
              >
                <div className="flex-1">
                  <p className="font-medium">
                    {booking.treatment?.name || "Unknown Treatment"}
                  </p>
                  <p className="text-sm text-gray-600">
                    {booking.date
                      ? format(new Date(booking.date), "PPP")
                      : "Date TBD"}
                    {booking.time && ` at ${booking.time}`}
                  </p>
                  {booking.status && (
                    <p className="text-xs text-gray-500 mt-1">
                      Status:{" "}
                      <span
                        className={`capitalize ${
                          booking.status === "confirmed"
                            ? "text-green-600"
                            : booking.status === "cancelled"
                            ? "text-red-600"
                            : "text-yellow-600"
                        }`}
                      >
                        {booking.status}
                      </span>
                    </p>
                  )}
                </div>
                {booking.status !== "cancelled" && (
                  <button
                    type="button"
                    onClick={() => cancelBooking(booking.id)}
                    disabled={cancellingBookings.has(booking.id)}
                    aria-label={`Cancel spa booking for ${
                      booking.treatment?.name || "treatment"
                    }`}
                    className={`text-red-500 hover:underline text-sm px-2 py-1 rounded transition-colors ${
                      cancellingBookings.has(booking.id)
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:bg-red-50"
                    }`}
                  >
                    {cancellingBookings.has(booking.id)
                      ? "Cancelling..."
                      : "Cancel"}
                  </button>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      {(!resortBookings || resortBookings.length === 0) &&
        (!spaBookings || spaBookings.length === 0) && (
          <div className="text-center py-8">
            <p className="text-gray-600 text-lg">You have no bookings yet.</p>
            <p className="text-gray-500 text-sm mt-2">
              Start exploring our resorts and spa treatments to make your first
              booking!
            </p>
          </div>
        )}
    </div>
  );
}
