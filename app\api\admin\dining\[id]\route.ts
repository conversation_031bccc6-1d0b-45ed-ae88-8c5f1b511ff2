import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const data = await req.json();
    
    const diningOption = await prisma.diningOption.update({
      where: { id: params.id },
      data,
    });
    return NextResponse.json(diningOption);
  } catch (error) {
    console.error("Error updating dining option:", error);
    return NextResponse.json(
      { error: "Failed to update dining option" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    await prisma.diningOption.delete({
      where: { id: params.id },
    });
    return NextResponse.json({ message: "Dining option deleted successfully" });
  } catch (error) {
    console.error("Error deleting dining option:", error);
    return NextResponse.json(
      { error: "Failed to delete dining option" },
      { status: 500 }
    );
  }
}

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const diningOption = await prisma.diningOption.findUnique({
      where: { id: params.id },
    });

    if (!diningOption) {
      return NextResponse.json(
        { error: "Dining option not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(diningOption);
  } catch (error) {
    console.error("Error fetching dining option:", error);
    return NextResponse.json(
      { error: "Failed to fetch dining option" },
      { status: 500 }
    );
  }
}
