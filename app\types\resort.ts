/**
 * TypeScript interfaces for Resort-related components and data structures
 * Ensures type safety and consistency across the resort system
 */

// Base resort interface
export interface Resort {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
  createdAt: Date;
  updatedAt?: Date;
}

// Extended resort interface with relationships
export interface ResortWithRelations extends Resort {
  rooms?: Room[];
  bookings?: Booking[];
  reviews?: Review[];
  _count?: {
    rooms: number;
    bookings: number;
    reviews: number;
  };
}

// Room interface
export interface Room {
  id: string;
  resortId: string;
  name: string;
  type: string;
  description: string;
  price: number;
  image: string;
  images: string[];
  amenities: string[];
  capacity: number;
  size?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  resort?: Resort;
  bookings?: Booking[];
  reviews?: Review[];
}

// Booking interface for resorts
export interface Booking {
  id: string;
  userEmail: string;
  resortId?: string;
  roomId?: string;
  checkIn: Date;
  checkOut?: Date;
  notes?: string;
  status: "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";
  totalAmount?: number;
  paymentStatus?: "PENDING" | "PAID" | "REFUNDED";
  createdAt: Date;
  updatedAt: Date;
  resort?: Resort;
  room?: Room;
}

// Review interface
export interface Review {
  id: string;
  userId: string;
  resortId?: string;
  roomId?: string;
  rating: number;
  title?: string;
  comment: string;
  images: string[];
  isPublished: boolean;
  isApproved: boolean;
  createdAt: Date;
  updatedAt: Date;
  user?: {
    id: string;
    name?: string;
    image?: string;
  };
  resort?: Resort;
  room?: Room;
}

// Form interfaces
export interface ResortFormData {
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
}

export interface RoomFormData {
  resortId: string;
  name: string;
  type: string;
  description: string;
  price: number;
  image: string;
  images: string[];
  amenities: string[];
  capacity: number;
  size?: number;
  isActive: boolean;
}

// API response interfaces
export interface ResortListResponse {
  data: Resort[];
  total: number;
  page?: number;
  limit?: number;
}

export interface ResortDetailResponse {
  resort: ResortWithRelations;
  rooms: Room[];
  reviews: Review[];
  stats: {
    totalRooms: number;
    averagePrice: number;
    averageRating: number;
    totalReviews: number;
  };
}

// Filter and search interfaces
export interface ResortFilters {
  location?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  amenities?: string[];
  capacity?: number;
  isActive?: boolean;
  searchTerm?: string;
}

export interface RoomFilters {
  resortId?: string;
  type?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  capacity?: number;
  amenities?: string[];
  isActive?: boolean;
  availability?: {
    checkIn: Date;
    checkOut: Date;
  };
}

// Component prop interfaces
export interface ResortCardProps {
  resort: Resort;
  showLocation?: boolean;
  showDescription?: boolean;
  className?: string;
}

export interface RoomCardProps {
  room: Room;
  showResort?: boolean;
  showBookingButton?: boolean;
  className?: string;
}

export interface ResortFormProps {
  initialData?: Partial<Resort>;
  onSubmit: (data: ResortFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string | null;
}

export interface RoomFormProps {
  resortId: string;
  initialData?: Partial<Room>;
  onSubmit: (data: RoomFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string | null;
}

// Booking-related interfaces
export interface BookingFormData {
  resortId?: string;
  roomId?: string;
  checkIn: Date;
  checkOut?: Date;
  notes?: string;
}

export interface AvailabilityRequest {
  resortId?: string;
  roomId?: string;
  checkIn: Date;
  checkOut?: Date;
}

export interface AvailabilityResponse {
  available: boolean;
  conflictingBookings?: Booking[];
  suggestedDates?: Date[];
  message?: string;
}

// Admin dashboard interfaces
export interface ResortStats {
  totalResorts: number;
  totalRooms: number;
  totalBookings: number;
  totalRevenue: number;
  averageOccupancy: number;
  topPerformingResorts: Array<{
    resort: Resort;
    bookings: number;
    revenue: number;
  }>;
}

// Error handling interfaces
export interface ResortError {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, any>;
}

// Utility types
export type ResortSortField = 'name' | 'location' | 'createdAt' | 'price';
export type SortDirection = 'asc' | 'desc';
export type ResortStatus = 'active' | 'inactive' | 'maintenance';
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed';
export type PaymentStatus = 'pending' | 'paid' | 'refunded';

// Export utility functions type
export interface ResortUtils {
  generateSlug: (name: string) => string;
  calculateAverageRating: (reviews: Review[]) => number;
  formatPrice: (price: number, currency?: string) => string;
  isAvailable: (room: Room, checkIn: Date, checkOut: Date) => boolean;
  getAvailableRooms: (resort: ResortWithRelations, checkIn: Date, checkOut: Date) => Room[];
}
