import OfferForm from "@/app/components/admin/OfferForm";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

export default async function EditOffer({ params }: { params: { id: string } }) {
  const offer = await prisma.specialOffer.findUnique({
    where: { id: params.id },
  });

  if (!offer) return notFound();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Edit Special Offer</h1>
        <p className="text-gray-600 mt-1">
          Update the details for {offer.name}
        </p>
      </div>
      <OfferForm initialData={offer} />
    </div>
  );
}
