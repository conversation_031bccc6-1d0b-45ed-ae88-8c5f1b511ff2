"use client";

import { useEffect, useState } from "react";
import {
  ListItemSkeleton,
  StatCardSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

// TypeScript interfaces for type safety
interface Resort {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
}

interface SpaTreatment {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
}

interface Booking {
  id: string;
  userEmail: string;
  resortId?: string;
  spaId?: string;
  checkIn: string;
  checkOut?: string;
  createdAt: string;
  notes?: string;
  status: "pending" | "confirmed" | "cancelled";
  resort?: Resort;
  spa?: SpaTreatment;
}

interface BookingDashboardState {
  bookings: Booking[];
  loading: boolean;
  error: string | null;
  stats: {
    total: number;
    pending: number;
    confirmed: number;
    cancelled: number;
  };
}

export default function BookingDashboard() {
  const [state, setState] = useState<BookingDashboardState>({
    bookings: [],
    loading: true,
    error: null,
    stats: {
      total: 0,
      pending: 0,
      confirmed: 0,
      cancelled: 0,
    },
  });

  const fetchBookings = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const res = await fetch("/api/bookings/admin");

      if (!res.ok) {
        throw new Error(`Failed to fetch bookings: ${res.status}`);
      }

      const data = await res.json();

      // Calculate stats
      const stats = {
        total: data.length,
        pending: data.filter((b: Booking) => b.status === "pending").length,
        confirmed: data.filter((b: Booking) => b.status === "confirmed").length,
        cancelled: data.filter((b: Booking) => b.status === "cancelled").length,
      };

      setState({
        bookings: data,
        loading: false,
        error: null,
        stats,
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch bookings";
      setState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      console.error("Error fetching bookings:", err);
    }
  };

  useEffect(() => {
    fetchBookings();
  }, []);

  const handleStatusChange = async (id: string, status: string) => {
    try {
      const res = await fetch(`/api/bookings/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!res.ok) {
        throw new Error(`Failed to update booking: ${res.status}`);
      }

      // Update the local state optimistically
      setState((prev) => ({
        ...prev,
        bookings: prev.bookings.map((b) =>
          b.id === id ? { ...b, status: status as Booking["status"] } : b
        ),
      }));

      // Recalculate stats
      fetchBookings();
    } catch (err) {
      console.error("Error updating booking status:", err);
      alert("Failed to update booking status. Please try again.");
    }
  };

  const handleRetry = () => {
    fetchBookings();
  };

  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Booking Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage customer bookings and reservations
            </p>
          </div>

          <button
            type="button"
            onClick={fetchBookings}
            disabled={state.loading}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            aria-label="Refresh booking data"
          >
            {state.loading ? "Refreshing..." : "Refresh"}
          </button>
        </div>

        {/* Loading State */}
        {state.loading && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <StatCardSkeleton key={i} />
              ))}
            </div>
            <div className="space-y-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <ListItemSkeleton key={i} />
              ))}
            </div>
          </div>
        )}

        {/* Error State */}
        {state.error && !state.loading && (
          <LoadingError resource="booking data" onRetry={handleRetry} />
        )}

        {/* Empty State */}
        {!state.loading && !state.error && state.bookings.length === 0 && (
          <EmptyState
            title="No Bookings Found"
            message="No bookings have been made yet."
            icon={<div className="text-gray-400 text-6xl mb-4">📅</div>}
          />
        )}

        {/* Content */}
        {!state.loading && !state.error && state.bookings.length > 0 && (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="text-2xl font-bold text-gray-900">
                  {state.stats.total}
                </div>
                <div className="text-gray-600">Total Bookings</div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="text-2xl font-bold text-yellow-600">
                  {state.stats.pending}
                </div>
                <div className="text-gray-600">Pending</div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="text-2xl font-bold text-green-600">
                  {state.stats.confirmed}
                </div>
                <div className="text-gray-600">Confirmed</div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="text-2xl font-bold text-red-600">
                  {state.stats.cancelled}
                </div>
                <div className="text-gray-600">Cancelled</div>
              </div>
            </div>

            {/* Bookings List */}
            <div className="space-y-4" role="list" aria-label="Bookings list">
              {state.bookings.map((booking) => (
                <div
                  key={booking.id}
                  className="border rounded-lg p-6 bg-white shadow-sm hover:shadow-md transition-shadow"
                  role="listitem"
                >
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-gray-900">
                          {booking.userEmail}
                        </h3>
                        <span
                          className={`px-2 py-1 text-xs font-medium rounded-full ${
                            booking.status === "confirmed"
                              ? "bg-green-100 text-green-800"
                              : booking.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {booking.status}
                        </span>
                      </div>

                      <div className="space-y-1 text-sm text-gray-600">
                        {booking.resort && (
                          <p>
                            <span className="font-medium">Resort:</span>{" "}
                            {booking.resort.name}
                            <span className="text-gray-400 ml-2">
                              ({booking.resort.location})
                            </span>
                          </p>
                        )}
                        {booking.spa && (
                          <p>
                            <span className="font-medium">Spa Treatment:</span>{" "}
                            {booking.spa.name}
                            <span className="text-gray-400 ml-2">
                              ({booking.spa.price} ETB)
                            </span>
                          </p>
                        )}

                        <p>
                          <span className="font-medium">Check-in:</span>{" "}
                          {new Date(booking.checkIn).toLocaleDateString()}
                        </p>

                        {booking.checkOut && (
                          <p>
                            <span className="font-medium">Check-out:</span>{" "}
                            {new Date(booking.checkOut).toLocaleDateString()}
                          </p>
                        )}

                        {booking.notes && (
                          <p>
                            <span className="font-medium">Notes:</span>{" "}
                            {booking.notes}
                          </p>
                        )}

                        <p className="text-xs text-gray-500 mt-2">
                          Booked:{" "}
                          {new Date(booking.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <select
                        value={booking.status}
                        onChange={(e) =>
                          handleStatusChange(booking.id, e.target.value)
                        }
                        className="text-sm border rounded-md px-3 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        aria-label={`Change status for booking ${booking.id}`}
                      >
                        <option value="pending">Pending</option>
                        <option value="confirmed">Confirmed</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </ErrorBoundary>
  );
}
