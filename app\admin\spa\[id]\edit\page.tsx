import SpaForm from "@/app/components/admin/SpaForm";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

export default async function EditSpa({ params }: { params: { id: string } }) {
  const treatment = await prisma.spaTreatment.findUnique({
    where: { id: params.id },
  });
  if (!treatment) return notFound();

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Edit Spa Treatment</h1>
      <SpaForm initialData={treatment} />
    </div>
  );
}
