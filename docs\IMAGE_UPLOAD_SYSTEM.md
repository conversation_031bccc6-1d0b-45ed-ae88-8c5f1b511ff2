# Image Upload System Documentation

## Overview

The Kuriftu Water Park image upload system provides a secure, user-friendly way to handle image uploads with comprehensive validation, optimization, and management features.

## Architecture

### Backend Components

#### 1. Upload API (`/api/upload/route.ts`)
- **Security**: Role-based access control (admin/manager only)
- **Validation**: File type, size, dimensions, and content scanning
- **Storage**: Cloudinary integration with automatic optimization
- **Audit**: Comprehensive logging of all upload activities

#### 2. Security Layer (`lib/security.ts`)
- Client-side file validation functions
- Image dimension validation
- File type and size checking
- Malicious content detection

#### 3. Image Utilities (`lib/imageUtils.ts`)
- URL optimization for different screen sizes
- Image compression and thumbnail generation
- Responsive image URL generation
- Public ID extraction from Cloudinary URLs

### Frontend Components

#### 1. ImageUpload Component (`app/components/ui/ImageUpload.tsx`)
- Drag-and-drop file upload
- Real-time preview with edit/remove options
- Progress indicators and error handling
- Accessibility features (ARIA labels, keyboard navigation)

#### 2. MultipleImageUpload Component (`app/components/ui/MultipleImageUpload.tsx`)
- Bulk image upload with queue management
- Grid-based image management
- Individual image removal
- Upload progress tracking

#### 3. ImageGallery Component (`app/components/ui/ImageGallery.tsx`)
- Responsive image display
- Fullscreen viewing with navigation
- Thumbnail navigation
- Keyboard shortcuts support

#### 4. Custom Hook (`app/hooks/useImageUpload.ts`)
- Reusable upload logic
- State management for upload process
- Error handling and validation
- Progress tracking

## Features

### Security Features
- **File Validation**: Type, size, and dimension checking
- **Content Scanning**: Magic number validation for file headers
- **Role-Based Access**: Only admin and manager roles can upload
- **Audit Logging**: All upload activities are logged
- **CSRF Protection**: Custom headers for API security
- **Rate Limiting**: Prevents abuse of upload endpoints

### User Experience Features
- **Drag & Drop**: Intuitive file selection
- **Real-time Preview**: Immediate visual feedback
- **Progress Indicators**: Upload progress with percentage
- **Error Handling**: Clear error messages and recovery options
- **Responsive Design**: Works on all device sizes
- **Accessibility**: Screen reader support and keyboard navigation

### Performance Features
- **Image Optimization**: Automatic compression and format conversion
- **Responsive Images**: Multiple sizes for different screen resolutions
- **Lazy Loading**: Images load only when needed
- **CDN Delivery**: Fast global content delivery via Cloudinary

## Usage Examples

### Single Image Upload

```tsx
import ImageUpload from '@/app/components/ui/ImageUpload';

function ResortForm() {
  const [image, setImage] = useState('');

  return (
    <ImageUpload
      value={image}
      onChange={setImage}
      uploadType="resort"
      placeholder="Upload resort image"
      required
    />
  );
}
```

### Multiple Image Upload

```tsx
import MultipleImageUpload from '@/app/components/ui/MultipleImageUpload';

function GalleryForm() {
  const [images, setImages] = useState<string[]>([]);

  return (
    <MultipleImageUpload
      value={images}
      onChange={setImages}
      uploadType="gallery"
      maxImages={10}
    />
  );
}
```

### Image Gallery Display

```tsx
import ImageGallery from '@/app/components/ui/ImageGallery';

function ResortDetail({ resort }) {
  return (
    <ImageGallery
      images={resort.images}
      allowFullscreen
      showThumbnails
      aspectRatio="landscape"
    />
  );
}
```

### Using the Upload Hook

```tsx
import { useImageUpload } from '@/app/hooks/useImageUpload';

function CustomUploadComponent() {
  const { uploadState, uploadImage, resetUpload } = useImageUpload({
    uploadType: 'custom',
    onUploadComplete: (image) => {
      console.log('Upload completed:', image);
    },
    onUploadError: (error) => {
      console.error('Upload failed:', error);
    },
  });

  const handleFileSelect = async (file: File) => {
    await uploadImage(file);
  };

  return (
    <div>
      {uploadState.isUploading && (
        <div>Uploading... {uploadState.progress}%</div>
      )}
      {uploadState.error && (
        <div>Error: {uploadState.error}</div>
      )}
      {uploadState.uploadedImage && (
        <img src={uploadState.uploadedImage.secure_url} alt="Uploaded" />
      )}
    </div>
  );
}
```

## Configuration

### Environment Variables

```env
# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
CLOUDINARY_WEBHOOK_URL=https://your-domain.com/api/webhooks/cloudinary

# Next.js Public Variables
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
```

### Upload Limits

```typescript
const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedMimeTypes: [
    "image/jpeg",
    "image/jpg", 
    "image/png",
    "image/webp",
    "image/gif",
  ],
  allowedExtensions: [".jpg", ".jpeg", ".png", ".webp", ".gif"],
  maxDimensions: {
    width: 4096,
    height: 4096,
  },
};
```

## Best Practices

### For Developers

1. **Always validate files on both client and server side**
2. **Use the provided hooks and components for consistency**
3. **Implement proper error handling and user feedback**
4. **Optimize images for the intended use case**
5. **Use appropriate upload types for organization**

### For Content Managers

1. **Upload high-quality images (minimum 1200px width for main images)**
2. **Use descriptive filenames before uploading**
3. **Compress large images before upload when possible**
4. **Choose appropriate aspect ratios for different content types**
5. **Test images on different devices after upload**

## Troubleshooting

### Common Issues

1. **Upload fails with "File too large"**
   - Solution: Compress image or reduce dimensions before upload

2. **Upload fails with "Invalid file type"**
   - Solution: Ensure file is in supported format (JPG, PNG, WebP, GIF)

3. **Images not displaying properly**
   - Solution: Check Cloudinary configuration and public ID extraction

4. **Slow upload speeds**
   - Solution: Compress images or check network connection

### Error Codes

- `400`: Invalid file or validation error
- `401`: Unauthorized (insufficient permissions)
- `413`: File too large
- `415`: Unsupported media type
- `500`: Server error (check logs)

## Future Enhancements

### Planned Features
- [ ] Image cropping and editing tools
- [ ] Batch upload with progress tracking
- [ ] Image metadata extraction and tagging
- [ ] Advanced image filters and effects
- [ ] Integration with AI-powered image analysis
- [ ] Automatic alt text generation
- [ ] Image versioning and rollback
- [ ] Advanced compression algorithms

### Performance Improvements
- [ ] WebP format optimization
- [ ] Progressive image loading
- [ ] Image caching strategies
- [ ] Background upload processing
- [ ] Thumbnail generation optimization

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.
