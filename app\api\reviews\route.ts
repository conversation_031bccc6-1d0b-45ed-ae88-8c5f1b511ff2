import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { logAuditEvent } from "@/lib/security";
import { ReviewFilters } from "@/app/types/review";

// GET /api/reviews - Fetch reviews with filtering and pagination
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    
    // Parse query parameters
    const filters: ReviewFilters = {
      rating: searchParams.get('rating') ? parseInt(searchParams.get('rating')!) : undefined,
      minRating: searchParams.get('minRating') ? parseInt(searchParams.get('minRating')!) : undefined,
      isVerified: searchParams.get('isVerified') === 'true',
      contentType: searchParams.get('contentType') as any,
      contentId: searchParams.get('contentId') || undefined,
      userId: searchParams.get('userId') || undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'newest',
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10'),
    };

    // Build where clause
    const where: any = {
      isPublished: true, // Only show published reviews
    };

    if (filters.rating) {
      where.rating = filters.rating;
    }

    if (filters.minRating) {
      where.rating = { gte: filters.minRating };
    }

    if (filters.isVerified) {
      where.isVerified = true;
    }

    if (filters.contentType && filters.contentId) {
      switch (filters.contentType) {
        case 'resort':
          where.resortId = filters.contentId;
          break;
        case 'room':
          where.roomId = filters.contentId;
          break;
        case 'spa':
          where.spaId = filters.contentId;
          break;
        case 'experience':
          where.experienceId = filters.contentId;
          break;
        case 'wellness':
          where.wellnessId = filters.contentId;
          break;
        case 'event':
          where.eventId = filters.contentId;
          break;
      }
    }

    if (filters.userId) {
      where.userId = filters.userId;
    }

    // Build orderBy clause
    let orderBy: any = { createdAt: 'desc' }; // Default to newest
    
    switch (filters.sortBy) {
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'rating_high':
        orderBy = { rating: 'desc' };
        break;
      case 'rating_low':
        orderBy = { rating: 'asc' };
        break;
      case 'helpful':
        orderBy = { helpfulVotes: 'desc' };
        break;
    }

    // Calculate pagination
    const skip = (filters.page! - 1) * filters.limit!;

    // Fetch reviews with relations
    const [reviews, total] = await Promise.all([
      prisma.review.findMany({
        where,
        orderBy,
        skip,
        take: filters.limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          resort: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          room: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          spaTreatment: {
            select: {
              id: true,
              name: true,
            },
          },
          experience: {
            select: {
              id: true,
              name: true,
            },
          },
          wellnessService: {
            select: {
              id: true,
              name: true,
            },
          },
          event: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
      prisma.review.count({ where }),
    ]);

    // Calculate average rating and distribution if filtering by content
    let averageRating: number | undefined;
    let ratingDistribution: any | undefined;

    if (filters.contentType && filters.contentId) {
      const ratingStats = await prisma.review.groupBy({
        by: ['rating'],
        where: {
          ...where,
          isPublished: true,
        },
        _count: {
          rating: true,
        },
      });

      const totalRatings = ratingStats.reduce((sum, stat) => sum + stat._count.rating, 0);
      const weightedSum = ratingStats.reduce((sum, stat) => sum + (stat.rating * stat._count.rating), 0);
      
      averageRating = totalRatings > 0 ? weightedSum / totalRatings : 0;
      
      ratingDistribution = {
        1: 0, 2: 0, 3: 0, 4: 0, 5: 0,
      };
      
      ratingStats.forEach(stat => {
        ratingDistribution[stat.rating] = stat._count.rating;
      });
    }

    return NextResponse.json({
      reviews,
      total,
      page: filters.page,
      limit: filters.limit,
      averageRating,
      ratingDistribution,
    });

  } catch (error) {
    console.error("Error fetching reviews:", error);
    return NextResponse.json(
      { error: "Failed to fetch reviews" },
      { status: 500 }
    );
  }
}

// POST /api/reviews - Create a new review
export async function POST(req: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      await logAuditEvent({
        action: "UNAUTHORIZED_REVIEW_CREATION",
        resource: "/api/reviews",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "No authentication session",
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await req.json();

    // Validate required fields
    if (!data.rating || !data.comment) {
      return NextResponse.json(
        { error: "Rating and comment are required" },
        { status: 400 }
      );
    }

    if (data.rating < 1 || data.rating > 5) {
      return NextResponse.json(
        { error: "Rating must be between 1 and 5" },
        { status: 400 }
      );
    }

    // Validate that exactly one content type is provided
    const contentTypes = ['resortId', 'roomId', 'spaId', 'experienceId', 'wellnessId', 'eventId'];
    const providedTypes = contentTypes.filter(type => data[type]);
    
    if (providedTypes.length !== 1) {
      return NextResponse.json(
        { error: "Exactly one content type must be provided" },
        { status: 400 }
      );
    }

    // Get user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user already reviewed this content
    const existingReview = await prisma.review.findFirst({
      where: {
        userId: user.id,
        ...Object.fromEntries(
          contentTypes.map(type => [type, data[type] || null])
        ),
      },
    });

    if (existingReview) {
      return NextResponse.json(
        { error: "You have already reviewed this item" },
        { status: 400 }
      );
    }

    // Check if user has a booking for this content (for verification)
    const hasBooking = await prisma.booking.findFirst({
      where: {
        userEmail: session.user.email,
        status: 'CONFIRMED',
        ...Object.fromEntries(
          contentTypes.map(type => [type, data[type] || null])
        ),
      },
    });

    // Create the review
    const review = await prisma.review.create({
      data: {
        userId: user.id,
        rating: data.rating,
        title: data.title || null,
        comment: data.comment,
        images: data.images || [],
        isVerified: !!hasBooking,
        isApproved: false, // Requires admin approval
        isPublished: false, // Will be published after approval
        resortId: data.resortId || null,
        roomId: data.roomId || null,
        spaId: data.spaId || null,
        experienceId: data.experienceId || null,
        wellnessId: data.wellnessId || null,
        eventId: data.eventId || null,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    await logAuditEvent({
      action: "REVIEW_CREATED",
      resource: `review_${review.id}`,
      userId: user.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json(review, { status: 201 });

  } catch (error) {
    console.error("Error creating review:", error);
    return NextResponse.json(
      { error: "Failed to create review" },
      { status: 500 }
    );
  }
}
