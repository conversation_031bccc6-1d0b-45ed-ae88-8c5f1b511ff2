import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  const data = await req.json();

  const updated = await prisma.resort.update({
    where: { id: params.id },
    data,
  });

  return NextResponse.json(updated);
}

export async function DELETE(
  _req: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.email)
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });

  const spaBooking = await prisma.spaBooking.findUnique({
    where: { id: params.id },
  });
  if (!spaBooking)
    return NextResponse.json({ error: "Not found" }, { status: 404 });

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
  });
  if (spaBooking.userId !== user?.id)
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });

  await prisma.spaBooking.delete({ where: { id: params.id } });

  return NextResponse.json({ success: true });
}
