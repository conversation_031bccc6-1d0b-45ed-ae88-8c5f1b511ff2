/**
 * Image utility functions for optimization and processing
 */

export interface ImageOptimizationOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'auto' | 'jpg' | 'png' | 'webp';
  crop?: 'fill' | 'fit' | 'limit' | 'scale';
}

/**
 * Generate optimized Cloudinary URL with transformations
 */
export function getOptimizedImageUrl(
  publicId: string,
  options: ImageOptimizationOptions = {}
): string {
  const {
    width,
    height,
    quality = 'auto:good',
    format = 'auto',
    crop = 'limit'
  } = options;

  const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
  if (!cloudName) {
    console.warn('NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME not set, returning original URL');
    return publicId;
  }

  const transformations: string[] = [];

  // Add quality transformation
  transformations.push(`q_${quality}`);

  // Add format transformation
  transformations.push(`f_${format}`);

  // Add dimension transformations
  if (width || height) {
    const dimensions: string[] = [];
    if (width) dimensions.push(`w_${width}`);
    if (height) dimensions.push(`h_${height}`);
    dimensions.push(`c_${crop}`);
    transformations.push(dimensions.join(','));
  }

  const transformationString = transformations.join('/');
  return `https://res.cloudinary.com/${cloudName}/image/upload/${transformationString}/${publicId}`;
}

/**
 * Generate responsive image URLs for different screen sizes
 */
export function getResponsiveImageUrls(publicId: string): {
  mobile: string;
  tablet: string;
  desktop: string;
  original: string;
} {
  return {
    mobile: getOptimizedImageUrl(publicId, { width: 480, quality: 'auto:low' }),
    tablet: getOptimizedImageUrl(publicId, { width: 768, quality: 'auto:good' }),
    desktop: getOptimizedImageUrl(publicId, { width: 1200, quality: 'auto:good' }),
    original: getOptimizedImageUrl(publicId, { quality: 'auto:best' }),
  };
}

/**
 * Extract public ID from Cloudinary URL
 */
export function extractPublicIdFromUrl(url: string): string | null {
  try {
    const urlParts = url.split('/');
    const uploadIndex = urlParts.findIndex(part => part === 'upload');
    
    if (uploadIndex === -1) return null;
    
    // Find the part after transformations (if any)
    let publicIdIndex = uploadIndex + 1;
    
    // Skip transformation parameters
    while (publicIdIndex < urlParts.length && urlParts[publicIdIndex].includes('_')) {
      publicIdIndex++;
    }
    
    if (publicIdIndex >= urlParts.length) return null;
    
    // Join remaining parts and remove file extension
    const publicIdWithExtension = urlParts.slice(publicIdIndex).join('/');
    const lastDotIndex = publicIdWithExtension.lastIndexOf('.');
    
    return lastDotIndex > 0 
      ? publicIdWithExtension.substring(0, lastDotIndex)
      : publicIdWithExtension;
  } catch (error) {
    console.error('Error extracting public ID from URL:', error);
    return null;
  }
}

/**
 * Validate image dimensions from file
 */
export function validateImageDimensions(file: File): Promise<{
  width: number;
  height: number;
  isValid: boolean;
  error?: string;
}> {
  return new Promise((resolve) => {
    const img = new Image();
    
    img.onload = () => {
      const { width, height } = img;
      
      // Clean up object URL
      URL.revokeObjectURL(img.src);
      
      // Validate dimensions
      const maxWidth = 4096;
      const maxHeight = 4096;
      const minWidth = 100;
      const minHeight = 100;
      
      if (width < minWidth || height < minHeight) {
        resolve({
          width,
          height,
          isValid: false,
          error: `Image dimensions ${width}x${height} are too small. Minimum size is ${minWidth}x${minHeight}`,
        });
        return;
      }
      
      if (width > maxWidth || height > maxHeight) {
        resolve({
          width,
          height,
          isValid: false,
          error: `Image dimensions ${width}x${height} exceed maximum allowed ${maxWidth}x${maxHeight}`,
        });
        return;
      }
      
      resolve({
        width,
        height,
        isValid: true,
      });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      resolve({
        width: 0,
        height: 0,
        isValid: false,
        error: 'Invalid image file or corrupted data',
      });
    };
    
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Compress image file before upload
 */
export function compressImage(
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }
      
      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to compress image'));
            return;
          }
          
          // Create new file with compressed data
          const compressedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now(),
          });
          
          URL.revokeObjectURL(img.src);
          resolve(compressedFile);
        },
        file.type,
        quality
      );
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      reject(new Error('Failed to load image for compression'));
    };
    
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Generate thumbnail from image file
 */
export function generateThumbnail(
  file: File,
  size: number = 150
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }
    
    img.onload = () => {
      // Set canvas to square thumbnail size
      canvas.width = size;
      canvas.height = size;
      
      // Calculate crop dimensions for square thumbnail
      const { width, height } = img;
      const minDimension = Math.min(width, height);
      const x = (width - minDimension) / 2;
      const y = (height - minDimension) / 2;
      
      // Draw cropped and scaled image
      ctx.drawImage(
        img,
        x, y, minDimension, minDimension,
        0, 0, size, size
      );
      
      const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.7);
      URL.revokeObjectURL(img.src);
      resolve(thumbnailDataUrl);
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(img.src);
      reject(new Error('Failed to generate thumbnail'));
    };
    
    img.src = URL.createObjectURL(file);
  });
}
