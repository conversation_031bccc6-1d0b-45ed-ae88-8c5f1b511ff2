import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { logAuditEvent } from "@/lib/security";
import { TestimonialFilters } from "@/app/types/review";

// GET /api/testimonials - Fetch testimonials with filtering
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    
    // Parse query parameters
    const filters: TestimonialFilters = {
      isFeatured: searchParams.get('isFeatured') === 'true',
      isApproved: searchParams.get('isApproved') === 'true',
      isPublished: searchParams.get('isPublished') !== 'false', // Default to true
      sortBy: (searchParams.get('sortBy') as any) || 'featured',
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10'),
    };

    // Build where clause
    const where: any = {};

    if (filters.isFeatured !== undefined) {
      where.isFeatured = filters.isFeatured;
    }

    if (filters.isApproved !== undefined) {
      where.isApproved = filters.isApproved;
    }

    if (filters.isPublished !== undefined) {
      where.isPublished = filters.isPublished;
    }

    // Build orderBy clause
    let orderBy: any = [
      { isFeatured: 'desc' },
      { displayOrder: 'asc' },
      { createdAt: 'desc' }
    ];
    
    switch (filters.sortBy) {
      case 'newest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'oldest':
        orderBy = { createdAt: 'asc' };
        break;
      case 'order':
        orderBy = [
          { displayOrder: 'asc' },
          { createdAt: 'desc' }
        ];
        break;
    }

    // Calculate pagination
    const skip = (filters.page! - 1) * filters.limit!;

    // Fetch testimonials with relations
    const [testimonials, total] = await Promise.all([
      prisma.testimonial.findMany({
        where,
        orderBy,
        skip,
        take: filters.limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      }),
      prisma.testimonial.count({ where }),
    ]);

    return NextResponse.json({
      testimonials,
      total,
      page: filters.page,
      limit: filters.limit,
    });

  } catch (error) {
    console.error("Error fetching testimonials:", error);
    return NextResponse.json(
      { error: "Failed to fetch testimonials" },
      { status: 500 }
    );
  }
}

// POST /api/testimonials - Create a new testimonial
export async function POST(req: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      await logAuditEvent({
        action: "UNAUTHORIZED_TESTIMONIAL_CREATION",
        resource: "/api/testimonials",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "No authentication session",
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.content) {
      return NextResponse.json(
        { error: "Name and content are required" },
        { status: 400 }
      );
    }

    // Get user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Create the testimonial
    const testimonial = await prisma.testimonial.create({
      data: {
        userId: user.id,
        name: data.name,
        title: data.title || null,
        content: data.content,
        image: data.image || null,
        location: data.location || null,
        isFeatured: false, // Admin sets this
        isApproved: false, // Requires admin approval
        isPublished: false, // Will be published after approval
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    await logAuditEvent({
      action: "TESTIMONIAL_CREATED",
      resource: `testimonial_${testimonial.id}`,
      userId: user.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json(testimonial, { status: 201 });

  } catch (error) {
    console.error("Error creating testimonial:", error);
    return NextResponse.json(
      { error: "Failed to create testimonial" },
      { status: 500 }
    );
  }
}
