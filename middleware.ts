import { getToken } from "next-auth/jwt";
import { NextRequest, NextResponse } from "next/server";
import {
  rateLimit,
  validateCSRF,
  getSecurityHeaders,
  logAuditEvent,
} from "@/lib/security";

export async function middleware(req: NextRequest) {
  const response = NextResponse.next();
  const pathname = new URL(req.url).pathname;

  // Add security headers to all responses
  const securityHeaders = getSecurityHeaders();
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // Rate limiting for API routes
  if (pathname.startsWith("/api/")) {
    if (!rateLimit(req)) {
      await logAuditEvent({
        action: "RATE_LIMIT_EXCEEDED",
        resource: pathname,
        ip: req.ip || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Rate limit exceeded",
      });
      return NextResponse.json({ error: "Too many requests" }, { status: 429 });
    }
  }

  // CSRF protection for state-changing operations
  if (
    ["POST", "PUT", "PATCH", "DELETE"].includes(req.method) &&
    pathname.startsWith("/api/")
  ) {
    if (!validateCSRF(req)) {
      await logAuditEvent({
        action: "CSRF_VALIDATION_FAILED",
        resource: pathname,
        ip: req.ip || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "CSRF validation failed",
      });
      return NextResponse.json(
        { error: "Invalid request origin" },
        { status: 403 }
      );
    }
  }

  // Role-based access control for protected routes
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  const userRole = token?.role?.toLowerCase();

  if (pathname.startsWith("/admin")) {
    if (!token || userRole !== "admin") {
      await logAuditEvent({
        userId: token?.sub,
        action: "UNAUTHORIZED_ACCESS_ATTEMPT",
        resource: pathname,
        ip: req.ip || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Insufficient permissions for admin area",
      });
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }
  }

  if (pathname.startsWith("/manager")) {
    if (!token || userRole !== "manager") {
      await logAuditEvent({
        userId: token?.sub,
        action: "UNAUTHORIZED_ACCESS_ATTEMPT",
        resource: pathname,
        ip: req.ip || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Insufficient permissions for manager area",
      });
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }
  }

  if (pathname.startsWith("/reception")) {
    if (!token || userRole !== "receptionist") {
      await logAuditEvent({
        userId: token?.sub,
        action: "UNAUTHORIZED_ACCESS_ATTEMPT",
        resource: pathname,
        ip: req.ip || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "Insufficient permissions for reception area",
      });
      return NextResponse.redirect(new URL("/unauthorized", req.url));
    }
  }

  return response;
}

export const config = {
  matcher: [
    // Protected routes
    "/admin/:path*",
    "/manager/:path*",
    "/reception/:path*",
    // API routes for security headers and rate limiting
    "/api/:path*",
    // Dashboard routes
    "/dashboard/:path*",
  ],
};
