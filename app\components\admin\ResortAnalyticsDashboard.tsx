"use client";

import { useState, useEffect } from "react";

interface AnalyticsData {
  overview: {
    totalResorts: number;
    totalRooms: number;
    activeRooms: number;
    totalBookings: number;
    totalRevenue: number;
    averageOccupancy: number;
    averageRating: number;
    totalReviews: number;
  };
  growth: {
    bookingsThisMonth: number;
    bookingsLastMonth: number;
    bookingGrowthRate: number;
  };
  topPerformingResorts: Array<{
    resort: {
      id: string;
      name: string;
      location: string;
    };
    bookings: number;
    revenue: number;
  }>;
  bookingTrends: Array<{
    date: string;
    bookings: number;
  }>;
  lastUpdated: string;
}

// Stat Card Component
function StatCard({
  title,
  value,
  subtitle,
  trend,
  icon,
  color = "blue",
}: {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  icon: React.ReactNode;
  color?: "blue" | "green" | "purple" | "orange" | "red";
}) {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-600 border-blue-200",
    green: "bg-green-50 text-green-600 border-green-200",
    purple: "bg-purple-50 text-purple-600 border-purple-200",
    orange: "bg-orange-50 text-orange-600 border-orange-200",
    red: "bg-red-50 text-red-600 border-red-200",
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
          {trend && (
            <div
              className={`flex items-center mt-2 text-sm ${
                trend.isPositive ? "text-green-600" : "text-red-600"
              }`}
            >
              <span className="mr-1">{trend.isPositive ? "↗" : "↘"}</span>
              {Math.abs(trend.value)}%
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg border ${colorClasses[color]}`}>
          {icon}
        </div>
      </div>
    </div>
  );
}

// Simple Chart Component
function BookingTrendChart({
  data,
}: {
  data: Array<{ date: string; bookings: number }>;
}) {
  const maxBookings = Math.max(...data.map((d) => d.bookings));

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Booking Trends (Last 7 Days)
      </h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="w-16 text-sm text-gray-600">
              {new Date(item.date).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
              })}
            </div>
            <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
              <div
                className="bg-blue-600 h-4 rounded-full transition-all duration-300"
                style={{
                  width:
                    maxBookings > 0
                      ? `${(item.bookings / maxBookings) * 100}%`
                      : "0%",
                }}
              ></div>
            </div>
            <div className="w-8 text-sm font-medium text-gray-900">
              {item.bookings}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Top Resorts Component
function TopResortsTable({
  resorts,
}: {
  resorts: AnalyticsData["topPerformingResorts"];
}) {
  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Top Performing Resorts (Last 30 Days)
      </h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Resort
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Bookings
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Revenue
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {resorts.map((item, index) => (
              <tr key={item.resort.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-medium text-sm">
                        #{index + 1}
                      </span>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {item.resort.name}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {item.resort.location}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {item.bookings}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ${item.revenue.toLocaleString()}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default function ResortAnalyticsDashboard() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/admin/analytics/resorts");
        if (response.ok) {
          const analyticsData = await response.json();
          setData(analyticsData);
          setError(null);
        } else {
          throw new Error("Failed to fetch analytics");
        }
      } catch (err) {
        console.error("Error fetching analytics:", err);
        setError("Failed to load analytics data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();

    // Refresh data every 5 minutes
    const interval = setInterval(fetchAnalytics, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div
              key={i}
              className="bg-white rounded-lg shadow-sm border p-6 animate-pulse"
            >
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 text-4xl mb-4">⚠️</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Error Loading Analytics
        </h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Resorts"
          value={data.overview.totalResorts}
          icon={<span className="text-xl">🏨</span>}
          color="blue"
        />
        <StatCard
          title="Total Bookings"
          value={data.overview.totalBookings}
          subtitle="All time"
          trend={{
            value: data.growth.bookingGrowthRate,
            isPositive: data.growth.bookingGrowthRate >= 0,
          }}
          icon={<span className="text-xl">📅</span>}
          color="green"
        />
        <StatCard
          title="Total Revenue"
          value={`$${data.overview.totalRevenue.toLocaleString()}`}
          subtitle="From confirmed bookings"
          icon={<span className="text-xl">💰</span>}
          color="purple"
        />
        <StatCard
          title="Occupancy Rate"
          value={`${data.overview.averageOccupancy}%`}
          subtitle="Last 30 days"
          icon={<span className="text-xl">📊</span>}
          color="orange"
        />
      </div>

      {/* Secondary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Active Rooms"
          value={`${data.overview.activeRooms}/${data.overview.totalRooms}`}
          subtitle="Available rooms"
          icon={<span className="text-xl">🛏️</span>}
          color="blue"
        />
        <StatCard
          title="This Month"
          value={data.growth.bookingsThisMonth}
          subtitle="Bookings"
          icon={<span className="text-xl">📈</span>}
          color="green"
        />
        <StatCard
          title="Average Rating"
          value={data.overview.averageRating}
          subtitle={`From ${data.overview.totalReviews} reviews`}
          icon={<span className="text-xl">⭐</span>}
          color="orange"
        />
        <StatCard
          title="Last Updated"
          value={new Date(data.lastUpdated).toLocaleTimeString()}
          subtitle={new Date(data.lastUpdated).toLocaleDateString()}
          icon={<span className="text-xl">🔄</span>}
          color="purple"
        />
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <BookingTrendChart data={data.bookingTrends} />
        <TopResortsTable resorts={data.topPerformingResorts} />
      </div>
    </div>
  );
}
