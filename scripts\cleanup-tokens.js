import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
dotenv.config();

const prisma = new PrismaClient();

async function cleanupExpiredTokens() {
  console.log("🧹 Starting cleanup of expired verification tokens...");

  try {
    // Delete expired tokens
    const result = await prisma.verificationToken.deleteMany({
      where: {
        expires: {
          lt: new Date(),
        },
      },
    });

    console.log(`✅ Cleaned up ${result.count} expired verification tokens`);

    // Also clean up old used tokens (older than 7 days)
    const oldUsedTokens = await prisma.verificationToken.deleteMany({
      where: {
        used: true,
        createdAt: {
          lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        },
      },
    });

    console.log(`✅ Cleaned up ${oldUsedTokens.count} old used tokens`);

    // Log statistics
    const remainingTokens = await prisma.verificationToken.count();
    console.log(`📊 Remaining tokens in database: ${remainingTokens}`);

    const tokensByType = await prisma.verificationToken.groupBy({
      by: ["type"],
      _count: {
        id: true,
      },
    });

    console.log("📊 Tokens by type:");
    tokensByType.forEach((group) => {
      console.log(`  - ${group.type}: ${group._count.id}`);
    });
  } catch (error) {
    console.error("❌ Error during token cleanup:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
  cleanupExpiredTokens()
    .then(() => {
      console.log("🎉 Token cleanup completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Token cleanup failed:", error);
      process.exit(1);
    });
}

module.exports = { cleanupExpiredTokens };
