"use client";

import React, { Component, ReactNode } from "react";
import { DashboardError, ErrorBoundaryState } from "./types";

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: DashboardError) => void;
  showDetails?: boolean;
  className?: string;
}

export default class ErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    const dashboardError: DashboardError = {
      code: "DASHBOARD_ERROR",
      message: error.message || "An unexpected error occurred",
      details: error.stack,
      timestamp: new Date().toISOString(),
      recoverable: true,
    };

    return {
      hasError: true,
      error: dashboardError,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const dashboardError: DashboardError = {
      code: "DASHBOARD_ERROR",
      message: error.message || "An unexpected error occurred",
      details: `${error.stack}\n\nComponent Stack:\n${errorInfo.componentStack}`,
      timestamp: new Date().toISOString(),
      recoverable: true,
    };

    // Log error to console for development
    console.error(
      "Dashboard Error Boundary caught an error:",
      error,
      errorInfo
    );

    // Call onError callback if provided
    if (this.props.onError) {
      this.props.onError(dashboardError);
    }

    // Update state with error details
    this.setState({
      hasError: true,
      error: dashboardError,
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div
          className={`min-h-[400px] flex items-center justify-center p-6 ${
            this.props.className || ""
          }`}
          role="alert"
          aria-live="assertive"
        >
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg border border-red-200 p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <svg
                  className="w-8 h-8 text-red-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">
                  Something went wrong
                </h3>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600">
                {this.state.error?.message ||
                  "An unexpected error occurred while loading the dashboard."}
              </p>

              {this.props.showDetails && this.state.error?.details && (
                <details className="mt-3">
                  <summary className="text-sm text-gray-500 cursor-pointer hover:text-gray-700">
                    Technical Details
                  </summary>
                  <pre className="mt-2 text-xs text-gray-500 bg-gray-50 p-2 rounded overflow-auto max-h-32">
                    {this.state.error.details}
                  </pre>
                </details>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={this.handleRetry}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                aria-label="Try again"
              >
                Try Again
              </button>
              <button
                type="button"
                onClick={this.handleReload}
                className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                aria-label="Reload page"
              >
                Reload Page
              </button>
            </div>

            {this.state.error?.timestamp && (
              <p className="mt-3 text-xs text-gray-400 text-center">
                Error occurred at{" "}
                {new Date(this.state.error.timestamp).toLocaleString()}
              </p>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook for handling async errors in functional components
 */
export function useErrorHandler() {
  const [error, setError] = React.useState<DashboardError | null>(null);

  const handleError = React.useCallback((error: Error | DashboardError) => {
    if ("code" in error) {
      setError(error);
    } else {
      setError({
        code: "ASYNC_ERROR",
        message: error.message || "An unexpected error occurred",
        details: error.stack,
        timestamp: new Date().toISOString(),
        recoverable: true,
      });
    }
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return { error, handleError, clearError };
}

export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, "children">
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${
    Component.displayName || Component.name
  })`;

  return WrappedComponent;
}
