import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { Suspense } from "react";

interface Room {
  id: string;
  name: string;
  type: string;
  description: string;
  price: number;
  image: string;
  images: string[];
  amenities: string[];
  capacity: number;
  size?: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Room card component
function RoomCard({ room, resortId }: { room: Room; resortId: string }) {
  return (
    <div className="bg-white rounded-xl shadow-sm border overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative h-48">
        <Image
          src={room.image}
          alt={room.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        <div className="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-sm font-semibold">
          ${room.price}/night
        </div>
        <div className={`absolute top-2 left-2 px-2 py-1 rounded text-xs font-medium ${
          room.isActive 
            ? "bg-green-100 text-green-800" 
            : "bg-red-100 text-red-800"
        }`}>
          {room.isActive ? "Active" : "Inactive"}
        </div>
      </div>
      
      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold text-gray-900">{room.name}</h3>
          <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {room.type}
          </span>
        </div>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {room.description}
        </p>
        
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <span>👥 {room.capacity} guests</span>
          {room.size && <span>📐 {room.size}m²</span>}
          <span>🛏️ {room.amenities.length} amenities</span>
        </div>

        <div className="flex justify-between items-center pt-3 border-t border-gray-200">
          <div className="flex space-x-2">
            <Link
              href={`/admin/resorts/${resortId}/rooms/${room.id}/edit`}
              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
            >
              Edit
            </Link>
            <Link
              href={`/rooms/${room.id}`}
              target="_blank"
              className="text-green-600 hover:text-green-800 font-medium text-sm"
            >
              View
            </Link>
          </div>
          <span className="text-xs text-gray-500">
            Updated {new Date(room.updatedAt).toLocaleDateString()}
          </span>
        </div>
      </div>
    </div>
  );
}

// Rooms grid component
async function RoomsGrid({ resortId }: { resortId: string }) {
  try {
    const rooms = await prisma.room.findMany({
      where: { resortId },
      orderBy: { createdAt: "desc" },
    });

    if (rooms.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🛏️</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No Rooms Created Yet
          </h3>
          <p className="text-gray-600 mb-4">
            Start by adding rooms to this resort.
          </p>
          <Link
            href={`/admin/resorts/${resortId}/rooms/create`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            + Add First Room
          </Link>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {rooms.map((room) => (
          <RoomCard key={room.id} room={room} resortId={resortId} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching rooms:", error);
    return (
      <div className="text-center py-12">
        <div className="text-red-400 text-6xl mb-4">⚠️</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Error Loading Rooms
        </h3>
        <p className="text-gray-600 mb-4">
          Unable to load room data. Please try refreshing the page.
        </p>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
        >
          Refresh Page
        </button>
      </div>
    );
  }
}

export default async function ResortRoomsPage({
  params,
}: {
  params: { id: string };
}) {
  const resort = await prisma.resort.findUnique({
    where: { id: params.id },
  });

  if (!resort) return notFound();

  // Get room statistics
  const roomStats = await prisma.room.aggregate({
    where: { resortId: params.id },
    _count: { id: true },
    _avg: { price: true },
  });

  const activeRooms = await prisma.room.count({
    where: { resortId: params.id, isActive: true },
  });

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/admin/resorts" className="hover:text-gray-700">
                Resorts
              </Link>
              <span>›</span>
              <Link href={`/admin/resorts/${resort.id}/edit`} className="hover:text-gray-700">
                {resort.name}
              </Link>
              <span>›</span>
              <span>Rooms</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              {resort.name} - Room Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage rooms and availability for this resort
            </p>
          </div>
          <Link
            href={`/admin/resorts/${params.id}/rooms/create`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors shadow-sm"
          >
            <span className="mr-2">+</span>
            Add Room
          </Link>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Rooms</p>
              <p className="text-2xl font-bold text-gray-900">{roomStats._count.id}</p>
            </div>
            <div className="p-3 rounded-lg bg-blue-50 text-blue-600">
              <span className="text-xl">🛏️</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Rooms</p>
              <p className="text-2xl font-bold text-gray-900">{activeRooms}</p>
            </div>
            <div className="p-3 rounded-lg bg-green-50 text-green-600">
              <span className="text-xl">✅</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Price</p>
              <p className="text-2xl font-bold text-gray-900">
                ${roomStats._avg.price ? Math.round(roomStats._avg.price) : 0}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-purple-50 text-purple-600">
              <span className="text-xl">💰</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Occupancy</p>
              <p className="text-2xl font-bold text-gray-900">--%</p>
              <p className="text-xs text-gray-500">Coming soon</p>
            </div>
            <div className="p-3 rounded-lg bg-orange-50 text-orange-600">
              <span className="text-xl">📊</span>
            </div>
          </div>
        </div>
      </div>

      {/* Rooms Grid */}
      <Suspense
        fallback={
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm border animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-4 space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        }
      >
        <RoomsGrid resortId={params.id} />
      </Suspense>
    </div>
  );
}
