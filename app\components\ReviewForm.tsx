"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { ReviewFormProps, CreateReviewInput, ReviewFormErrors } from "@/app/types/review";
import StarRating from "./StarRating";
import { motion } from "framer-motion";
import toast from "react-hot-toast";

const ReviewForm: React.FC<ReviewFormProps> = ({
  contentType,
  contentId,
  contentName,
  onSubmit,
  loading = false,
  className = "",
}) => {
  const { data: session } = useSession();
  const [formData, setFormData] = useState<CreateReviewInput>({
    rating: 0,
    title: "",
    comment: "",
    images: [],
    [`${contentType}Id`]: contentId,
  });
  const [errors, setErrors] = useState<ReviewFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: ReviewFormErrors = {};

    if (formData.rating === 0) {
      newErrors.rating = "Please select a rating";
    }

    if (!formData.comment.trim()) {
      newErrors.comment = "Please write a review comment";
    } else if (formData.comment.trim().length < 10) {
      newErrors.comment = "Review comment must be at least 10 characters";
    } else if (formData.comment.trim().length > 1000) {
      newErrors.comment = "Review comment must be less than 1000 characters";
    }

    if (formData.title && formData.title.length > 100) {
      newErrors.title = "Review title must be less than 100 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!session) {
      toast.error("Please sign in to submit a review");
      return;
    }

    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(formData);
      
      // Reset form on success
      setFormData({
        rating: 0,
        title: "",
        comment: "",
        images: [],
        [`${contentType}Id`]: contentId,
      });
      setErrors({});
      
      toast.success("Review submitted successfully! It will be published after approval.");
    } catch (error) {
      console.error("Error submitting review:", error);
      toast.error("Failed to submit review. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRatingChange = (rating: number) => {
    setFormData(prev => ({ ...prev, rating }));
    if (errors.rating) {
      setErrors(prev => ({ ...prev, rating: undefined }));
    }
  };

  const handleInputChange = (field: keyof CreateReviewInput, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof ReviewFormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!session) {
    return (
      <div className={`bg-gray-50 rounded-lg p-6 text-center ${className}`}>
        <p className="text-gray-600 mb-4">Please sign in to write a review</p>
        <button
          onClick={() => window.location.href = '/api/auth/signin'}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Sign In
        </button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`bg-white rounded-lg shadow-md p-6 border border-gray-200 ${className}`}
    >
      <h3 className="text-xl font-semibold text-gray-900 mb-4">
        Write a Review for {contentName}
      </h3>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Rating */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Rating *
          </label>
          <StarRating
            rating={formData.rating}
            interactive={true}
            onChange={handleRatingChange}
            size="lg"
            className="mb-2"
          />
          {formData.rating > 0 && (
            <p className="text-sm text-gray-600">
              {formData.rating === 1 && "Poor"}
              {formData.rating === 2 && "Fair"}
              {formData.rating === 3 && "Good"}
              {formData.rating === 4 && "Very Good"}
              {formData.rating === 5 && "Excellent"}
            </p>
          )}
          {errors.rating && (
            <p className="text-red-600 text-sm mt-1" role="alert">
              {errors.rating}
            </p>
          )}
        </div>

        {/* Title */}
        <div>
          <label htmlFor="review-title" className="block text-sm font-medium text-gray-700 mb-2">
            Review Title (Optional)
          </label>
          <input
            id="review-title"
            type="text"
            value={formData.title || ""}
            onChange={(e) => handleInputChange("title", e.target.value)}
            placeholder="Summarize your experience..."
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.title ? "border-red-500" : "border-gray-300"
            }`}
            maxLength={100}
          />
          <div className="flex justify-between mt-1">
            {errors.title && (
              <p className="text-red-600 text-sm" role="alert">
                {errors.title}
              </p>
            )}
            <p className="text-gray-500 text-sm ml-auto">
              {(formData.title || "").length}/100
            </p>
          </div>
        </div>

        {/* Comment */}
        <div>
          <label htmlFor="review-comment" className="block text-sm font-medium text-gray-700 mb-2">
            Your Review *
          </label>
          <textarea
            id="review-comment"
            value={formData.comment}
            onChange={(e) => handleInputChange("comment", e.target.value)}
            placeholder="Share your experience with other guests..."
            rows={5}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical ${
              errors.comment ? "border-red-500" : "border-gray-300"
            }`}
            maxLength={1000}
          />
          <div className="flex justify-between mt-1">
            {errors.comment && (
              <p className="text-red-600 text-sm" role="alert">
                {errors.comment}
              </p>
            )}
            <p className="text-gray-500 text-sm ml-auto">
              {formData.comment.length}/1000
            </p>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-500">
            * Required fields. Your review will be published after approval.
          </p>
          
          <button
            type="submit"
            disabled={isSubmitting || loading}
            className={`px-6 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
              isSubmitting || loading
                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Submitting...</span>
              </div>
            ) : (
              "Submit Review"
            )}
          </button>
        </div>
      </form>
    </motion.div>
  );
};

export default ReviewForm;
