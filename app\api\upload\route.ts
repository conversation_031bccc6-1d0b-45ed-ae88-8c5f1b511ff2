import { NextRequest, NextResponse } from "next/server";
import cloudinary from "@/lib/cloudinary";
import { Readable } from "stream";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { requireRole } from "@/lib/checkRole";
import { logAuditEvent } from "@/lib/security";

// File upload security configuration
const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedMimeTypes: [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/webp",
    "image/gif",
  ],
  allowedExtensions: [".jpg", ".jpeg", ".png", ".webp", ".gif"],
  maxDimensions: {
    width: 4096,
    height: 4096,
  },
};

function validateFile(file: File): { isValid: boolean; error?: string } {
  // Check file size
  if (file.size > UPLOAD_CONFIG.maxFileSize) {
    return {
      isValid: false,
      error: `File size exceeds ${
        UPLOAD_CONFIG.maxFileSize / (1024 * 1024)
      }MB limit`,
    };
  }

  // Check MIME type
  if (!UPLOAD_CONFIG.allowedMimeTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${
        file.type
      } not allowed. Allowed types: ${UPLOAD_CONFIG.allowedMimeTypes.join(
        ", "
      )}`,
    };
  }

  // Check file extension
  const extension = file.name
    .toLowerCase()
    .substring(file.name.lastIndexOf("."));
  if (!UPLOAD_CONFIG.allowedExtensions.includes(extension)) {
    return {
      isValid: false,
      error: `File extension ${extension} not allowed. Allowed extensions: ${UPLOAD_CONFIG.allowedExtensions.join(
        ", "
      )}`,
    };
  }

  // Check for suspicious file names
  if (
    file.name.includes("..") ||
    file.name.includes("/") ||
    file.name.includes("\\")
  ) {
    return {
      isValid: false,
      error: "Invalid file name",
    };
  }

  return { isValid: true };
}

async function scanFileContent(
  buffer: Buffer
): Promise<{ isSafe: boolean; error?: string }> {
  // Basic file header validation
  const fileSignatures = {
    jpeg: [0xff, 0xd8, 0xff],
    png: [0x89, 0x50, 0x4e, 0x47],
    gif: [0x47, 0x49, 0x46],
    webp: [0x52, 0x49, 0x46, 0x46],
  };

  // Check if file starts with valid image signature
  for (const [type, signature] of Object.entries(fileSignatures)) {
    if (signature.every((byte, index) => buffer[index] === byte)) {
      return { isSafe: true };
    }
  }

  // Additional checks for WebP (RIFF container)
  if (
    buffer.length >= 12 &&
    buffer[0] === 0x52 &&
    buffer[1] === 0x49 &&
    buffer[2] === 0x46 &&
    buffer[3] === 0x46 &&
    buffer[8] === 0x57 &&
    buffer[9] === 0x45 &&
    buffer[10] === 0x42 &&
    buffer[11] === 0x50
  ) {
    return { isSafe: true };
  }

  return {
    isSafe: false,
    error: "File content does not match expected image format",
  };
}

export async function POST(request: NextRequest) {
  try {
    // Check authorization - only admin and manager can upload files
    const authResult = await requireRole(["admin", "manager"]);
    if (authResult instanceof NextResponse) {
      return authResult;
    }

    const { user } = authResult;

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const uploadType = formData.get("type") as string; // 'resort', 'spa', 'profile', etc.

    if (!file) {
      await logAuditEvent({
        userId: user.id,
        action: "FILE_UPLOAD_FAILED",
        resource: "/api/upload",
        ip: request.headers.get("x-forwarded-for") || "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
        success: false,
        error: "No file provided",
      });
      return NextResponse.json({ error: "No file uploaded" }, { status: 400 });
    }

    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      await logAuditEvent({
        userId: user.id,
        action: "FILE_UPLOAD_FAILED",
        resource: "/api/upload",
        ip: request.headers.get("x-forwarded-for") || "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
        success: false,
        error: validation.error,
      });
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Scan file content for security
    const contentScan = await scanFileContent(buffer);
    if (!contentScan.isSafe) {
      await logAuditEvent({
        userId: user.id,
        action: "MALICIOUS_FILE_UPLOAD_ATTEMPT",
        resource: "/api/upload",
        ip: request.headers.get("x-forwarded-for") || "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
        success: false,
        error: contentScan.error,
      });
      return NextResponse.json({ error: contentScan.error }, { status: 400 });
    }

    // Generate secure filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = file.name
      .toLowerCase()
      .substring(file.name.lastIndexOf("."));
    const secureFilename = `${
      uploadType || "general"
    }_${timestamp}_${randomString}${extension}`;

    // Upload to Cloudinary with security options
    const result = await new Promise((resolve, reject) => {
      const stream = cloudinary.uploader.upload_stream(
        {
          resource_type: "image",
          public_id: secureFilename,
          folder: `kuriftu/${uploadType || "general"}`,
          transformation: [
            { quality: "auto:good" },
            { fetch_format: "auto" },
            {
              width: UPLOAD_CONFIG.maxDimensions.width,
              height: UPLOAD_CONFIG.maxDimensions.height,
              crop: "limit",
            },
          ],
          allowed_formats: ["jpg", "jpeg", "png", "webp", "gif"],
          use_filename: false,
          unique_filename: true,
          overwrite: false,
          invalidate: true,
          // Additional security options
          moderation: "aws_rek", // Enable AI content moderation if available
          notification_url: process.env.CLOUDINARY_WEBHOOK_URL, // Optional webhook for processing
        },
        (error, result) => {
          if (error) {
            console.error("Cloudinary upload error:", error);
            reject(new Error(`Upload failed: ${error.message}`));
          } else {
            resolve(result);
          }
        }
      );

      // Handle stream errors
      stream.on("error", (error) => {
        console.error("Stream error:", error);
        reject(new Error(`Stream error: ${error.message}`));
      });

      Readable.from(buffer).pipe(stream);
    });

    await logAuditEvent({
      userId: user.id,
      action: "FILE_UPLOADED",
      resource: "/api/upload",
      resourceId: (result as any).public_id,
      ip: request.headers.get("x-forwarded-for") || "unknown",
      userAgent: request.headers.get("user-agent") || "unknown",
      success: true,
    });

    // Return sanitized response
    const sanitizedResponse = {
      public_id: (result as any).public_id,
      secure_url: (result as any).secure_url,
      url: (result as any).url,
      width: (result as any).width,
      height: (result as any).height,
      format: (result as any).format,
      bytes: (result as any).bytes,
      created_at: (result as any).created_at,
    };

    return NextResponse.json(sanitizedResponse);
  } catch (error) {
    console.error("Upload error:", error);

    const session = await getServerSession(authOptions);
    await logAuditEvent({
      userId: session?.user?.id,
      action: "FILE_UPLOAD_ERROR",
      resource: "/api/upload",
      ip: request.headers.get("x-forwarded-for") || "unknown",
      userAgent: request.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json({ error: "Upload failed" }, { status: 500 });
  }
}
