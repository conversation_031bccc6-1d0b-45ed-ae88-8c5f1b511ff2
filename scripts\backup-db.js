#!/usr/bin/env node

/**
 * Database Backup Script
 * Creates secure backups of the MongoDB database
 */

import { exec } from "child_process";
import fs from "fs";
import path from "path";
import crypto from "crypto";
import { promisify } from "util";

const execAsync = promisify(exec);

class DatabaseBackup {
  constructor() {
    this.backupDir = path.join(process.cwd(), "backups");
    this.maxBackups = 30; // Keep 30 days of backups
    this.compressionLevel = 9;
  }

  async ensureBackupDirectory() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      console.log(`✅ Created backup directory: ${this.backupDir}`);
    }
  }

  parseMongoUri(uri) {
    try {
      const url = new URL(uri);
      return {
        host: url.hostname,
        port: url.port || "27017",
        database: url.pathname.substring(1),
        username: url.username,
        password: url.password,
        options: Object.fromEntries(url.searchParams),
      };
    } catch (error) {
      throw new Error(`Invalid MongoDB URI: ${error.message}`);
    }
  }

  generateBackupFilename() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const randomSuffix = crypto.randomBytes(4).toString("hex");
    return `backup_${timestamp}_${randomSuffix}`;
  }

  async createBackup() {
    console.log("🔄 Starting database backup...");

    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error("MONGODB_URI environment variable not found");
    }

    const dbConfig = this.parseMongoUri(mongoUri);
    const backupName = this.generateBackupFilename();
    const backupPath = path.join(this.backupDir, backupName);

    try {
      // Create backup using mongodump
      let mongodumpCmd = `mongodump --uri="${mongoUri}" --out="${backupPath}"`;

      // Add compression if available
      mongodumpCmd += " --gzip";

      console.log("📦 Creating database dump...");
      const { stderr } = await execAsync(mongodumpCmd);

      if (stderr && !stderr.includes("done dumping")) {
        console.warn("⚠️  Backup warnings:", stderr);
      }

      console.log("✅ Database dump created successfully");

      // Compress the backup
      const compressedPath = await this.compressBackup(backupPath, backupName);

      // Encrypt the backup if encryption key is available
      let finalPath = compressedPath;
      if (process.env.BACKUP_ENCRYPTION_KEY) {
        finalPath = await this.encryptBackup(compressedPath, backupName);
      }

      // Generate checksum
      const checksum = await this.generateChecksum(finalPath);

      // Save backup metadata
      await this.saveBackupMetadata(backupName, {
        timestamp: new Date().toISOString(),
        database: dbConfig.database,
        size: fs.statSync(finalPath).size,
        checksum,
        encrypted: !!process.env.BACKUP_ENCRYPTION_KEY,
        compressed: true,
      });

      // Clean up temporary files
      if (fs.existsSync(backupPath)) {
        await execAsync(`rm -rf "${backupPath}"`);
      }
      if (compressedPath !== finalPath && fs.existsSync(compressedPath)) {
        fs.unlinkSync(compressedPath);
      }

      console.log(`✅ Backup completed: ${path.basename(finalPath)}`);
      console.log(
        `📊 Backup size: ${(fs.statSync(finalPath).size / 1024 / 1024).toFixed(
          2
        )} MB`
      );

      return finalPath;
    } catch (error) {
      console.error("❌ Backup failed:", error.message);

      // Clean up on failure
      if (fs.existsSync(backupPath)) {
        await execAsync(`rm -rf "${backupPath}"`).catch(() => {});
      }

      throw error;
    }
  }

  async compressBackup(backupPath, backupName) {
    console.log("🗜️  Compressing backup...");

    const compressedPath = path.join(this.backupDir, `${backupName}.tar.gz`);
    const tarCmd = `tar -czf "${compressedPath}" -C "${this.backupDir}" "${backupName}"`;

    await execAsync(tarCmd);

    console.log("✅ Backup compressed successfully");
    return compressedPath;
  }

  async encryptBackup(backupPath, backupName) {
    console.log("🔐 Encrypting backup...");

    const encryptedPath = path.join(this.backupDir, `${backupName}.tar.gz.enc`);
    const encryptionKey = process.env.BACKUP_ENCRYPTION_KEY;

    // Use AES-256-CBC encryption
    const algorithm = "aes-256-cbc";
    const key = crypto.scryptSync(encryptionKey, "salt", 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipher(algorithm, key);
    const input = fs.createReadStream(backupPath);
    const output = fs.createWriteStream(encryptedPath);

    // Write IV to the beginning of the file
    output.write(iv);

    return new Promise((resolve, reject) => {
      input.pipe(cipher).pipe(output);

      output.on("finish", () => {
        console.log("✅ Backup encrypted successfully");
        resolve(encryptedPath);
      });

      output.on("error", reject);
    });
  }

  async generateChecksum(filePath) {
    const hash = crypto.createHash("sha256");
    const stream = fs.createReadStream(filePath);

    return new Promise((resolve, reject) => {
      stream.on("data", (data) => hash.update(data));
      stream.on("end", () => resolve(hash.digest("hex")));
      stream.on("error", reject);
    });
  }

  async saveBackupMetadata(backupName, metadata) {
    const metadataPath = path.join(this.backupDir, `${backupName}.json`);
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
  }

  async cleanupOldBackups() {
    console.log("🧹 Cleaning up old backups...");

    try {
      const files = fs.readdirSync(this.backupDir);
      const backupFiles = files
        .filter(
          (file) =>
            file.startsWith("backup_") &&
            (file.endsWith(".tar.gz") || file.endsWith(".tar.gz.enc"))
        )
        .map((file) => ({
          name: file,
          path: path.join(this.backupDir, file),
          mtime: fs.statSync(path.join(this.backupDir, file)).mtime,
        }))
        .sort((a, b) => b.mtime - a.mtime);

      if (backupFiles.length > this.maxBackups) {
        const filesToDelete = backupFiles.slice(this.maxBackups);

        for (const file of filesToDelete) {
          fs.unlinkSync(file.path);

          // Also delete metadata file
          const metadataFile = file.path.replace(
            /\.(tar\.gz|tar\.gz\.enc)$/,
            ".json"
          );
          if (fs.existsSync(metadataFile)) {
            fs.unlinkSync(metadataFile);
          }

          console.log(`🗑️  Deleted old backup: ${file.name}`);
        }

        console.log(`✅ Cleaned up ${filesToDelete.length} old backups`);
      } else {
        console.log("✅ No old backups to clean up");
      }
    } catch (error) {
      console.warn("⚠️  Failed to clean up old backups:", error.message);
    }
  }

  async listBackups() {
    console.log("📋 Available backups:");

    try {
      const files = fs.readdirSync(this.backupDir);
      const backupFiles = files
        .filter((file) => file.endsWith(".json"))
        .filter((file) => file.startsWith("backup_"))
        .sort()
        .reverse();

      if (backupFiles.length === 0) {
        console.log("No backups found");
        return;
      }

      for (const file of backupFiles) {
        const metadataPath = path.join(this.backupDir, file);
        const metadata = JSON.parse(fs.readFileSync(metadataPath, "utf8"));

        console.log(`\n📦 ${file.replace(".json", "")}`);
        console.log(
          `   Date: ${new Date(metadata.timestamp).toLocaleString()}`
        );
        console.log(`   Database: ${metadata.database}`);
        console.log(`   Size: ${(metadata.size / 1024 / 1024).toFixed(2)} MB`);
        console.log(`   Encrypted: ${metadata.encrypted ? "Yes" : "No"}`);
        console.log(`   Checksum: ${metadata.checksum.substring(0, 16)}...`);
      }
    } catch (error) {
      console.error("❌ Failed to list backups:", error.message);
    }
  }

  async run(command = "backup") {
    try {
      await this.ensureBackupDirectory();

      switch (command) {
        case "backup":
          await this.createBackup();
          await this.cleanupOldBackups();
          break;
        case "list":
          await this.listBackups();
          break;
        case "cleanup":
          await this.cleanupOldBackups();
          break;
        default:
          console.log("Usage: node backup-db.js [backup|list|cleanup]");
          process.exit(1);
      }
    } catch (error) {
      console.error("❌ Backup operation failed:", error.message);
      process.exit(1);
    }
  }
}

// Run the backup
const command = process.argv[2] || "backup";
const backup = new DatabaseBackup();
backup.run(command);
