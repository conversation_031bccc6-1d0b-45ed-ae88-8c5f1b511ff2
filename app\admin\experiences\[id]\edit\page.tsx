import ExperienceForm from "@/app/components/admin/ExperienceForm";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";

export default async function EditExperience({ params }: { params: { id: string } }) {
  const experience = await prisma.experience.findUnique({
    where: { id: params.id },
  });

  if (!experience) return notFound();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Edit Experience</h1>
        <p className="text-gray-600 mt-1">
          Update the details for {experience.name}
        </p>
      </div>
      <ExperienceForm initialData={experience} />
    </div>
  );
}
