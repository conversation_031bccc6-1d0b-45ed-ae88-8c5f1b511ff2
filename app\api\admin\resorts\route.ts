import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function POST(req: Request) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult; // Return error response
  }

  try {
    const data = await req.json();

    // Basic input validation
    if (!data.name || !data.slug || !data.description || !data.location) {
      return NextResponse.json(
        { error: "Missing required fields: name, slug, description, location" },
        { status: 400 }
      );
    }

    const resort = await prisma.resort.create({ data });
    return NextResponse.json(resort);
  } catch (error) {
    console.error("Error creating resort:", error);
    return NextResponse.json(
      { error: "Failed to create resort" },
      { status: 500 }
    );
  }
}
