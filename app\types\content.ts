// TypeScript interfaces for all dynamic content types

// Base interface for common properties
export interface BaseContent {
  id: string;
  name: string;
  description: string;
  image: string;
  images?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Accommodation (Enhanced Room) Interface
export interface Accommodation extends BaseContent {
  resortId: string;
  type: string;
  price: number;
  amenities: string[];
  capacity: number;
  size?: number; // Square meters
  resort?: Resort;
}

// Experience Interface
export interface Experience extends BaseContent {
  price: number;
  duration?: number; // Duration in minutes
  category?: string; // Adventure, Cultural, Water Sports, etc.
  features: string[];
  difficulty?: string; // Easy, Moderate, Challenging
  minAge?: number;
  maxCapacity?: number;
  location?: string;
}

// Wellness Service Interface
export interface WellnessService extends BaseContent {
  price: number;
  duration?: number; // Duration in minutes
  category?: string; // Fitness, Yoga, Meditation, Therapy, etc.
  features: string[];
  instructor?: string;
  equipment: string[];
  maxCapacity?: number;
}

// Dining Option Interface
export interface DiningOption extends BaseContent {
  category?: string; // Restaurant, Cafe, Bar, Room Service, etc.
  cuisine?: string; // Ethiopian, International, Italian, etc.
  priceRange?: string; // $, $$, $$$, $$$$
  features: string[];
  openingHours?: string;
  location?: string;
  capacity?: number;
}

// Event Interface
export interface Event extends BaseContent {
  price?: number; // Price per person or base price
  category?: string; // Wedding, Conference, Birthday, Corporate, etc.
  features: string[];
  venue?: string;
  maxCapacity?: number;
  duration?: number; // Duration in hours
}

// Special Offer Interface
export interface SpecialOffer extends BaseContent {
  category?: string; // Package, Discount, Seasonal, etc.
  originalPrice?: number;
  discountPrice?: number;
  discountPercent?: number;
  features: string[];
  validFrom?: Date;
  validUntil?: Date;
  terms?: string;
}

// Resort Interface (for reference)
export interface Resort {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
  createdAt: Date;
}

// Content type union for generic components
export type ContentItem = 
  | Accommodation 
  | Experience 
  | WellnessService 
  | DiningOption 
  | Event 
  | SpecialOffer;

// Content categories
export type AccommodationType = "Standard" | "Deluxe" | "Suite" | "Villa" | "Cottage";
export type ExperienceCategory = "Adventure" | "Cultural" | "Water Sports" | "Wildlife" | "Relaxation";
export type WellnessCategory = "Fitness" | "Yoga" | "Meditation" | "Therapy" | "Nutrition";
export type DiningCategory = "Restaurant" | "Cafe" | "Bar" | "Room Service" | "Buffet";
export type EventCategory = "Wedding" | "Conference" | "Birthday" | "Corporate" | "Cultural";
export type OfferCategory = "Package" | "Discount" | "Seasonal" | "Early Bird" | "Last Minute";

// Component Props Interfaces
export interface ContentCardProps<T extends ContentItem> {
  item: T;
  isLoading?: boolean;
  showBookButton?: boolean;
  showDetailsButton?: boolean;
  onBook?: (itemId: string) => void;
  onViewDetails?: (itemId: string) => void;
  className?: string;
}

export interface DynamicContentSectionProps {
  title: string;
  description?: string;
  apiEndpoint: string;
  contentType: 'accommodation' | 'experience' | 'wellness' | 'dining' | 'event' | 'offer';
  className?: string;
  showTitle?: boolean;
  maxItems?: number;
  enableRealTimeUpdates?: boolean;
  updateInterval?: number;
  gridCols?: 1 | 2 | 3 | 4;
}

// Booking related interfaces
export interface BookingRequest {
  contentType: 'resort' | 'room' | 'spa' | 'experience' | 'wellness' | 'event';
  contentId: string;
  checkIn: Date;
  checkOut?: Date;
  participants?: number;
  notes?: string;
}

// Filter interfaces
export interface ContentFilters {
  category?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  features?: string[];
  isActive?: boolean;
  searchTerm?: string;
}

// Admin interfaces
export interface CreateContentInput<T> extends Omit<T, 'id' | 'createdAt' | 'updatedAt'> {}
export interface UpdateContentInput<T> extends Partial<CreateContentInput<T>> {
  id: string;
}

// API Response interfaces
export interface ContentListResponse<T> {
  data: T[];
  total: number;
  page?: number;
  limit?: number;
}

export interface ContentDetailResponse<T> {
  data: T;
}

// Error interface
export interface ContentError {
  message: string;
  code?: string;
  field?: string;
}
