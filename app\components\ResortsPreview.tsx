"use client";
import { motion } from "framer-motion";
import Image from "next/image";

const resorts = [
  { name: "Kuriftu Entoto", image: "/images/entoto.jpg" },
  { name: "Kuriftu Water Park", image: "/images/water-park.jpg" },
  { name: "Kuriftu Awash Park", image: "/images/awash.jpg" },
  { name: "Kuriftu African Village", image: "/images/african-village.jpg" },
];

export default function ResortsPreview() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-6xl mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-3xl font-bold text-center mb-10"
        >
          Explore Our Resorts
        </motion.h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {resorts.map((resort) => (
            <motion.div
              key={resort.name}
              whileHover={{ scale: 1.05 }}
              className="rounded-xl overflow-hidden shadow-lg bg-white"
            >
              <Image
                src={resort.image}
                alt={resort.name}
                className="h-48 w-full object-cover"
              />
              <div className="p-4 text-center font-semibold">{resort.name}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
