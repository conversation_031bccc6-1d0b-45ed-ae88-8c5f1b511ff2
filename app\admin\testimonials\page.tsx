"use client";

import { useState, useEffect } from "react";
import { Testimonial } from "@/app/types/review";
import { motion } from "framer-motion";
import toast from "react-hot-toast";
import Image from "next/image";
import {
  ListItemSkeleton,
  StatCardSkeleton,
} from "@/app/components/ui/SkeletonComponents";
import { LoadingError, EmptyState } from "@/app/components/ui/ErrorComponents";
import ErrorBoundary from "@/app/components/dashboard/ErrorBoundary";

interface TestimonialStats {
  total: number;
  pending: number;
  approved: number;
  published: number;
  featured: number;
}

export default function AdminTestimonialsPage() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<TestimonialStats>({
    total: 0,
    pending: 0,
    approved: 0,
    published: 0,
    featured: 0,
  });
  const [selectedTestimonials, setSelectedTestimonials] = useState<string[]>(
    []
  );
  const [currentFilter, setCurrentFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchTestimonials = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20",
      });

      if (currentFilter !== "all") {
        params.append("status", currentFilter);
      }

      const response = await fetch(
        `/api/admin/testimonials?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch testimonials");
      }

      const data = await response.json();
      setTestimonials(data.testimonials);
      setStats(data.summary);
      setTotalPages(Math.ceil(data.total / 20));
    } catch (error) {
      console.error("Error fetching testimonials:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load testimonials"
      );
      toast.error("Failed to load testimonials");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTestimonials();
  }, [currentFilter, currentPage]);

  const handleBulkAction = async (action: string) => {
    if (selectedTestimonials.length === 0) {
      toast.error("Please select testimonials to perform this action");
      return;
    }

    const reason =
      action === "reject"
        ? prompt("Reason for rejection (optional):")
        : undefined;
    const displayOrder =
      action === "feature"
        ? prompt("Display order (optional number):")
        : undefined;

    try {
      const response = await fetch("/api/admin/testimonials", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          testimonialIds: selectedTestimonials,
          action,
          reason,
          displayOrder: displayOrder ? parseInt(displayOrder) : undefined,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update testimonials");
      }

      const data = await response.json();
      toast.success(data.message);
      setSelectedTestimonials([]);
      fetchTestimonials();
    } catch (error) {
      console.error("Error updating testimonials:", error);
      toast.error("Failed to update testimonials");
    }
  };

  const handleSelectAll = () => {
    if (selectedTestimonials.length === testimonials.length) {
      setSelectedTestimonials([]);
    } else {
      setSelectedTestimonials(testimonials.map((t) => t.id));
    }
  };

  const handleSelectTestimonial = (testimonialId: string) => {
    setSelectedTestimonials((prev) =>
      prev.includes(testimonialId)
        ? prev.filter((id) => id !== testimonialId)
        : [...prev, testimonialId]
    );
  };

  const getStatusBadge = (testimonial: Testimonial) => {
    if (testimonial.isFeatured) {
      return (
        <span className="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
          Featured
        </span>
      );
    } else if (testimonial.isPublished) {
      return (
        <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
          Published
        </span>
      );
    } else if (testimonial.isApproved) {
      return (
        <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
          Approved
        </span>
      );
    } else {
      return (
        <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
          Pending
        </span>
      );
    }
  };

  const handleRetry = () => {
    setError(null);
    fetchTestimonials();
  };

  if (loading) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="h-8 bg-gray-300 rounded w-1/4 mb-6 animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <StatCardSkeleton key={i} />
          ))}
        </div>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <ListItemSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorBoundary>
        <div className="p-6 max-w-7xl mx-auto">
          <LoadingError
            resource="testimonial data"
            onRetry={handleRetry}
          />
        </div>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Testimonial Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage customer testimonials and success stories
            </p>
          </div>
        </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-gray-600">Total</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="text-2xl font-bold text-yellow-600">
            {stats.pending}
          </div>
          <div className="text-gray-600">Pending</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="text-2xl font-bold text-blue-600">
            {stats.approved}
          </div>
          <div className="text-gray-600">Approved</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="text-2xl font-bold text-green-600">
            {stats.published}
          </div>
          <div className="text-gray-600">Published</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="text-2xl font-bold text-purple-600">
            {stats.featured}
          </div>
          <div className="text-gray-600">Featured</div>
        </motion.div>
      </div>

      {/* Filters and Actions */}
      <div className="bg-white rounded-lg shadow mb-6">
        <div className="p-4 border-b border-gray-200">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <select
                value={currentFilter}
                onChange={(e) => setCurrentFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Testimonials</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="published">Published</option>
                <option value="featured">Featured</option>
              </select>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={
                    selectedTestimonials.length === testimonials.length &&
                    testimonials.length > 0
                  }
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">Select All</span>
              </label>

              {selectedTestimonials.length > 0 && (
                <span className="text-sm text-gray-600">
                  {selectedTestimonials.length} selected
                </span>
              )}
            </div>

            {selectedTestimonials.length > 0 && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleBulkAction("approve")}
                  className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                >
                  Approve
                </button>
                <button
                  onClick={() => handleBulkAction("publish")}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  Publish
                </button>
                <button
                  onClick={() => handleBulkAction("feature")}
                  className="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700 transition-colors"
                >
                  Feature
                </button>
                <button
                  onClick={() => handleBulkAction("reject")}
                  className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                >
                  Reject
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Testimonials List */}
        <div className="p-4">
          {testimonials.length > 0 ? (
            <div className="space-y-4">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-start space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedTestimonials.includes(testimonial.id)}
                      onChange={() => handleSelectTestimonial(testimonial.id)}
                      className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />

                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          {getStatusBadge(testimonial)}
                          {testimonial.displayOrder && (
                            <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                              Order: {testimonial.displayOrder}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(testimonial.createdAt).toLocaleDateString()}
                        </div>
                      </div>

                      <div className="bg-gray-50 rounded-lg p-4">
                        {testimonial.title && (
                          <h4 className="font-semibold text-gray-900 mb-2">
                            {testimonial.title}
                          </h4>
                        )}

                        <p className="text-gray-700 italic mb-3 leading-relaxed">
                          "{testimonial.content}"
                        </p>

                        <div className="flex items-center space-x-3">
                          {testimonial.image ? (
                            <Image
                              src={testimonial.image}
                              alt={testimonial.name}
                              width={40}
                              height={40}
                              className="rounded-full object-cover"
                            />
                          ) : (
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-blue-600 font-medium">
                                {testimonial.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}

                          <div>
                            <p className="font-semibold text-gray-900">
                              {testimonial.name}
                            </p>
                            {testimonial.location && (
                              <p className="text-sm text-gray-500">
                                {testimonial.location}
                              </p>
                            )}
                            {testimonial.user && (
                              <p className="text-xs text-gray-400">
                                User: {testimonial.user.email}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-600">
                No testimonials found for the selected filter.
              </p>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="p-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <button
                onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>

              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>

              <button
                onClick={() =>
                  setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                }
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
}
