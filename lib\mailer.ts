import nodemailer from "nodemailer";
import { logAuditEvent } from "./security";

// Email configuration with security settings
export const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: Number(process.env.EMAIL_PORT),
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
  tls: {
    rejectUnauthorized: true,
  },
});

// Enhanced booking confirmation email with proper template
export const sendBookingConfirmation = async (
  userEmail: string,
  bookingData: any
) => {
  const emailTemplate = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Booking Confirmation - Kuriftu Resorts</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .booking-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea; }
        .detail-row { display: flex; justify-content: space-between; margin: 10px 0; padding: 8px 0; border-bottom: 1px solid #eee; }
        .detail-label { font-weight: bold; color: #555; }
        .detail-value { color: #333; }
        .status { padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status.pending { background: #fff3cd; color: #856404; }
        .status.confirmed { background: #d4edda; color: #155724; }
        .footer { text-align: center; margin-top: 30px; padding: 20px; color: #666; font-size: 14px; }
        .contact-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .button { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🏨 Kuriftu Resorts</h1>
        <h2>Booking Confirmation</h2>
      </div>

      <div class="content">
        <p>Dear ${bookingData.customerName || "Valued Guest"},</p>
        <p>Thank you for choosing Kuriftu Resorts! We're delighted to confirm your booking.</p>

        <div class="booking-details">
          <h3>📋 Booking Details</h3>
          <div class="detail-row">
            <span class="detail-label">Booking ID:</span>
            <span class="detail-value">${bookingData.bookingId}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Type:</span>
            <span class="detail-value">${bookingData.type}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">${
              bookingData.type === "Resort" ? "Resort" : "Treatment"
            }:</span>
            <span class="detail-value">${bookingData.name}</span>
          </div>
          ${
            bookingData.location
              ? `
          <div class="detail-row">
            <span class="detail-label">Location:</span>
            <span class="detail-value">${bookingData.location}</span>
          </div>
          `
              : ""
          }
          <div class="detail-row">
            <span class="detail-label">Check-in:</span>
            <span class="detail-value">${bookingData.checkIn}</span>
          </div>
          ${
            bookingData.checkOut
              ? `
          <div class="detail-row">
            <span class="detail-label">Check-out:</span>
            <span class="detail-value">${bookingData.checkOut}</span>
          </div>
          `
              : ""
          }
          <div class="detail-row">
            <span class="detail-label">Status:</span>
            <span class="detail-value">
              <span class="status ${
                bookingData.status?.toLowerCase() || "pending"
              }">${bookingData.status || "PENDING"}</span>
            </span>
          </div>
          ${
            bookingData.notes
              ? `
          <div class="detail-row">
            <span class="detail-label">Notes:</span>
            <span class="detail-value">${bookingData.notes}</span>
          </div>
          `
              : ""
          }
        </div>

        <div class="contact-info">
          <h4>📞 Contact Information</h4>
          <p><strong>Phone:</strong> +251 11 123 4567</p>
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Website:</strong> www.kuriftu.com</p>
        </div>

        <p>If you need to modify or cancel your booking, please contact us at least 24 hours in advance.</p>

        <div style="text-align: center;">
          <a href="${
            process.env.NEXTAUTH_URL
          }/bookings" class="button">View My Bookings</a>
        </div>
      </div>

      <div class="footer">
        <p>Thank you for choosing Kuriftu Resorts!</p>
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; ${new Date().getFullYear()} Kuriftu Resorts. All rights reserved.</p>
      </div>
    </body>
    </html>
  `;

  try {
    await transporter.sendMail({
      from: `"Kuriftu Resorts" <${process.env.EMAIL_USER}>`,
      to: userEmail,
      subject: "Booking Confirmation - Kuriftu Resorts",
      html: emailTemplate,
    });

    console.log("Booking confirmation email sent successfully to:", userEmail);

    await logAuditEvent({
      action: "EMAIL_SENT",
      resource: "booking_confirmation",
      ip: "system",
      userAgent: "email_service",
      success: true,
    });
  } catch (error) {
    console.error("Error sending booking confirmation email:", error);

    await logAuditEvent({
      action: "EMAIL_FAILED",
      resource: "booking_confirmation",
      ip: "system",
      userAgent: "email_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    throw error;
  }
};

// Email verification functionality
export const sendEmailVerification = async (
  userEmail: string,
  verificationToken: string,
  userName?: string
) => {
  const verificationUrl = `${
    process.env.NEXTAUTH_URL
  }/auth/verify-email?token=${verificationToken}&email=${encodeURIComponent(
    userEmail
  )}`;

  const emailTemplate = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Email Verification - Kuriftu Resorts</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .verification-box { background: white; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea; text-align: center; }
        .verify-button { display: inline-block; padding: 15px 30px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .verify-button:hover { background: #5a6fd8; }
        .footer { text-align: center; margin-top: 30px; padding: 20px; color: #666; font-size: 14px; }
        .security-note { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
        .token-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0; font-family: monospace; word-break: break-all; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🏨 Kuriftu Resorts</h1>
        <h2>Email Verification Required</h2>
      </div>

      <div class="content">
        <p>Dear ${userName || "Valued Guest"},</p>
        <p>Welcome to Kuriftu Resorts! To complete your account setup and ensure the security of your account, please verify your email address.</p>

        <div class="verification-box">
          <h3>🔐 Verify Your Email Address</h3>
          <p>Click the button below to verify your email address and activate your account:</p>
          <a href="${verificationUrl}" class="verify-button">Verify Email Address</a>
        </div>

        <div class="security-note">
          <h4>🛡️ Security Information</h4>
          <p><strong>This verification link will expire in 24 hours</strong> for your security.</p>
          <p>If you didn't create an account with Kuriftu Resorts, please ignore this email.</p>
        </div>

        <p>If the button above doesn't work, you can copy and paste the following link into your browser:</p>
        <div class="token-info">
          ${verificationUrl}
        </div>

        <p>Once verified, you'll be able to:</p>
        <ul>
          <li>✅ Make resort and spa bookings</li>
          <li>✅ Access exclusive member offers</li>
          <li>✅ Leave reviews and testimonials</li>
          <li>✅ Manage your booking history</li>
        </ul>

        <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
      </div>

      <div class="footer">
        <p>Thank you for choosing Kuriftu Resorts!</p>
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; ${new Date().getFullYear()} Kuriftu Resorts. All rights reserved.</p>
      </div>
    </body>
    </html>
  `;

  try {
    await transporter.sendMail({
      from: `"Kuriftu Resorts" <${process.env.EMAIL_USER}>`,
      to: userEmail,
      subject: "Verify Your Email Address - Kuriftu Resorts",
      html: emailTemplate,
    });

    console.log("Email verification sent successfully to:", userEmail);

    await logAuditEvent({
      action: "EMAIL_VERIFICATION_SENT",
      resource: "email_verification",
      ip: "system",
      userAgent: "email_service",
      success: true,
    });
  } catch (error) {
    console.error("Error sending email verification:", error);

    await logAuditEvent({
      action: "EMAIL_VERIFICATION_FAILED",
      resource: "email_verification",
      ip: "system",
      userAgent: "email_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    throw error;
  }
};

// Password reset email functionality
export const sendPasswordResetEmail = async (
  userEmail: string,
  resetToken: string,
  userName?: string
) => {
  const resetUrl = `${
    process.env.NEXTAUTH_URL
  }/auth/reset-password?token=${resetToken}&email=${encodeURIComponent(
    userEmail
  )}`;

  const emailTemplate = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Password Reset - Kuriftu Resorts</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .reset-box { background: white; padding: 25px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545; text-align: center; }
        .reset-button { display: inline-block; padding: 15px 30px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .reset-button:hover { background: #c82333; }
        .footer { text-align: center; margin-top: 30px; padding: 20px; color: #666; font-size: 14px; }
        .security-note { background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545; }
        .token-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 15px 0; font-family: monospace; word-break: break-all; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🏨 Kuriftu Resorts</h1>
        <h2>Password Reset Request</h2>
      </div>

      <div class="content">
        <p>Dear ${userName || "Valued Guest"},</p>
        <p>We received a request to reset your password for your Kuriftu Resorts account.</p>

        <div class="reset-box">
          <h3>🔑 Reset Your Password</h3>
          <p>Click the button below to reset your password:</p>
          <a href="${resetUrl}" class="reset-button">Reset Password</a>
        </div>

        <div class="security-note">
          <h4>🛡️ Security Information</h4>
          <p><strong>This reset link will expire in 1 hour</strong> for your security.</p>
          <p>If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>
        </div>

        <p>If the button above doesn't work, you can copy and paste the following link into your browser:</p>
        <div class="token-info">
          ${resetUrl}
        </div>
      </div>

      <div class="footer">
        <p>Thank you for choosing Kuriftu Resorts!</p>
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; ${new Date().getFullYear()} Kuriftu Resorts. All rights reserved.</p>
      </div>
    </body>
    </html>
  `;

  try {
    await transporter.sendMail({
      from: `"Kuriftu Resorts" <${process.env.EMAIL_USER}>`,
      to: userEmail,
      subject: "Password Reset Request - Kuriftu Resorts",
      html: emailTemplate,
    });

    console.log("Password reset email sent successfully to:", userEmail);

    await logAuditEvent({
      action: "PASSWORD_RESET_EMAIL_SENT",
      resource: "password_reset",
      ip: "system",
      userAgent: "email_service",
      success: true,
    });
  } catch (error) {
    console.error("Error sending password reset email:", error);

    await logAuditEvent({
      action: "PASSWORD_RESET_EMAIL_FAILED",
      resource: "password_reset",
      ip: "system",
      userAgent: "email_service",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    throw error;
  }
};

// Verify email configuration
export async function verifyEmailConfig(): Promise<boolean> {
  try {
    await transporter.verify();
    console.log("✅ Email configuration verified successfully");
    return true;
  } catch (error) {
    console.error("❌ Email configuration verification failed:", error);
    return false;
  }
}
