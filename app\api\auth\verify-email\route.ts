import { NextResponse } from "next/server";
import { verifyEmailToken } from "@/lib/email-verification";
import { logAuditEvent } from "@/lib/security";
import { z } from "zod";

// Validation schema for email verification
const VerifyEmailSchema = z.object({
  email: z.string().email("Invalid email address"),
  token: z.string().min(1, "Token is required"),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    
    // Validate input
    const validation = VerifyEmailSchema.safeParse(body);
    if (!validation.success) {
      await logAuditEvent({
        action: "EMAIL_VERIFICATION_INVALID_INPUT",
        resource: "/api/auth/verify-email",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: validation.error.errors.map(e => e.message).join(", "),
      });

      return NextResponse.json(
        { 
          error: "Invalid input", 
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { email, token } = validation.data;

    // Verify the email token
    const result = await verifyEmailToken(email, token);

    if (!result.success) {
      await logAuditEvent({
        action: "EMAIL_VERIFICATION_FAILED",
        resource: "/api/auth/verify-email",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: result.error || "Unknown error",
      });

      return NextResponse.json(
        { error: result.error || "Verification failed" },
        { status: 400 }
      );
    }

    await logAuditEvent({
      action: "EMAIL_VERIFICATION_SUCCESS",
      resource: "/api/auth/verify-email",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json({
      success: true,
      message: "Email verified successfully",
    });

  } catch (error) {
    console.error("Email verification error:", error);

    await logAuditEvent({
      action: "EMAIL_VERIFICATION_ERROR",
      resource: "/api/auth/verify-email",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET method for handling verification links
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const email = searchParams.get("email");
    const token = searchParams.get("token");

    if (!email || !token) {
      return NextResponse.redirect(
        new URL("/auth/verify-email?error=missing-params", req.url)
      );
    }

    // Validate input
    const validation = VerifyEmailSchema.safeParse({ email, token });
    if (!validation.success) {
      return NextResponse.redirect(
        new URL("/auth/verify-email?error=invalid-params", req.url)
      );
    }

    // Verify the email token
    const result = await verifyEmailToken(email, token);

    if (!result.success) {
      return NextResponse.redirect(
        new URL(`/auth/verify-email?error=${encodeURIComponent(result.error || "verification-failed")}`, req.url)
      );
    }

    // Redirect to success page
    return NextResponse.redirect(
      new URL("/auth/verify-email?success=true", req.url)
    );

  } catch (error) {
    console.error("Email verification GET error:", error);
    return NextResponse.redirect(
      new URL("/auth/verify-email?error=server-error", req.url)
    );
  }
}
