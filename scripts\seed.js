#!/usr/bin/env node

/**
 * Database Seeding Script
 * Populates the database with initial data for development and testing
 */

import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

const sampleResorts = [
  {
    name: "Kuriftu Resort & Spa Debre Zeit",
    slug: "kuriftu-debre-zeit",
    description:
      "A luxurious lakeside resort offering stunning views of Lake Bishoftu. Experience world-class amenities, spa treatments, and fine dining in a serene natural setting.",
    image: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800",
    location: "Debre Zeit, Ethiopia",
    rooms: [
      {
        name: "Deluxe Lake View Room",
        type: "Deluxe",
        price: 250.0,
        image:
          "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=600",
      },
      {
        name: "Executive Suite",
        type: "Suite",
        price: 450.0,
        image:
          "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=600",
      },
      {
        name: "Presidential Villa",
        type: "Villa",
        price: 800.0,
        image:
          "https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=600",
      },
    ],
  },
  {
    name: "Kuriftu Resort & Spa Bahir Dar",
    slug: "kuriftu-bahir-dar",
    description:
      "Nestled on the shores of Lake Tana, this resort combines traditional Ethiopian hospitality with modern luxury. Perfect for exploring the Blue Nile Falls and ancient monasteries.",
    image: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800",
    location: "Bahir Dar, Ethiopia",
    rooms: [
      {
        name: "Standard Room",
        type: "Standard",
        price: 180.0,
        image:
          "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600",
      },
      {
        name: "Lake View Suite",
        type: "Suite",
        price: 380.0,
        image:
          "https://images.unsplash.com/photo-1590490360182-c33d57733427?w=600",
      },
    ],
  },
  {
    name: "Kuriftu Resort & Spa Adama",
    slug: "kuriftu-adama",
    description:
      "A modern resort featuring natural hot springs and therapeutic spa treatments. Ideal for relaxation and wellness retreats in the heart of the Rift Valley.",
    image: "https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=800",
    location: "Adama, Ethiopia",
    rooms: [
      {
        name: "Hot Spring Room",
        type: "Deluxe",
        price: 220.0,
        image:
          "https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=600",
      },
      {
        name: "Wellness Suite",
        type: "Suite",
        price: 420.0,
        image:
          "https://images.unsplash.com/photo-1595576508898-0ad5c879a061?w=600",
      },
    ],
  },
];

const sampleSpaTreatments = [
  {
    name: "Ethiopian Coffee Body Scrub",
    description:
      "Exfoliate and energize your skin with our signature coffee scrub made from locally sourced Ethiopian coffee beans. This treatment removes dead skin cells and improves circulation.",
    price: 85.0,
    duration: 60,
    image: "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=600",
  },
  {
    name: "Honey & Oat Facial",
    description:
      "A nourishing facial treatment using pure Ethiopian honey and organic oats. Perfect for sensitive skin, this treatment hydrates and soothes while providing natural anti-aging benefits.",
    price: 95.0,
    duration: 75,
    image: "https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=600",
  },
  {
    name: "Hot Stone Massage",
    description:
      "Relax and unwind with our therapeutic hot stone massage. Smooth, heated stones are placed on key points of your body to melt away tension and stress.",
    price: 120.0,
    duration: 90,
    image: "https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=600",
  },
  {
    name: "Aromatherapy Massage",
    description:
      "A full-body massage using essential oils derived from local Ethiopian plants. Choose from energizing eucalyptus, calming lavender, or balancing frankincense.",
    price: 100.0,
    duration: 60,
    image: "https://images.unsplash.com/photo-1515377905703-c4788e51af15?w=600",
  },
  {
    name: "Couples Spa Package",
    description:
      "Share a relaxing experience with your loved one. Includes side-by-side massages, access to private relaxation area, and complimentary refreshments.",
    price: 280.0,
    duration: 120,
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600",
  },
  {
    name: "Traditional Ethiopian Mud Wrap",
    description:
      "Detoxify and rejuvenate with our mineral-rich mud wrap using clay from the Ethiopian highlands. This treatment purifies the skin and promotes relaxation.",
    price: 110.0,
    duration: 90,
    image: "https://images.unsplash.com/photo-1596178065887-1198b6148b2b?w=600",
  },
];

const sampleUsers = [
  {
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150",
  },
  {
    name: "Manager User",
    email: "<EMAIL>",
    role: "manager",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
  },
  {
    name: "Receptionist User",
    email: "<EMAIL>",
    role: "receptionist",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150",
  },
  {
    name: "John Doe",
    email: "<EMAIL>",
    role: "user",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",
  },
  {
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "user",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
  },
];

async function seedDatabase() {
  console.log("🌱 Starting database seeding...");

  try {
    // Clear existing data
    console.log("🧹 Clearing existing data...");
    await prisma.booking.deleteMany();
    await prisma.room.deleteMany();
    await prisma.spaTreatment.deleteMany();
    await prisma.resort.deleteMany();
    await prisma.user.deleteMany();

    // Seed users
    console.log("👥 Seeding users...");
    const createdUsers = [];
    for (const userData of sampleUsers) {
      const user = await prisma.user.create({
        data: userData,
      });
      createdUsers.push(user);
      console.log(`   ✅ Created user: ${user.name} (${user.role})`);
    }

    // Seed spa treatments
    console.log("💆 Seeding spa treatments...");
    const createdSpaTreatments = [];
    for (const spaData of sampleSpaTreatments) {
      const spa = await prisma.spaTreatment.create({
        data: spaData,
      });
      createdSpaTreatments.push(spa);
      console.log(`   ✅ Created spa treatment: ${spa.name}`);
    }

    // Seed resorts with rooms
    console.log("🏨 Seeding resorts and rooms...");
    const createdResorts = [];
    for (const resortData of sampleResorts) {
      const { rooms, ...resortInfo } = resortData;

      const resort = await prisma.resort.create({
        data: resortInfo,
      });
      createdResorts.push(resort);
      console.log(`   ✅ Created resort: ${resort.name}`);

      // Create rooms for this resort
      for (const roomData of rooms) {
        const room = await prisma.room.create({
          data: {
            ...roomData,
            resortId: resort.id,
          },
        });
        console.log(`     ✅ Created room: ${room.name}`);
      }
    }

    // Seed sample bookings
    console.log("📅 Seeding sample bookings...");
    const userIds = createdUsers
      .filter((u) => u.role === "user")
      .map((u) => u.email);
    const resortIds = createdResorts.map((r) => r.id);
    const spaIds = createdSpaTreatments.map((s) => s.id);

    // Create some resort bookings
    for (let i = 0; i < 5; i++) {
      const checkIn = new Date();
      checkIn.setDate(checkIn.getDate() + Math.floor(Math.random() * 30) + 1);
      const checkOut = new Date(checkIn);
      checkOut.setDate(checkOut.getDate() + Math.floor(Math.random() * 5) + 1);

      const booking = await prisma.booking.create({
        data: {
          userEmail: userIds[Math.floor(Math.random() * userIds.length)],
          resortId: resortIds[Math.floor(Math.random() * resortIds.length)],
          checkIn,
          checkOut,
          status: ["PENDING", "CONFIRMED", "CANCELLED"][
            Math.floor(Math.random() * 3)
          ],
          notes: `Sample booking ${i + 1} - Resort stay`,
        },
      });
      console.log(`   ✅ Created resort booking: ${booking.id}`);
    }

    // Create some spa bookings
    for (let i = 0; i < 8; i++) {
      const checkIn = new Date();
      checkIn.setDate(checkIn.getDate() + Math.floor(Math.random() * 20) + 1);

      const booking = await prisma.booking.create({
        data: {
          userEmail: userIds[Math.floor(Math.random() * userIds.length)],
          spaId: spaIds[Math.floor(Math.random() * spaIds.length)],
          checkIn,
          status: ["PENDING", "CONFIRMED"][Math.floor(Math.random() * 2)],
          notes: `Sample booking ${i + 1} - Spa treatment`,
        },
      });
      console.log(`   ✅ Created spa booking: ${booking.id}`);
    }

    console.log("\n🎉 Database seeding completed successfully!");
    console.log("\n📊 Summary:");
    console.log(`   Users: ${createdUsers.length}`);
    console.log(`   Resorts: ${createdResorts.length}`);
    console.log(`   Spa Treatments: ${createdSpaTreatments.length}`);
    console.log(`   Sample Bookings: 13`);

    console.log("\n🔐 Test Accounts:");
    console.log("   Admin: <EMAIL>");
    console.log("   Manager: <EMAIL>");
    console.log("   Receptionist: <EMAIL>");
    console.log("   User: <EMAIL>");
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedDatabase().catch((error) => {
    console.error(error);
    process.exit(1);
  });
}

module.exports = { seedDatabase };
