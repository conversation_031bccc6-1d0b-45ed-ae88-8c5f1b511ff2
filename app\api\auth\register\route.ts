import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { createEmailVerificationToken } from "@/lib/email-verification";
import { logAuditEvent } from "@/lib/security";
import { z } from "zod";
import bcrypt from "bcryptjs";

// Validation schema for user registration
const RegisterSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters long"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters long")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one uppercase letter, one lowercase letter, and one number"),
});

// Rate limiting for registration
const registrationRateLimitStore = new Map<string, { count: number; resetTime: number }>();
const REGISTRATION_RATE_LIMIT_WINDOW = 60 * 60 * 1000; // 1 hour
const REGISTRATION_RATE_LIMIT_MAX_ATTEMPTS = 5; // Max 5 registration attempts per hour per IP

function checkRegistrationRateLimit(ip: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const key = `register_${ip}`;
  const record = registrationRateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    registrationRateLimitStore.set(key, {
      count: 1,
      resetTime: now + REGISTRATION_RATE_LIMIT_WINDOW,
    });
    return { allowed: true };
  }

  if (record.count >= REGISTRATION_RATE_LIMIT_MAX_ATTEMPTS) {
    return { allowed: false, resetTime: record.resetTime };
  }

  record.count++;
  registrationRateLimitStore.set(key, record);
  return { allowed: true };
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const ip = req.headers.get("x-forwarded-for") || "unknown";
    const userAgent = req.headers.get("user-agent") || "unknown";
    
    // Check rate limiting
    const rateLimitCheck = checkRegistrationRateLimit(ip);
    if (!rateLimitCheck.allowed) {
      await logAuditEvent({
        action: "REGISTRATION_RATE_LIMITED",
        resource: "/api/auth/register",
        ip,
        userAgent,
        success: false,
        error: "Rate limit exceeded",
      });

      const resetTime = rateLimitCheck.resetTime || Date.now();
      const waitMinutes = Math.ceil((resetTime - Date.now()) / (60 * 1000));

      return NextResponse.json(
        { 
          error: "Too many registration attempts", 
          message: `Please wait ${waitMinutes} minutes before trying again.`,
          retryAfter: resetTime
        },
        { status: 429 }
      );
    }

    // Validate input
    const validation = RegisterSchema.safeParse(body);
    if (!validation.success) {
      await logAuditEvent({
        action: "REGISTRATION_INVALID_INPUT",
        resource: "/api/auth/register",
        ip,
        userAgent,
        success: false,
        error: validation.error.errors.map(e => e.message).join(", "),
      });

      return NextResponse.json(
        { 
          error: "Invalid input", 
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { name, email, password } = validation.data;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
      select: { id: true, emailVerified: true },
    });

    if (existingUser) {
      await logAuditEvent({
        action: "REGISTRATION_EMAIL_EXISTS",
        resource: "/api/auth/register",
        ip,
        userAgent,
        success: false,
        error: "Email already registered",
      });

      if (existingUser.emailVerified) {
        return NextResponse.json(
          { error: "An account with this email already exists" },
          { status: 400 }
        );
      } else {
        // User exists but email not verified - resend verification
        const verificationResult = await createEmailVerificationToken(email, name);
        
        if (verificationResult.success) {
          return NextResponse.json({
            success: true,
            message: "Account exists but email not verified. A new verification email has been sent.",
            requiresVerification: true,
          });
        } else {
          return NextResponse.json(
            { error: "Failed to send verification email" },
            { status: 500 }
          );
        }
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: "user",
        isActive: false, // Will be activated when email is verified
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    // Create and send email verification token
    const verificationResult = await createEmailVerificationToken(email, name);

    if (!verificationResult.success) {
      // If email verification fails, we should still consider registration successful
      // but inform the user about the email issue
      await logAuditEvent({
        action: "REGISTRATION_SUCCESS_EMAIL_FAILED",
        resource: "/api/auth/register",
        ip,
        userAgent,
        success: true,
        error: "Email verification failed to send",
      });

      return NextResponse.json({
        success: true,
        message: "Account created successfully, but verification email failed to send. Please request a new verification email.",
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
        },
        requiresVerification: true,
        emailError: true,
      });
    }

    await logAuditEvent({
      action: "REGISTRATION_SUCCESS",
      resource: "/api/auth/register",
      ip,
      userAgent,
      success: true,
      userId: user.id,
    });

    return NextResponse.json({
      success: true,
      message: "Account created successfully! Please check your email to verify your account.",
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
      requiresVerification: true,
    });

  } catch (error) {
    console.error("Registration error:", error);

    await logAuditEvent({
      action: "REGISTRATION_ERROR",
      resource: "/api/auth/register",
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    });

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
