import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { requireRole } from "@/lib/checkRole";

export async function PUT(
  req: Request,
  { params }: { params: { id: string } }
) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    const data = await req.json();
    
    const offer = await prisma.specialOffer.update({
      where: { id: params.id },
      data,
    });
    return NextResponse.json(offer);
  } catch (error) {
    console.error("Error updating special offer:", error);
    return NextResponse.json(
      { error: "Failed to update special offer" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  // Check authorization
  const authResult = await requireRole(["admin"]);
  if (authResult instanceof NextResponse) {
    return authResult;
  }

  try {
    await prisma.specialOffer.delete({
      where: { id: params.id },
    });
    return NextResponse.json({ message: "Special offer deleted successfully" });
  } catch (error) {
    console.error("Error deleting special offer:", error);
    return NextResponse.json(
      { error: "Failed to delete special offer" },
      { status: 500 }
    );
  }
}

export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const offer = await prisma.specialOffer.findUnique({
      where: { id: params.id },
    });

    if (!offer) {
      return NextResponse.json(
        { error: "Special offer not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(offer);
  } catch (error) {
    console.error("Error fetching special offer:", error);
    return NextResponse.json(
      { error: "Failed to fetch special offer" },
      { status: 500 }
    );
  }
}
